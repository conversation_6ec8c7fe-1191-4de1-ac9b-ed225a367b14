package com.yxt.order.atom.open.sdk.res.es_order;

import com.yxt.order.types.es_order.EsOrderStatus;
import com.yxt.order.types.es_order.EsOrderType;
import com.yxt.order.types.es_order.EsServiceMode;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年08月07日 16:45
 * @email: <EMAIL>
 */
@Data
public class EsOrderResDTO {

  @ApiModelProperty("线上门店 可以获取storeId和StoreName")
  private String onlineStoreCode; // 网点code,线下单没有网点
  @ApiModelProperty("门店编码 可以获取storeId和StoreName")
  private String organizationCode; // 网点单对应的履约门店,会员慢病用这个

  @ApiModelProperty("会员id 可以查会员卡号")
  private String userId;

  @ApiModelProperty("订单类型，ORDER-正单 REFUND-退单")
  private EsOrderType esOrderType;

  @ApiModelProperty("正单号")
  private String orderNo;

  @ApiModelProperty("退单号")
  private String refundNo;

  @ApiModelProperty("服务模式 O2O,B2C,POS")
  private EsServiceMode serviceMode;

  @ApiModelProperty("订单状态 DONE-已完成")
  private EsOrderStatus esOrderStatus;

  @ApiModelProperty("支付时间")
  private Date payTime;

  @ApiModelProperty("三方正单号")
  private String thirdOrderNo;

  @ApiModelProperty("三方退单号")
  private String thirdRefundNo;

  @ApiModelProperty("平台编码")
  private String platformCode;

  @ApiModelProperty("创建时间")
  // 交易日期==订单的创建时间
  private Date createTime;

  @ApiModelProperty("完成时间")
  private Date completeTime;

  @ApiModelProperty("明细")
  private List<EsOrderItem> esOrderItemList;

  @Data
  public static class EsOrderItem {

    @ApiModelProperty("商品编码")
    private String commodityCode;

    @ApiModelProperty("商品名称")
    private String commodityName;

    @ApiModelProperty("商品数量")
    private BigDecimal commodityCount;

    @ApiModelProperty("商品总实付金额")
    private BigDecimal actualAmount;

    @ApiModelProperty("商品图片")
    private String mainPic;

    @ApiModelProperty("五级分类")
    private String fiveClass;

  }

}
