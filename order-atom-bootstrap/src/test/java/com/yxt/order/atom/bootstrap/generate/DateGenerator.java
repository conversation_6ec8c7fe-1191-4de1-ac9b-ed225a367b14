package com.yxt.order.atom.bootstrap.generate;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

public class DateGenerator {
    public static void main(String[] args) {
        // Create formatter for "yyMM" format
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyMM");
        
        // Set start and end dates
        LocalDate startDate = LocalDate.of(2009, 1, 1);
        LocalDate endDate = LocalDate.of(2024, 5, 1);
        
        // Generate date strings
        List<String> dateStrings = generateDateStrings(startDate, endDate, formatter);
        
        // Print the results
        for (String dateString : dateStrings) {
            System.out.println(dateString);
        }
        
        // Print the total count
        System.out.println("Total dates generated: " + dateStrings.size());
    }
    
    /**
     * Generates a list of date strings in the specified format from start to end date
     */
    public static List<String> generateDateStrings(LocalDate startDate, LocalDate endDate, DateTimeFormatter formatter) {
        List<String> result = new ArrayList<>();
        
        LocalDate currentDate = startDate;
        while (!currentDate.isAfter(endDate)) {
            // Only add a new entry when the month changes
            String formattedDate = currentDate.format(formatter);
            
            // Avoid duplicates by checking if we've already added this month
            if (result.isEmpty() || !result.get(result.size() - 1).equals(formattedDate)) {
                result.add(formattedDate);
            }
            
            // Move to the next month
            currentDate = currentDate.plusMonths(1);
        }
        
        return result;
    }
}