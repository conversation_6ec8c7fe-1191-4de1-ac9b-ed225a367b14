{
  "core": {
    "transport" : {
      "channel": {
        "speed": {
          "channel": ${channel},
          "byte":  ${byte},
          "record": ${record}
        }
      }
    }
  },
  "job": {
    "setting": {
      "speed": {
        "channel": ${channel},
        "byte":  ${byte},
        "record": ${record}
      }
    },
    "content": [
      {
        "reader": {
          "name": "rdbmsreader",
          "parameter": {
            "username": "${hanaUsername}",
            "password": "${hanaPassword}",
            "connection": [
              {
                "querySql": [
                  "select ID,LOGINID,PASSWORD,LASTNAME,SEX,BIRTHDAY,NATIONALITY,SYSTEMLANGUAGE,MARITALSTATUS,T<PERSON><PERSON>H<PERSON><PERSON>,<PERSON><PERSON><PERSON>LE,<PERSON><PERSON><PERSON><PERSON>CALL,EMAIL,LOCATIONID,WORKROOM,HOMEADDRESS,RESOURCETYPE,STARTDATE,ENDDATE,JOBTITLE,JOBACTIVITYDESC,JOBLEVEL,SEC<PERSON><PERSON><PERSON>,DEPA<PERSON><PERSON>NT<PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON>N<PERSON>ID1,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON>STAN<PERSON>D,BAN<PERSON>ID1,<PERSON><PERSON><PERSON><PERSON>D1,RES<PERSON><PERSON><PERSON><PERSON>AGEI<PERSON>,CREATERID,CREATEDATE,LASTMODID,LASTMODDATE,LASTLOGINDATE,DATEFIELD1,DATEFIELD2,DATEFIELD3,DATEFIELD4,DATEFIELD5,NUMBERFIELD1,NUMBERFIELD2,NUMBERFIELD3,NUMBERFIELD4,NUMBERFIELD5,TEXTFIELD1,TEXTFIELD2,TEXTFIELD3,TEXTFIELD4,TEXTFIELD5,TINYINTFIELD1,TINYINTFIELD2,TINYINTFIELD3,TINYINTFIELD4,TINYINTFIELD5,CERTIFICATENUM,NATIVEPLACE,EDUCATIONLEVEL,BEMEMBERDATE,BEPARTYDATE,WORKCODE,REGRESIDENTPLACE,HEALTHINFO,RESIDENTPLACE,POLICY,DEGREE,HEIGHT,USEKIND,JOBCALL,ACCUMFUNDACCOUNT,BIRTHPLACE,FOLK,RESIDENTPHONE,RESIDENTPOSTCODE,EXTPHONE,MANAGERSTR,STATUS,FAX,ISLABOUUNION,WEIGHT,TEMPRESIDENTNUMBER,PROBATIONENDDATE,COUNTRYID,PASSWDCHGDATE,NEEDUSB,SERIAL,ACCOUNT,LLOGINID,NEEDDYNAPASS,DSPORDER,PASSWORDSTATE,ACCOUNTTYPE,BELONGTO,DACTYLOGRAM,ASSISTANTDACTYLOGRAM,PASSWORDLOCK,SUMPASSWORDWRONG,OLDPASSWORD1,OLDPASSWORD2,MSGSTYLE,MESSAGERURL,PINYINLASTNAME,TOKENKEY,USERUSBTYPE,BEGDA,ENDDA,AEDTM,BUKRS,KOSTL,ORGEH,WERKS,NAME1,PERSG,PTEXT,PERSK,PTEXT1,BTRTL,BTRTL1,ABKRS,ABKTX,INSTI,NMF01,NMF02,PLANS,STEXT,ISCORRECTED,ISINSERT,ADSJGS,ADGS,ADBM,ZZHRPOSTE,LTEXT,HXOA_INSERTTIME,HXOA_UPDATETIME from HANA_APP.HRMRESOURCE WHERE LOGINID IN (${fixLoginId}) "
                ],
                "jdbcUrl": [
                  "${hanaUrl}"
                ]
              }
            ],
            "fetchSize": ${fetchSize}
          }
        },
        "writer": {
          "name": "mysqlwriter",
          "parameter": {
            "username": "${mySqlUsername}",
            "password": "${mySqlPassword}",
            "column": [
              "ID",
              "LOGINID",
              "PASSWORD",
              "LASTNAME",
              "SEX",
              "BIRTHDAY",
              "NATIONALITY",
              "SYSTEMLANGUAGE",
              "MARITALSTATUS",
              "TELEPHONE",
              "MOBILE",
              "MOBILECALL",
              "EMAIL",
              "LOCATIONID",
              "WORKROOM",
              "HOMEADDRESS",
              "RESOURCETYPE",
              "STARTDATE",
              "ENDDATE",
              "JOBTITLE",
              "JOBACTIVITYDESC",
              "JOBLEVEL",
              "SECLEVEL",
              "DEPARTMENTID",
              "SUBCOMPANYID1",
              "COSTCENTERID",
              "MANAGERID",
              "ASSISTANTID",
              "BANKID1",
              "ACCOUNTID1",
              "RESOURCEIMAGEID",
              "CREATERID",
              "CREATEDATE",
              "LASTMODID",
              "LASTMODDATE",
              "LASTLOGINDATE",
              "DATEFIELD1",
              "DATEFIELD2",
              "DATEFIELD3",
              "DATEFIELD4",
              "DATEFIELD5",
              "NUMBERFIELD1",
              "NUMBERFIELD2",
              "NUMBERFIELD3",
              "NUMBERFIELD4",
              "NUMBERFIELD5",
              "TEXTFIELD1",
              "TEXTFIELD2",
              "TEXTFIELD3",
              "TEXTFIELD4",
              "TEXTFIELD5",
              "TINYINTFIELD1",
              "TINYINTFIELD2",
              "TINYINTFIELD3",
              "TINYINTFIELD4",
              "TINYINTFIELD5",
              "CERTIFICATENUM",
              "NATIVEPLACE",
              "EDUCATIONLEVEL",
              "BEMEMBERDATE",
              "BEPARTYDATE",
              "WORKCODE",
              "REGRESIDENTPLACE",
              "HEALTHINFO",
              "RESIDENTPLACE",
              "POLICY",
              "DEGREE",
              "HEIGHT",
              "USEKIND",
              "JOBCALL",
              "ACCUMFUNDACCOUNT",
              "BIRTHPLACE",
              "FOLK",
              "RESIDENTPHONE",
              "RESIDENTPOSTCODE",
              "EXTPHONE",
              "MANAGERSTR",
              "STATUS",
              "FAX",
              "ISLABOUUNION",
              "WEIGHT",
              "TEMPRESIDENTNUMBER",
              "PROBATIONENDDATE",
              "COUNTRYID",
              "PASSWDCHGDATE",
              "NEEDUSB",
              "SERIAL",
              "ACCOUNT",
              "LLOGINID",
              "NEEDDYNAPASS",
              "DSPORDER",
              "PASSWORDSTATE",
              "ACCOUNTTYPE",
              "BELONGTO",
              "DACTYLOGRAM",
              "ASSISTANTDACTYLOGRAM",
              "PASSWORDLOCK",
              "SUMPASSWORDWRONG",
              "OLDPASSWORD1",
              "OLDPASSWORD2",
              "MSGSTYLE",
              "MESSAGERURL",
              "PINYINLASTNAME",
              "TOKENKEY",
              "USERUSBTYPE",
              "BEGDA",
              "ENDDA",
              "AEDTM",
              "BUKRS",
              "KOSTL",
              "ORGEH",
              "WERKS",
              "NAME1",
              "PERSG",
              "PTEXT",
              "PERSK",
              "PTEXT1",
              "BTRTL",
              "BTRTL1",
              "ABKRS",
              "ABKTX",
              "INSTI",
              "NMF01",
              "NMF02",
              "PLANS",
              "STEXT",
              "ISCORRECTED",
              "ISINSERT",
              "ADSJGS",
              "ADGS",
              "ADBM",
              "ZZHRPOSTE",
              "LTEXT",
              "HXOA_INSERTTIME",
              "HXOA_UPDATETIME"
            ],
            "connection": [
              {
                "table": [
                  "hrmresource"
                ],
                "jdbcUrl": "${mySqlUrl}"
              }
            ],
            "batchSize": ${batchSize}
          }
        }
      }
    ]
  }
}
