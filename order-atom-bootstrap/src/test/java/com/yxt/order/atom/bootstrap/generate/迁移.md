从归档库迁移数据

1. hana --datax--> archiveDatabase 从hana迁移到归档库
2. archiveDatabase --> offlineOrder 从归档库迁移到下线单库


迁移步骤:
- [x] 迁移订单归属信息 HX_CRM.CV_STOREINFOR
- [x] 迁移会员信息HX_CRM.TB_CRM_MATSTER_N
- [x] 迁移商品信息 HANA_D.MAKT
- [x] 迁移支付描述 HX_SJCJ.TENDTYT
- [x] 迁移公司员工表 HANA_APP.HRMRESOURCE
- [ ] 同步2024.06.22至2024.06.24 00:00:00
  - [x] CDHX_USERS
  - [ ] 测试迁移脚本,注意数量和数据的正确性
- [ ] 自测迁移脚本
- [ ] 支持断点续传
- [ ] 程序化生成执行脚本,不手动修改

-- 执行脚本目录
cd /home/<USER>/datax/datax/job

cp -r /mnt/d/software/datax/datax/job/hana_cv_storeinfor.json . && nohup python3 ../bin/datax.py hana_cv_storeinfor.json > /home/<USER>/datax/datax/job/migration_log/hana_cv_storeinfor.log 2>&1 &
cp -r /mnt/d/software/datax/datax/job/hana_tb_crm_matster_n.json . && nohup python3 ../bin/datax.py hana_tb_crm_matster_n.json > /home/<USER>/datax/datax/job/migration_log/hana_tb_crm_matster_n.log 2>&1 &
cp -r /mnt/d/software/datax/datax/job/hana_d_makt.json . && nohup python3 ../bin/datax.py hana_d_makt.json > /home/<USER>/datax/datax/job/migration_log/hana_d_makt.log 2>&1 &
cp -r /mnt/d/software/datax/datax/job/hana_tendyty.json . && nohup python3 ../bin/datax.py hana_tendyty.json > /home/<USER>/datax/datax/job/migration_log/hana_tendyty.log 2>&1 &
cp -r /mnt/d/software/datax/datax/job/hana_hrmresource.json . && nohup python3 ../bin/datax.py hana_hrmresource.json > /home/<USER>/datax/datax/job/migration_log/hana_hrmresource.log 2>&1 &


-- 同步拆单场景的表
cp -r /mnt/d/software/datax/datax/job/hana_xf_dbm_coupon.json . && nohup python3 ../bin/datax.py hana_xf_dbm_coupon.json > /home/<USER>/datax/datax/job/migration_log/hana_xf_dbm_coupon.log 2>&1 &
cp -r /mnt/d/software/datax/datax/job/hana_xf_zt_giftgiving_memomap.json . && nohup python3 ../bin/datax.py hana_xf_zt_giftgiving_memomap.json > /home/<USER>/datax/datax/job/migration_log/hana_xf_zt_giftgiving_memomap.log 2>&1 &


-- 同步订单信息
cp -r /mnt/d/software/datax/datax/job/hana_xf_transsalestotal.json . && nohup python3 ../bin/datax.py hana_xf_transsalestotal.json > /home/<USER>/datax/datax/job/migration_log/hana_xf_transsalestotal.log 2>&1 &
cp -r /mnt/d/software/datax/datax/job/hana_xf_transsalesitem.json . && nohup python3 ../bin/datax.py hana_xf_transsalesitem.json > /home/<USER>/datax/datax/job/migration_log/hana_xf_transsalesitem.log 2>&1 &
cp -r /mnt/d/software/datax/datax/job/hana_xf_transsalestender.json . && nohup python3 ../bin/datax.py hana_xf_transsalestender.json > /home/<USER>/datax/datax/job/migration_log/hana_xf_transsalestender.log 2>&1 &




-- tail
cp -r /mnt/d/software/datax/datax/job/hana_cv_storeinfor.json . && nohup python3 ../bin/datax.py hana_cv_storeinfor.json > /home/<USER>/datax/datax/job/migration_log/hana_cv_storeinfor.log 2>&1 &
tail -f /home/<USER>/datax/datax/job/migration_log/hana_cv_storeinfor.log


tail -f /home/<USER>/datax/datax/job/migration_log/hana_tendyty.log
tail -f /home/<USER>/datax/datax/job/migration_log/hana_hrmresource.log
tail -f /home/<USER>/datax/datax/job/migration_log/hana_d_makt.log
tail -f /home/<USER>/datax/datax/job/migration_log/hana_xf_dbm_coupon.log
tail -f /home/<USER>/datax/datax/job/migration_log/hana_xf_zt_giftgiving_memomap.log
tail -f /home/<USER>/datax/datax/job/migration_log/hana_xf_transsalestotal.log
tail -f /home/<USER>/datax/datax/job/migration_log/hana_xf_transsalesitem.log
tail -f /home/<USER>/datax/datax/job/migration_log/hana_xf_transsalestender.log
tail -f /home/<USER>/datax/datax/job/migration_log/hana_xf_transsalestotal.log
tail -f /home/<USER>/datax/datax/job/migration_log/hana_xf_transsalesitem.log
tail -f /home/<USER>/datax/datax/job/migration_log/hana_xf_transsalestender.log
tail -f /home/<USER>/datax/datax/job/migration_log/hana_tb_crm_matster_n.log



cp -r /mnt/d/software/datax/datax/job/hana_tb_crm_matster_n.json . && nohup python3 ../bin/datax.py hana_tb_crm_matster_n.json > /home/<USER>/datax/datax/job/migration_log/hana_tb_crm_matster_n.log 2>&1 &
tail -f /home/<USER>/datax/datax/job/migration_log/hana_tb_crm_matster_n.log



select count(1) from tb_crm_matster_n;
select count(1) from makt;
select count(1) from xf_transsalesitem_cdhx_users;

TRUNCATE tb_crm_matster_n;
TRUNCATE makt;
TRUNCATE xf_transsalesitem_cdhx_users;




===========
-- history
cp -r /mnt/d/software/datax/datax/job/hana.json . && python3 ../bin/datax.py hana.json
100  cp -r /mnt/d/software/datax/datax/job/hana.json . && python3 ../bin/datax.py hana.json
105  cp -r /mnt/d/software/datax/datax/job/hana_xf_transsalestotal.json . && python3 ../bin/datax.py hana_xf_transsalestotal.json
106  cp -r /mnt/d/software/datax/datax/job/hana_xf_transsalesitem.json . && python3 ../bin/datax.py hana_xf_transsalesitem.json
107  cp -r /mnt/d/software/datax/datax/job/hana_xf_transsalestender . && python3 ../bin/datax.py hana_xf_transsalestender.json
108  cp -r /mnt/d/software/datax/datax/job/hana_xf_transsalestender.json . && python3 ../bin/datax.py hana_xf_transsalestender.json
111  cp -r /mnt/d/software/datax/datax/job/hana_cv_storeinfor.json . && python3 ../bin/datax.py hana_cv_storeinfor.json
112  cp -r /mnt/d/software/datax/datax/job/hana_tb_crm_matster_n.json . && python3 ../bin/datax.py hana_tb_crm_matster_n.json
113  nohup python3 ../bin/datax.py hana_tb_crm_matster_n.json > /migration/datax/logfile.log 2>&1 &
114  nohup python3 ../bin/datax.py hana_tb_crm_matster_n.json > ~/logfile.log 2>&1 &
117  nohup python3 ../bin/datax.py hana_tb_crm_matster_n.json > ~/logfile2.log 2>&1 &
132  cp -r /mnt/d/software/datax/datax/job/hana_d_makt.json . && python3 ../bin/datax.py hana_d_makt.json

145  cp -r /mnt/d/software/datax/datax/job/hana_cv_storeinfor.json . && python3 ../bin/datax.py hana_cv_storeinfor.json
146  cp -r /mnt/d/software/datax/datax/job/hana_d_makt.json . && python3 ../bin/datax.py hana_d_makt.json
147  cp -r /mnt/d/software/datax/datax/job/hana_tb_crm_matster_n.json . && python3 ../bin/datax.py hana_tb_crm_matster_n.json
148  cp -r /mnt/d/software/datax/datax/job/hana_xf_transsalesitem.json . && python3 ../bin/datax.py hana_xf_transsalesitem.json
149  cp -r /mnt/d/software/datax/datax/job/hana_xf_transsalestender.json . && python3 ../bin/datax.py hana_xf_transsalestender.json
150  cp -r /mnt/d/software/datax/datax/job/hana_xf_transsalestotal.json . && python3 ../bin/datax.py hana_xf_transsalestotal.json
151  cp -r /mnt/d/software/datax/datax/job/hana_tb_crm_matster_n.json . && python3 ../bin/datax.py hana_tb_crm_matster_n.json
152  cp -r /mnt/d/software/datax/datax/job/hana_d_makt.json . && python3 ../bin/datax.py hana_d_makt.json
