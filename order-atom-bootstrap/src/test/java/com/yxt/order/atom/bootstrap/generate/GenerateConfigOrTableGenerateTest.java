package com.yxt.order.atom.bootstrap.generate;

import static com.yxt.order.atom.bootstrap.migration.Constant.ALL_SCHEMA;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import com.google.common.collect.Lists;
import com.yxt.order.atom.bootstrap.generate.GenerateDataXConfig.Config;
import com.yxt.order.atom.bootstrap.generate.GenerateDataXConfig.HanaDataSource;
import com.yxt.order.atom.bootstrap.generate.GenerateDataXConfig.SyncDataToMySQLSource;
import com.yxt.order.atom.bootstrap.generate.fix.FixMemberMigration;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.charset.Charset;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.jetbrains.annotations.NotNull;
import org.junit.Assert;
import org.junit.Test;

public class GenerateConfigOrTableGenerateTest {

  /**
   * 生产年的Schema
   */
  @Test
  public void testRangeSchemaTableNameYear() {
    rangeSchemaTableNameYear("CQHX_USERS", 2020, 2024);
  }

  /**
   * 生成季度的Schema
   */
  @Test
  public void testRangeSchemaTableNameQuarter() {
    rangeSchemaQuarter("CQHX_USERS", 2020, 2024);
  }

  /**
   * 生成月的Schema
   */
  @Test
  public void testRangeSchemaTableNameMonth() {
    rangeSchemaMonth("CQHX_USERS", 2020, 2024);
  }

  private ArrayList<String> rangeSchemaMonth(String schema, int startYear, int endYear) {
    Assert.assertTrue("最大年份不能超过2024", endYear <= 2024);

    ArrayList<String> list = Lists.newArrayList();

    for (int year = startYear; year <= endYear; year++) {
      for (int month = 1; month <= 12; month++) {
        String format = String.format("%s_%s_%s", schema, year, month);
        System.out.println(format);
        list.add(format);
      }
    }
    return list;
  }

  private ArrayList<String> growthSchema(String schema) {
    return Lists.newArrayList(schema + "_"
        + "20241007_2025011512"); // 对应的时间点 XF_CREATETIME >= '2024-10-07 00:00:00' AND XF_CREATETIME <= '2025-01-15 12:00:00'
  }


  /**
   * 按月生成where条件
   */
  @Test
  public void monthGenerate() {
    int startYear = 2021;
    int endYear = 2024;

    for (int year = startYear; year <= endYear; year++) {
      for (int month = 1; month <= 12; month++) {
        String sqlCondition = generateMonthSQLCondition(year, month);
        System.out.println(sqlCondition);
      }
    }

  }

  public static String generateMonthSQLCondition(int year, int month) {
    String startDate = String.format("%d-%02d-01 00:00:00", year, month);
    String endDate;

    if (month == 12) {
      endDate = String.format("%d-01-01 00:00:00", year + 1);
    } else {
      endDate = String.format("%d-%02d-01 00:00:00", year, month + 1);
    }

    // 特殊处理10月份
    if (year == 2024 && month == 10) {
      endDate = String.format("%d-10-07 00:00:00", year);
      return "XF_CREATETIME >= '" + startDate + "' AND XF_CREATETIME <= '" + endDate + "'";
    }

    // 如果当前月份已经超过10月，则不生成SQL条件
    if (year == 2024 && month > 10) {
      return "-- " + year + "年" + month + "月份不在统计范围内";
    }

    return "XF_CREATETIME >= '" + startDate + "' AND XF_CREATETIME < '" + endDate + "'";
  }

  @Test
  public void yearGenerateYear() {
    rangeYear(2020, 2024);
  }

  @Test
  public void yearGenerateQuarter() {
    rangeQuarter(2020, 2024);
  }

  public void rangeQuarter(Integer startYear, Integer endYear) {
    Assert.assertTrue("最大年份不能超过2024", endYear <= 2024);

    for (int year = startYear; year <= endYear; year++) {
      for (int quarter = 1; quarter <= 4; quarter++) {
        String sqlCondition = generateQuarterSQLCondition(year, quarter);
//        System.out.println(sqlCondition);

        System.out.println(String.format("debug: %s_Q%s sql: %s", year, quarter, sqlCondition));

      }
    }
  }


  public List<String> rangeSchemaQuarter(String schema, Integer startYear, Integer endYear) {
    Assert.assertTrue("最大年份不能超过2024", endYear <= 2024);

    ArrayList<String> list = Lists.newArrayList();

    for (int year = startYear; year <= endYear; year++) {
      for (int quarter = 1; quarter <= 4; quarter++) {
        String format = String.format("%s_%s_Q%s", schema, year, quarter);
        System.out.println(format);
        list.add(format);
      }
    }
    return list;
  }

  public static String generateQuarterSQLCondition(int year, int quarter) {
    String startDate, endDate;
    switch (quarter) {
      case 1:
        startDate = year + "-01-01 00:00:00";
        endDate = year + "-04-01 00:00:00";
        break;
      case 2:
        startDate = year + "-04-01 00:00:00";
        endDate = year + "-07-01 00:00:00";
        break;
      case 3:
        startDate = year + "-07-01 00:00:00";
        endDate = year + "-10-01 00:00:00";
        break;
      case 4:
        startDate = year + "-10-01 00:00:00";
        endDate = (year + 1) + "-01-01 00:00:00";
        if (year == 2024) {
          endDate = year + "-10-07 00:00:00";
          return "XF_CREATETIME >= '" + startDate + "' AND XF_CREATETIME <= '" + endDate + "'";
        }
        break;
      default:
        throw new IllegalArgumentException("无效的季度: " + quarter);
    }
    return "XF_CREATETIME >= '" + startDate + "' AND XF_CREATETIME < '" + endDate + "'";
  }

  public void rangeYear(Integer startYear, Integer endYear) {
    Assert.assertTrue("最大年份不能超过2024", endYear <= 2024);

    for (int year = startYear; year <= endYear; year++) {
      String sqlCondition = generateSQLCondition(year);
      System.out.println(sqlCondition);
    }
  }

  public List<String> rangeSchemaTableNameYear(String schema, Integer startYear, Integer endYear) {
//    Assert.assertTrue("最大年份不能超过2024", endYear <= 2024);
    List<String> list = Lists.newArrayList();
    for (int year = startYear; year <= endYear; year++) {
      String format = String.format("%s_%s", schema, year);
      System.out.println(format);
      list.add(format);
    }
    return list;
  }

  public String generateSQLCondition(int year) {
    if (year == 2024) {
      return "XF_CREATETIME >= '" + year
          + "-01-01 00:00:00' AND XF_CREATETIME <= '2024-10-07 00:00:00'";
    }

    return "XF_CREATETIME >= '" + year + "-01-01 00:00:00' AND XF_CREATETIME < '" + (year + 1)
        + "-01-01 00:00:00'";
  }


  @Test
  @Deprecated
  public void testGenerateConfig() throws Exception {
    Boolean fixUseIdList = Boolean.FALSE;
    Boolean fixLoginIdList = Boolean.FALSE;
    Boolean fixPayInfoList = Boolean.FALSE;
    Boolean fixItemList = Boolean.FALSE;

    String base = "D:\\github\\codes\\order-atom-service\\order-atom-bootstrap\\src\\test\\java\\com\\yxt\\order\\atom\\bootstrap\\generate\\template";
    String folderPath = base;
    String newFolderPath = base + "-" + DateUtil.format(new Date(), "yyyyMMddHHmmss");
    Map<String, String> map = readJsonFiles(folderPath, ".json");

    String s = "YNHX_DATA01\n"
        + "GXHX_USERS\n"
        + "GZHX_USERS\n"
        + "SCHX_USERS\n"
        + "SXHX_USERS\n"
        + "CQHX_USERS\n"
        + "CDHX_USERS\n"
        + "SHHX_DATA01\n"
        + "TJHX_DATA01\n"
        + "HNHX_DATA01\n"
        + "HENHX_DATA01\n"
        + "SXGSHX_DATA01\n"
        + "TJQCHX_DATA01\n"
        + "HENNYHX_DATA01\n"
        + "ZYHX_USERS";
    String[] schemaList = s.split("\n");
    // YNHX_DATA01
//    ArrayList<String> schemaList = Lists.newArrayList("ZYHX_USERS");
    //替换模板变量
    for (String schema : schemaList) {
      map.forEach((fileName, content) -> {
        content = content
            .replace("${schema}", schema)
            .replace("${salesTotalWhereSql}", GenerateDataXConfig.salesTotalWhereSql)
            .replace("${salesItemWhereSql}", GenerateDataXConfig.salesItemWhereSql)
            .replace("${salesTenderWhereSql}", GenerateDataXConfig.salesTenderWhereSql)
            .replace("${hanaUrl}", HanaDataSource.hanaUrl)
            .replace("${hanaUsername}", HanaDataSource.hanaUsername)
            .replace("${hanaPassword}", HanaDataSource.hanaPassword)
            .replace("${mySqlUrl}", SyncDataToMySQLSource.mySqlUrl)
            .replace("${mySqlUsername}", SyncDataToMySQLSource.mySqlUsername)
            .replace("${mySqlPassword}", SyncDataToMySQLSource.mySqlPassword)
            .replace("${fetchSize}", Config.fetchSize)
            .replace("${batchSize}", Config.batchSize)
            .replace("${channel}", Config.channel)
            .replace("${byte}", Config.bytes)
            .replace("${record}", Config.record)
        ;

        if (fixUseIdList && content.contains("${fixUserIdList}")) {
          content = content
              .replace("${fixUserIdList}", FixMemberMigration.extractedUserIdLIst());
        }

        if (fixLoginIdList && content.contains("${fixLoginId}")) {
          content = content
              .replace("${fixLoginId}", FixMemberMigration.extractedLoginIdLIst());
        }
        if (fixPayInfoList && content.contains("${fixtenderSql}")) {
          content = content
              .replace("${fixtenderSql}", FixMemberMigration.extractedPayLIst());
        }
        if (fixItemList && content.contains("${fixItemSql}")) {
          content = content
              .replace("${fixItemSql}", FixMemberMigration.extractedItemLIst());
        }

        writeToFile(newFolderPath, schema + "_" + fileName, content);
      });
    }

    System.out.println(map);
  }
  private ArrayList<String> xfTxBatch(String schema) {
    return Lists.newArrayList(schema + "_"
        + "txBatch"); // 非S0开头的订单需要查XF_TXBATCH,在之前的归档库总是被转成科学技术法了,这里单独处理
  }

  @Test
  public void testGenerateConfigOneSQLFile() throws Exception {

    String base = "D:\\github\\codes\\order-atom-service\\order-atom-bootstrap\\src\\test\\java\\com\\yxt\\order\\atom\\bootstrap\\generate\\template";
    String folderPath = base;
    String newFolderPath = base + "-" + DateUtil.format(new Date(), "yyyyMMddHHmmss");
    Map<String, String> map = readJsonFiles(folderPath, ".sql");

    String[] schemaCompanyArray = ALL_SCHEMA.split("\n");
    StringBuilder oneSQL = new StringBuilder();
    for (String schemaCompany : schemaCompanyArray) {
//      List<String> schemaList = growthSchema(schemaCompany);
      List<String> schemaList = xfTxBatch(schemaCompany);

      //替换模板变量
      for (String schema : schemaList) {
        map.forEach((fileName, content) -> {
          content = content
              .replace("${schema}", schema)
          ;

// 非oneSQL file
//        writeToFile(newFolderPath, schema + "_" + fileName, content);

          oneSQL.append(content);
          oneSQL.append("\n");
        });
      }
    }
    writeToFile(newFolderPath,   "growthData.sql", oneSQL.toString());

//    System.out.println(map);

    // 按照Schema
//    String s = "YNHX_DATA01\n"
//        + "GXHX_USERS\n"
//        + "GZHX_USERS\n"
//        + "SCHX_USERS\n"
//        + "SXHX_USERS\n"
//        + "CQHX_USERS\n"
//        + "CDHX_USERS\n"
//        + "SHHX_DATA01\n"
//        + "TJHX_DATA01\n"
//        + "HNHX_DATA01\n"
//        + "HENHX_DATA01\n"
//        + "SXGSHX_DATA01\n"
//        + "TJQCHX_DATA01\n"
//        + "HENNYHX_DATA01\n"
//        + "ZYHX_USERS";
//    String[] schemaList = s.split("\n");

    // 按照年月、季度
//    String schemaCompany = "CQHX_USERS";
//    List<String> schemaList = rangeSchemaTableNameYear(schemaCompany, 2018, 2024);

//    String schemaCompany = "SXHX_USERS";
//    List<String> schemaList = rangeSchemaTableNameYear(schemaCompany, 2020, 2024);

//    String schemaCompany = "SCHX_USERS";
//    List<String> schemaList = rangeSchemaTableNameYear(schemaCompany, 2018, 2024);

//    String schemaCompany = "GZHX_USERS";
//    List<String> schemaList = rangeSchemaTableNameYear(schemaCompany, 2018, 2024);

//    String schemaCompany = "GXHX_USERS";
//    List<String> schemaList = rangeSchemaQuarter(schemaCompany, 2020, 2024);

    // YNHX_DATA01 按季度
//    String schemaCompany = "YNHX_DATA01";
//    List<String> schemaList = rangeSchemaQuarter(schemaCompany, 2021, 2024);

//        String schemaCompany = "HNHX_DATA01";
//    List<String> schemaList = rangeSchemaTableNameYear(schemaCompany, 2019, 2024);

//    String schemaCompany = "CDHX_USERS";
//    List<String> schemaList = rangeSchemaQuarter(schemaCompany, 2020, 2024);

//        String schemaCompany = "补充索引";
//    List<String> schemaList = Lists.newArrayList(
//        "ZYHX_USERS",
//        "HENNYHX_DATA01",
//        "TJQCHX_DATA01",
//        "SXGSHX_DATA01",
//        "HENHX_DATA01",
//        "TJHX_DATA01",
//        "SHHX_DATA01"
//    );

//    String schemaCompany = "YNHX_DATA01";

  }

  public static void writeToFile(String folderPath, String fileName, String content) {
    File folder = new File(folderPath);

    // 如果文件夹不存在，创建文件夹
    if (!folder.exists()) {
      folder.mkdirs();
    }

    File file = new File(folder, fileName);

    try (FileWriter writer = new FileWriter(file)) {
      writer.write(content);
      System.out.println("内容已成功写入文件：" + file.getAbsolutePath());
    } catch (IOException e) {
      System.out.println("写入文件时发生错误：" + e.getMessage());
    }
  }

  public Map<String, String> readJsonFiles(String folderPath, String suffix) throws Exception {

    Path dir = Paths.get(folderPath);
    List<File> sqlFiles = getFiles(dir, suffix);
    List<File> allFiles = new ArrayList<>();
    allFiles.addAll(sqlFiles);

    Map<String, String> map = new HashMap<>();
    for (File file : allFiles) {
      String s = FileUtil.readString(file, Charset.defaultCharset());
      map.put(file.getName(), s);
    }

    return map;
  }

  @NotNull
  private static List<File> getFiles(Path dir, String fileType) throws IOException {
    List<File> jsonFiles = Files.walk(dir)
        .filter(path -> path.toString().endsWith(fileType))
        // 手动排查特殊的先
        .filter(path -> !path.toString().contains("XF_DBM_COUPON.sql"))
        .filter(path -> !path.toString().contains("XF_ZT_GIFTGIVING_MEMOMAP.sql"))
        .filter(path -> !path.toString().contains("XF_TRANSSALESTOTAL_addCreateTimeMigrationIndex"))
        .filter(path -> !path.toString().contains("XF_TRANSSALESITEM.sql"))
        .filter(path -> !path.toString().contains("XF_TRANSSALESTENDER.sql"))
        .map(Path::toFile)
        .collect(Collectors.toList());
    return jsonFiles;
  }

  @Test
  public void testGenerateAddColumnTest() {
    ArrayList<String> strings = Lists.newArrayList(
        "SHHX_DATA01",
        "TJHX_DATA01",
        "HENHX_DATA01",
        "SXGSHX_DATA01",
        "TJQCHX_DATA01",
        "ZYHX_USERS",
        "HENNYHX_DATA01"
    );

//    String sFormat = "alter TABLE XF_TRANSSALESTOTAL_%s add COLUMN migration tinyint DEFAULT '0' COMMENT '迁移 0-否  1-是 2-已存在';";
//    String sFormat = "alter table xf_transsalesitem_%s drop migration;";
    String sFormat = "select count(1) from xf_transsalesitem_%s ;";

    for (String string : strings) {
      System.out.println(String.format(sFormat, string));
    }


  }

  @Test
  public void generate(){
    for (String schema : ALL_SCHEMA.split("\n")) {
      String sql = String.format("select * from %s.XF_TRANSSALESTOTAL where XF_CLIENTCODE = '532400129750' ; ",schema);
      System.out.println(sql);
    }
  }
}
