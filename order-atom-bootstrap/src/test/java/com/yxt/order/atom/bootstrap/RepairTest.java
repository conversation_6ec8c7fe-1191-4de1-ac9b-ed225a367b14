//package com.yxt.order.atom.bootstrap;
//
//import com.yxt.order.atom.repair.handler.ProblemDataRepairController;
//import javax.annotation.Resource;
//import org.junit.Test;
//
///**
// * @author: moatkon
// * @time: 2025/2/5 18:04
// */
//public class RepairTest extends BaseTest {
//
//  @Resource
//  private ProblemDataRepairController problemDataRepairHandler;
//
//
//  @Test
//  public void repairTest() {
//    for (; ; ) {
//      try {
//        problemDataRepairHandler.execute();
//      } catch (Exception e) {
//        e.printStackTrace();
//        System.out.println(e);
//      }
//      System.out.println();
//    }
//  }
//
//}
