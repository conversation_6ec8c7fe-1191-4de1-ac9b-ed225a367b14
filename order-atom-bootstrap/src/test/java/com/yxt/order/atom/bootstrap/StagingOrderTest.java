package com.yxt.order.atom.bootstrap;

import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.yxt.lang.util.JsonUtils;
import com.yxt.order.atom.common.LocalConst.DATA_SOURCE;
import com.yxt.order.atom.job.User404Handler;
import com.yxt.order.atom.job.abstractsStageOrder.RefundOrderDirectSave2Db;
import com.yxt.order.atom.job.abstractsStageOrder.RefundOrderFoundOrder;
import com.yxt.order.atom.job.abstractsStageOrder.User404DirectSave2Db;
import com.yxt.order.atom.job.abstractsStageOrder.User404FoundUser;
import com.yxt.order.atom.job.compensate.mongo.CompensateResultV1;
import com.yxt.order.atom.job.compensate.mongo.CompensateRetry;
import com.yxt.order.atom.job.compensate.mongo.CompensateType;
import com.yxt.order.atom.order.entity.OfflineRefundOrderDO;
import com.yxt.order.atom.order.mongo.StagingOrder;
import com.yxt.order.atom.order.repository.OfflineOrderRepository;
import com.yxt.order.atom.order.repository.StageOrderService;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineOrderStagingReqDto;
import com.yxt.order.types.offline.NumberType;
import com.yxt.order.types.offline.StagingType;
import com.yxt.order.types.offline.enums.ThirdPlatformCodeEnum;
import java.util.Date;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.data.mongodb.core.MongoTemplate;

/**
 * <AUTHOR> Runkang (moatkon)
 * @date 2024年08月13日 16:28
 * @email: <EMAIL>
 */
@Slf4j
public class StagingOrderTest extends BaseTest {

  @Resource
  private StageOrderService stageOrderService;


  /**
   *
   */
  @Test
  public void testInsert() {
    OfflineOrderStagingReqDto req = new OfflineOrderStagingReqDto();
    req.setStoreCode("Adfd");
    req.setThirdPlatformCode("KECHUAN");
    req.setThirdOrderNo("thirddd");
    req.setThirdRefundNo("refundDww");
    req.setDefineNo("delj");
    req.setUserCardNo("vvss");

    req.setMigration(Boolean.FALSE);
    req.setNumberType(NumberType.ORDER);
    req.setStagingType(StagingType.QUERY_ORDER_404_FROM_REFUND_NO);
    req.setData("tst");

    Boolean exist = stageOrderService.exist(req);
    req.setData("DD");

    Boolean exist2 = stageOrderService.exist(req);

    Boolean b = stageOrderService.create(req);
    System.out.println();
  }

  @Resource
  private RefundOrderDirectSave2Db refundOrderDirectSave2Db;
  @Resource
  private RefundOrderFoundOrder refundOrderFoundOrder;
  @Resource
  private User404DirectSave2Db user404DirectSave2Db;
  @Resource
  private User404FoundUser user404FoundUser;

  @Test
  public void testNonMigrationDirectSaveDB() {
    // #### -----非迁移----
    // 退款 海典 查不到正单 非迁移订单 直接入库
    StagingOrder refundOrderDirectSave2DbData = empty();
    refundOrderDirectSave2DbData.setMigration(Boolean.FALSE);
    refundOrderDirectSave2DbData.setStagingType(StagingType.QUERY_ORDER_404_FROM_REFUND_NO);
    refundOrderDirectSave2DbData.setData(
        "{\"retsaleno\":\"0\",\"shiftid\":3,\"reason\":\"DDF\",\"codes\":[],\"member_tel\":\"17780659272\",\"membercardno\":\"900000103543\",\"posno\":\"101\",\"member_name\":\"杨明杰\",\"saleDetails\":[{\"commonName\":\"(兴)复方甘草片_太极_50片\",\"rowno\":1,\"mdmBusno\":\"H812\",\"wareid\":100000,\"netprice\":35,\"makeno\":\"QH10024\",\"discount\":0,\"distype\":0,\"billAmount\":-105,\"orgname\":\"[H812]一心堂荣县旭水大道店\",\"compid\":1006,\"ware_qty\":-3,\"stdprice\":35,\"discountShare\":0,\"promoplanno\":\"\",\"compname\":\"四川一心堂医药连锁有限公司(测试)\"}],\"saleno\":\"1024081500000020\",\"payee\":\"1153740\",\"netsum\":-105,\"salePayDetails\":[{\"netsum\":-105,\"paytypen\":\"现金\",\"paytype\":\"1\",\"saleno\":\"1024081500000020\"}],\"shiftdate\":\"2024-08-15 11:06:58.806\",\"finaltime\":\"2024-08-15 11:06:58.806\",\"srcsaleno\":\"\"}");
    refundOrderDirectSave2DbData.setThirdPlatformCode(ThirdPlatformCodeEnum.HAIDIAN.name());
    refundOrderDirectSave2DbData.setStoreCode("H812");
    refundOrderDirectSave2DbData.setNumberType(NumberType.REFUND);
    refundOrderDirectSave2Db.handle(refundOrderDirectSave2DbData);

    // 退款 科传 查不到正单 非迁移订单 直接入库
    StagingOrder refundOrderDirectSave2DbDataKc = empty();
    refundOrderDirectSave2DbDataKc.setMigration(Boolean.FALSE);
    refundOrderDirectSave2DbDataKc.setStagingType(StagingType.QUERY_ORDER_404_FROM_REFUND_NO);
    refundOrderDirectSave2DbDataKc.setData(
        "{\"baseRefundInfo\":{\"thirdRefundNo\":\"A0020120240814135239S010002089\",\"parentThirdRefundNo\":\"\",\"thirdOrderNo\":\"\",\"created\":\"2024-08-14 13:52:39\",\"shopRefund\":4.1,\"consumerRefund\":4.1,\"serialNo\":\"\"},\"baseRefundOrganizationInfo\":{\"storeCode\":\"A002\",\"storeName\":\"一心堂昆明虫草参茸连锁店\"},\"baseRefundCashierDeskInfo\":{\"posCashierDeskNo\":\"01\",\"cashier\":\"1000001\",\"cashierName\":\"测试1\"},\"baseRefundUserInfo\":{\"userCardNo\":\"\"},\"refundDetailList\":[{\"baseRefundDetailInfo\":{\"rowNo\":\"1\",\"erpCode\":\"181351\",\"erpName\":\"(兴)小儿肺热咳喘颗粒_葫芦娃_4g*13袋\",\"commodityCount\":1.0,\"originalPrice\":1.0,\"price\":1.0,\"discountAmount\":0.0,\"billAmount\":1.0}},{\"baseRefundDetailInfo\":{\"rowNo\":\"2\",\"erpCode\":\"149856\",\"erpName\":\"陈香露白露片_飞云岭_0.5g*100片\",\"commodityCount\":1.0,\"originalPrice\":12.5,\"price\":3.1,\"discountAmount\":9.4,\"billAmount\":3.1}}],\"refundPayInfoList\":[{\"refundAmount\":4.1,\"payName\":\"现金\",\"payCode\":\"CH\"}],\"medInsSettle\":null}");
    refundOrderDirectSave2DbDataKc.setThirdPlatformCode(ThirdPlatformCodeEnum.KE_CHUAN.name());
    refundOrderDirectSave2DbDataKc.setStoreCode("A002");
    refundOrderDirectSave2DbDataKc.setNumberType(NumberType.REFUND);
    refundOrderDirectSave2Db.handle(refundOrderDirectSave2DbDataKc);

    // 退款 海典 查不到正单 非迁移订单 发现订单
    StagingOrder refundOrderData = empty();
    refundOrderData.setMigration(Boolean.FALSE);
    refundOrderData.setStagingType(StagingType.QUERY_ORDER_404_FROM_REFUND_NO);
    refundOrderData.setData(
        "{\"retsaleno\":\"0\",\"shiftid\":3,\"reason\":\"DDF\",\"codes\":[],\"member_tel\":\"17780659272\",\"membercardno\":\"900000103543\",\"posno\":\"101\",\"member_name\":\"杨明杰\",\"saleDetails\":[{\"commonName\":\"(兴)复方甘草片_太极_50片\",\"rowno\":1,\"mdmBusno\":\"H812\",\"wareid\":100000,\"netprice\":35,\"makeno\":\"QH10024\",\"discount\":0,\"distype\":0,\"billAmount\":-105,\"orgname\":\"[H812]一心堂荣县旭水大道店\",\"compid\":1006,\"ware_qty\":-3,\"stdprice\":35,\"discountShare\":0,\"promoplanno\":\"\",\"compname\":\"四川一心堂医药连锁有限公司(测试)\"}],\"saleno\":\"1024081500000020\",\"payee\":\"1153740\",\"netsum\":-105,\"salePayDetails\":[{\"netsum\":-105,\"paytypen\":\"现金\",\"paytype\":\"1\",\"saleno\":\"1024081500000020\"}],\"shiftdate\":\"2024-08-15 11:06:58.806\",\"finaltime\":\"2024-08-15 11:06:58.806\",\"srcsaleno\":\"\"}");
    refundOrderData.setThirdPlatformCode(ThirdPlatformCodeEnum.HAIDIAN.name());
    refundOrderData.setStoreCode("H812");
    refundOrderData.setNumberType(NumberType.REFUND);
    refundOrderFoundOrder.handle(refundOrderData);

    // 退款 科传 查不到正单 非迁移订单 发现订单
    StagingOrder refundOrderDataKc = empty();
    refundOrderDataKc.setMigration(Boolean.FALSE);
    refundOrderDataKc.setStagingType(StagingType.QUERY_ORDER_404_FROM_REFUND_NO);
    refundOrderDataKc.setData(
        "{\"baseRefundInfo\":{\"thirdRefundNo\":\"A0020120240814135239S010002089\",\"parentThirdRefundNo\":\"\",\"thirdOrderNo\":\"\",\"created\":\"2024-08-14 13:52:39\",\"shopRefund\":4.1,\"consumerRefund\":4.1,\"serialNo\":\"\"},\"baseRefundOrganizationInfo\":{\"storeCode\":\"A002\",\"storeName\":\"一心堂昆明虫草参茸连锁店\"},\"baseRefundCashierDeskInfo\":{\"posCashierDeskNo\":\"01\",\"cashier\":\"1000001\",\"cashierName\":\"测试1\"},\"baseRefundUserInfo\":{\"userCardNo\":\"\"},\"refundDetailList\":[{\"baseRefundDetailInfo\":{\"rowNo\":\"1\",\"erpCode\":\"181351\",\"erpName\":\"(兴)小儿肺热咳喘颗粒_葫芦娃_4g*13袋\",\"commodityCount\":1.0,\"originalPrice\":1.0,\"price\":1.0,\"discountAmount\":0.0,\"billAmount\":1.0}},{\"baseRefundDetailInfo\":{\"rowNo\":\"2\",\"erpCode\":\"149856\",\"erpName\":\"陈香露白露片_飞云岭_0.5g*100片\",\"commodityCount\":1.0,\"originalPrice\":12.5,\"price\":3.1,\"discountAmount\":9.4,\"billAmount\":3.1}}],\"refundPayInfoList\":[{\"refundAmount\":4.1,\"payName\":\"现金\",\"payCode\":\"CH\"}],\"medInsSettle\":null}");
    refundOrderDataKc.setThirdPlatformCode(ThirdPlatformCodeEnum.KE_CHUAN.name());
    refundOrderDataKc.setStoreCode("A002");
    refundOrderDataKc.setNumberType(NumberType.REFUND);
    refundOrderFoundOrder.handle(refundOrderDataKc);

  }


  @Test
  public void testMigrationDirectSaveDB() {
    // #### -----迁移----
    String migrationMockData = "{\"hanaOrderInfo\":{\"thirdOrderNo\":\"S010718646\",\"thirdRefundNo\":\"\",\"createTime\":\"2024-07-30T10:22:46.000+0000\",\"txDate\":\"2024-07-30\",\"txTime\":\"182244\",\"actualPayAmount\":-19.9000,\"actualCollectAmount\":-19.9000,\"posCashierDeskNo\":\"01\",\"cashier\":\"1132674\",\"picker\":\"\",\"storeCode\":\"A831\",\"clientCode\":\"\"},\"schema\":\"YNHX_DATA01\",\"taskKey\":\"hanaMigration_ynhx_data01_true_20240727000000_20240731232300\"}";

    // 退款 海典 查不到正单 迁移订单 直接入库
    StagingOrder refundOrderDirectSave2DbData = empty();
    refundOrderDirectSave2DbData.setMigration(Boolean.TRUE);
    refundOrderDirectSave2DbData.setStagingType(StagingType.QUERY_ORDER_404_FROM_REFUND_NO);
    refundOrderDirectSave2DbData.setData(migrationMockData);
    refundOrderDirectSave2DbData.setThirdPlatformCode(ThirdPlatformCodeEnum.HAIDIAN.name());
    refundOrderDirectSave2DbData.setStoreCode("H812");
    refundOrderDirectSave2DbData.setNumberType(NumberType.REFUND);
    refundOrderDirectSave2Db.handle(refundOrderDirectSave2DbData);

    // 退款 科传 查不到正单 迁移订单 直接入库
    StagingOrder refundOrderDirectSave2DbDataKc = empty();
    refundOrderDirectSave2DbDataKc.setMigration(Boolean.TRUE);
    refundOrderDirectSave2DbDataKc.setStagingType(StagingType.QUERY_ORDER_404_FROM_REFUND_NO);
    refundOrderDirectSave2DbDataKc.setData(migrationMockData);
    refundOrderDirectSave2DbDataKc.setThirdPlatformCode(ThirdPlatformCodeEnum.KE_CHUAN.name());
    refundOrderDirectSave2DbDataKc.setStoreCode("A002");
    refundOrderDirectSave2DbDataKc.setNumberType(NumberType.REFUND);
    refundOrderDirectSave2Db.handle(refundOrderDirectSave2DbDataKc);

    // 退款 海典 查不到正单 迁移订单 发现订单
    StagingOrder refundOrderData = empty();
    refundOrderData.setMigration(Boolean.TRUE);
    refundOrderData.setStagingType(StagingType.QUERY_ORDER_404_FROM_REFUND_NO);
    refundOrderData.setData(migrationMockData);
    refundOrderData.setThirdPlatformCode(ThirdPlatformCodeEnum.HAIDIAN.name());
    refundOrderData.setStoreCode("H812");
    refundOrderData.setNumberType(NumberType.REFUND);
    refundOrderFoundOrder.handle(refundOrderData);

    // 退款 科传 查不到正单 非迁移订单 发现订单
    StagingOrder refundOrderDataKc = empty();
    refundOrderDataKc.setMigration(Boolean.TRUE);
    refundOrderDataKc.setStagingType(StagingType.QUERY_ORDER_404_FROM_REFUND_NO);
    refundOrderDataKc.setData(migrationMockData);
    refundOrderDataKc.setThirdPlatformCode(ThirdPlatformCodeEnum.KE_CHUAN.name());
    refundOrderDataKc.setStoreCode("A002");
    refundOrderDataKc.setNumberType(NumberType.REFUND);
    refundOrderFoundOrder.handle(refundOrderDataKc);
  }

  //    #### ------迁移-------

//    user404DirectSave2Db.handle();
//    user404FoundUser.handle();


  @Test
  public void testNonMigrationUser404DirectSaveDB() {
    // #### -----非迁移----
    // 退款 海典 查不到正单 非迁移订单 直接入库
    StagingOrder refundOrderDirectSave2DbData = empty();
    refundOrderDirectSave2DbData.setMigration(Boolean.FALSE);
    refundOrderDirectSave2DbData.setStagingType(StagingType.QUERY_ORDER_404_FROM_REFUND_NO);
    refundOrderDirectSave2DbData.setData(
        "{\"retsaleno\":\"0\",\"shiftid\":3,\"reason\":\"DDF\",\"codes\":[],\"member_tel\":\"17780659272\",\"membercardno\":\"900000103543\",\"posno\":\"101\",\"member_name\":\"杨明杰\",\"saleDetails\":[{\"commonName\":\"(兴)复方甘草片_太极_50片\",\"rowno\":1,\"mdmBusno\":\"H812\",\"wareid\":100000,\"netprice\":35,\"makeno\":\"QH10024\",\"discount\":0,\"distype\":0,\"billAmount\":-105,\"orgname\":\"[H812]一心堂荣县旭水大道店\",\"compid\":1006,\"ware_qty\":-3,\"stdprice\":35,\"discountShare\":0,\"promoplanno\":\"\",\"compname\":\"四川一心堂医药连锁有限公司(测试)\"}],\"saleno\":\"1024081500000020\",\"payee\":\"1153740\",\"netsum\":-105,\"salePayDetails\":[{\"netsum\":-105,\"paytypen\":\"现金\",\"paytype\":\"1\",\"saleno\":\"1024081500000020\"}],\"shiftdate\":\"2024-08-15 11:06:58.806\",\"finaltime\":\"2024-08-15 11:06:58.806\",\"srcsaleno\":\"\"}");
    refundOrderDirectSave2DbData.setThirdPlatformCode(ThirdPlatformCodeEnum.HAIDIAN.name());
    refundOrderDirectSave2DbData.setStoreCode("H812");
    refundOrderDirectSave2DbData.setNumberType(NumberType.REFUND);
    user404DirectSave2Db.handle(refundOrderDirectSave2DbData);

    // 退款 科传 查不到正单 非迁移订单 直接入库
    StagingOrder refundOrderDirectSave2DbDataKc = empty();
    refundOrderDirectSave2DbDataKc.setMigration(Boolean.FALSE);
    refundOrderDirectSave2DbDataKc.setStagingType(StagingType.QUERY_ORDER_404_FROM_REFUND_NO);
    refundOrderDirectSave2DbDataKc.setData(
        "{\"baseRefundInfo\":{\"thirdRefundNo\":\"A0020120240814135239S010002089\",\"parentThirdRefundNo\":\"\",\"thirdOrderNo\":\"\",\"created\":\"2024-08-14 13:52:39\",\"shopRefund\":4.1,\"consumerRefund\":4.1,\"serialNo\":\"\"},\"baseRefundOrganizationInfo\":{\"storeCode\":\"A002\",\"storeName\":\"一心堂昆明虫草参茸连锁店\"},\"baseRefundCashierDeskInfo\":{\"posCashierDeskNo\":\"01\",\"cashier\":\"1000001\",\"cashierName\":\"测试1\"},\"baseRefundUserInfo\":{\"userCardNo\":\"\"},\"refundDetailList\":[{\"baseRefundDetailInfo\":{\"rowNo\":\"1\",\"erpCode\":\"181351\",\"erpName\":\"(兴)小儿肺热咳喘颗粒_葫芦娃_4g*13袋\",\"commodityCount\":1.0,\"originalPrice\":1.0,\"price\":1.0,\"discountAmount\":0.0,\"billAmount\":1.0}},{\"baseRefundDetailInfo\":{\"rowNo\":\"2\",\"erpCode\":\"149856\",\"erpName\":\"陈香露白露片_飞云岭_0.5g*100片\",\"commodityCount\":1.0,\"originalPrice\":12.5,\"price\":3.1,\"discountAmount\":9.4,\"billAmount\":3.1}}],\"refundPayInfoList\":[{\"refundAmount\":4.1,\"payName\":\"现金\",\"payCode\":\"CH\"}],\"medInsSettle\":null}");
    refundOrderDirectSave2DbDataKc.setThirdPlatformCode(ThirdPlatformCodeEnum.KE_CHUAN.name());
    refundOrderDirectSave2DbDataKc.setStoreCode("A002");
    refundOrderDirectSave2DbDataKc.setNumberType(NumberType.REFUND);
    user404DirectSave2Db.handle(refundOrderDirectSave2DbDataKc);

    // 退款 海典 查不到正单 非迁移订单 发现订单
    StagingOrder refundOrderData = empty();
    refundOrderData.setMigration(Boolean.FALSE);
    refundOrderData.setStagingType(StagingType.QUERY_ORDER_404_FROM_REFUND_NO);
    refundOrderData.setData(
        "{\"retsaleno\":\"0\",\"shiftid\":3,\"reason\":\"DDF\",\"codes\":[],\"member_tel\":\"17780659272\",\"membercardno\":\"900000103543\",\"posno\":\"101\",\"member_name\":\"杨明杰\",\"saleDetails\":[{\"commonName\":\"(兴)复方甘草片_太极_50片\",\"rowno\":1,\"mdmBusno\":\"H812\",\"wareid\":100000,\"netprice\":35,\"makeno\":\"QH10024\",\"discount\":0,\"distype\":0,\"billAmount\":-105,\"orgname\":\"[H812]一心堂荣县旭水大道店\",\"compid\":1006,\"ware_qty\":-3,\"stdprice\":35,\"discountShare\":0,\"promoplanno\":\"\",\"compname\":\"四川一心堂医药连锁有限公司(测试)\"}],\"saleno\":\"1024081500000020\",\"payee\":\"1153740\",\"netsum\":-105,\"salePayDetails\":[{\"netsum\":-105,\"paytypen\":\"现金\",\"paytype\":\"1\",\"saleno\":\"1024081500000020\"}],\"shiftdate\":\"2024-08-15 11:06:58.806\",\"finaltime\":\"2024-08-15 11:06:58.806\",\"srcsaleno\":\"\"}");
    refundOrderData.setThirdPlatformCode(ThirdPlatformCodeEnum.HAIDIAN.name());
    refundOrderData.setStoreCode("H812");
    refundOrderData.setNumberType(NumberType.REFUND);
    user404FoundUser.handle(refundOrderData);

    // 退款 科传 查不到正单 非迁移订单 发现订单
    StagingOrder refundOrderDataKc = empty();
    refundOrderDataKc.setMigration(Boolean.FALSE);
    refundOrderDataKc.setStagingType(StagingType.QUERY_ORDER_404_FROM_REFUND_NO);
    refundOrderDataKc.setData(
        "{\"baseRefundInfo\":{\"thirdRefundNo\":\"A0020120240814135239S010002089\",\"parentThirdRefundNo\":\"\",\"thirdOrderNo\":\"\",\"created\":\"2024-08-14 13:52:39\",\"shopRefund\":4.1,\"consumerRefund\":4.1,\"serialNo\":\"\"},\"baseRefundOrganizationInfo\":{\"storeCode\":\"A002\",\"storeName\":\"一心堂昆明虫草参茸连锁店\"},\"baseRefundCashierDeskInfo\":{\"posCashierDeskNo\":\"01\",\"cashier\":\"1000001\",\"cashierName\":\"测试1\"},\"baseRefundUserInfo\":{\"userCardNo\":\"\"},\"refundDetailList\":[{\"baseRefundDetailInfo\":{\"rowNo\":\"1\",\"erpCode\":\"181351\",\"erpName\":\"(兴)小儿肺热咳喘颗粒_葫芦娃_4g*13袋\",\"commodityCount\":1.0,\"originalPrice\":1.0,\"price\":1.0,\"discountAmount\":0.0,\"billAmount\":1.0}},{\"baseRefundDetailInfo\":{\"rowNo\":\"2\",\"erpCode\":\"149856\",\"erpName\":\"陈香露白露片_飞云岭_0.5g*100片\",\"commodityCount\":1.0,\"originalPrice\":12.5,\"price\":3.1,\"discountAmount\":9.4,\"billAmount\":3.1}}],\"refundPayInfoList\":[{\"refundAmount\":4.1,\"payName\":\"现金\",\"payCode\":\"CH\"}],\"medInsSettle\":null}");
    refundOrderDataKc.setThirdPlatformCode(ThirdPlatformCodeEnum.KE_CHUAN.name());
    refundOrderDataKc.setStoreCode("A002");
    refundOrderDataKc.setNumberType(NumberType.REFUND);
    user404FoundUser.handle(refundOrderDataKc);

  }


  @Test
  public void testMigrationUser404DirectSaveDB() {
    // #### -----迁移----
    String migrationMockData = "{\"hanaOrderInfo\":{\"thirdOrderNo\":\"S010718646\",\"thirdRefundNo\":\"\",\"createTime\":\"2024-07-30T10:22:46.000+0000\",\"txDate\":\"2024-07-30\",\"txTime\":\"182244\",\"actualPayAmount\":-19.9000,\"actualCollectAmount\":-19.9000,\"posCashierDeskNo\":\"01\",\"cashier\":\"1132674\",\"picker\":\"\",\"storeCode\":\"A831\",\"clientCode\":\"\"},\"schema\":\"YNHX_DATA01\",\"taskKey\":\"hanaMigration_ynhx_data01_true_20240727000000_20240731232300\"}";

    // 退款 海典 查不到正单 迁移订单 直接入库
    StagingOrder refundOrderDirectSave2DbData = empty();
    refundOrderDirectSave2DbData.setMigration(Boolean.TRUE);
    refundOrderDirectSave2DbData.setStagingType(StagingType.QUERY_ORDER_404_FROM_REFUND_NO);
    refundOrderDirectSave2DbData.setData(migrationMockData);
    refundOrderDirectSave2DbData.setThirdPlatformCode(ThirdPlatformCodeEnum.HAIDIAN.name());
    refundOrderDirectSave2DbData.setStoreCode("H812");
    refundOrderDirectSave2DbData.setNumberType(NumberType.REFUND);
    user404DirectSave2Db.handle(refundOrderDirectSave2DbData);

    // 退款 科传 查不到正单 迁移订单 直接入库
    StagingOrder refundOrderDirectSave2DbDataKc = empty();
    refundOrderDirectSave2DbDataKc.setMigration(Boolean.TRUE);
    refundOrderDirectSave2DbDataKc.setStagingType(StagingType.QUERY_ORDER_404_FROM_REFUND_NO);
    refundOrderDirectSave2DbDataKc.setData(migrationMockData);
    refundOrderDirectSave2DbDataKc.setThirdPlatformCode(ThirdPlatformCodeEnum.KE_CHUAN.name());
    refundOrderDirectSave2DbDataKc.setStoreCode("A002");
    refundOrderDirectSave2DbDataKc.setNumberType(NumberType.REFUND);
    user404DirectSave2Db.handle(refundOrderDirectSave2DbDataKc);

    // 退款 海典 查不到正单 迁移订单 发现订单
    StagingOrder refundOrderData = empty();
    refundOrderData.setMigration(Boolean.TRUE);
    refundOrderData.setStagingType(StagingType.QUERY_ORDER_404_FROM_REFUND_NO);
    refundOrderData.setData(migrationMockData);
    refundOrderData.setThirdPlatformCode(ThirdPlatformCodeEnum.HAIDIAN.name());
    refundOrderData.setStoreCode("H812");
    refundOrderData.setNumberType(NumberType.REFUND);
    user404FoundUser.handle(refundOrderData);

    // 退款 科传 查不到正单 非迁移订单 发现订单
    StagingOrder refundOrderDataKc = empty();
    refundOrderDataKc.setMigration(Boolean.TRUE);
    refundOrderDataKc.setStagingType(StagingType.QUERY_ORDER_404_FROM_REFUND_NO);
    refundOrderDataKc.setData(migrationMockData);
    refundOrderDataKc.setThirdPlatformCode(ThirdPlatformCodeEnum.KE_CHUAN.name());
    refundOrderDataKc.setStoreCode("A002");
    refundOrderDataKc.setNumberType(NumberType.REFUND);
    user404FoundUser.handle(refundOrderDataKc);
  }


  private static StagingOrder empty() {
    StagingOrder base = new StagingOrder();
    base.setId("mockId");
    base.setCreateTime(new Date());
    base.setHandled(Boolean.FALSE);
    base.setThirdOrderNo("mockThirdOrderNo");
    base.setThirdRefundNo("mockThirdRefundNo");
    base.setUserCardNo("mockUserCardNo");
    base.setDefineNo("mockDefineNo");
    return base;
  }

  @Resource
  private User404Handler user404Handler;

  @Test
  public void testJob() {
    for (; ; ) {
      try {
        user404Handler.execute();
        System.out.println();
      } catch (Exception e) {
        throw new RuntimeException(e);
      }
    }
  }
  @Resource
  private MongoTemplate mongoTemplate;
  @Test
  public void testInsert2(){
    CompensateResultV1 compensateResultV1 = new CompensateResultV1();
    compensateResultV1.setDataJson("{}}");
    compensateResultV1.setCompensateType(CompensateType.NO_VIP_FAILED);
    compensateResultV1.setCreateTime(new Date());
    compensateResultV1.setRetry(CompensateRetry.UN_RETRY);
    compensateResultV1.setResult(false);
    // 可能会和异常的记录重复,因为异常的返回的是false
    mongoTemplate.insert(compensateResultV1);
  }

  @Resource
  private OfflineOrderRepository offlineOrderRepository;
  @Test
  public void testFix(){
    DynamicDataSourceContextHolder.push(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME);

    while (true){
      try {
//        String json = "{\"id\":null,\"orderNo\":\"\",\"storeCode\":\"F199\",\"userId\":\"1808510293980392451\",\"refundNo\":\"1536220172935760010\",\"parentRefundNo\":\"\",\"thirdPlatformCode\":\"HAIDIAN\",\"thirdRefundNo\":\"1124082802537542\",\"parentThirdRefundNo\":null,\"thirdOrderNo\":\"1124082802525002\",\"refundType\":\"UNKOWN\",\"afterSaleType\":\"AFTER_SALE_AMOUNT_GOODS\",\"refundState\":\"REFUNDED\",\"reason\":\"买错了\",\"created\":\"2024-08-28T10:00:53.000+0000\",\"applyTime\":\"2024-08-28T10:00:53.000+0000\",\"completeTime\":\"2024-08-28T10:00:53.000+0000\",\"billTime\":\"2024-08-28T10:00:53.000+0000\",\"totalAmount\":15.5,\"shopRefund\":15.5,\"consumerRefund\":15.5,\"createdBy\":\"\",\"updatedBy\":\"\",\"createdTime\":\"2024-12-06T01:46:15.000+0000\",\"updatedTime\":\"2024-12-06T01:46:15.000+0000\",\"version\":1,\"serialNo\":null,\"refundOrderDetailDOList\":null,\"refundOrderPayDOList\":null,\"offlineRefundOrderMedInsSettleDO\":null,\"offlineRefundOrderCashierDeskDO\":{\"id\":null,\"refundNo\":\"1536220172935760010\",\"posCashierDeskNo\":\"101\",\"cashier\":\"1093712\",\"cashierName\":null,\"picker\":null,\"pickerName\":null,\"shiftId\":\"1\",\"shiftDate\":\"2024-08-28T10:00:53.000+0000\",\"createdBy\":\"\",\"updatedBy\":\"\",\"createdTime\":\"2024-12-06T01:46:15.000+0000\",\"updatedTime\":\"2024-12-06T01:46:15.000+0000\",\"version\":1},\"offlineRefundOrderOrganizationDO\":{\"id\":null,\"refundNo\":\"1536220172935760010\",\"storeCode\":\"F199\",\"storeName\":\"[F199]一心堂江津金科世界城店\",\"companyCode\":\"1005\",\"companyName\":\"重庆鸿翔一心堂药业有限公司\",\"storeDirectJoinType\":\"DIRECT_SALES\",\"createdBy\":\"\",\"updatedBy\":\"\",\"createdTime\":\"2024-12-06T01:46:15.000+0000\",\"updatedTime\":\"2024-12-06T01:46:15.000+0000\",\"version\":1},\"offlineRefundOrderUserDO\":null,\"migration\":null,\"isOnPromotion\":\"false\"}";

        String jsonyy = "{\"id\":null,\"orderNo\":\"2969145967741512412\",\"storeCode\":\"A985\",\"userId\":null,\"refundNo\":\"2613760678392392412\",\"parentRefundNo\":\"\",\"thirdPlatformCode\":\"KE_CHUAN\",\"thirdRefundNo\":\"S030000013\",\"parentThirdRefundNo\":null,\"thirdOrderNo\":\"S030000012\",\"refundType\":\"ALL\",\"afterSaleType\":null,\"refundState\":\"REFUNDED\",\"reason\":null,\"created\":\"2024-12-03T05:47:02.000+0000\",\"applyTime\":\"2024-12-03T05:47:02.000+0000\",\"completeTime\":\"2024-12-03T05:47:02.000+0000\",\"billTime\":\"2024-12-03T05:47:02.000+0000\",\"totalAmount\":36.10,\"shopRefund\":36.1,\"consumerRefund\":36.1,\"createdBy\":\"\",\"updatedBy\":\"\",\"createdTime\":\"2024-12-06T03:49:30.000+0000\",\"updatedTime\":\"2024-12-06T03:49:30.000+0000\",\"version\":1,\"serialNo\":\"\",\"refundOrderDetailDOList\":null,\"refundOrderPayDOList\":null,\"offlineRefundOrderMedInsSettleDO\":null,\"offlineRefundOrderCashierDeskDO\":{\"id\":null,\"refundNo\":\"2613760678392392412\",\"posCashierDeskNo\":\"03\",\"cashier\":\"55555\",\"cashierName\":\"信息专用帐号\",\"picker\":null,\"pickerName\":null,\"shiftId\":null,\"shiftDate\":null,\"createdBy\":\"\",\"updatedBy\":\"\",\"createdTime\":\"2024-12-06T03:49:30.000+0000\",\"updatedTime\":\"2024-12-06T03:49:30.000+0000\",\"version\":1},\"offlineRefundOrderOrganizationDO\":{\"id\":null,\"refundNo\":\"2613760678392392412\",\"storeCode\":\"A985\",\"storeName\":\"一心堂昆明和平村连锁店\",\"companyCode\":null,\"companyName\":null,\"storeDirectJoinType\":\"DIRECT_SALES\",\"createdBy\":\"\",\"updatedBy\":\"\",\"createdTime\":\"2024-12-06T03:49:30.000+0000\",\"updatedTime\":\"2024-12-06T03:49:30.000+0000\",\"version\":1},\"offlineRefundOrderUserDO\":null,\"migration\":null,\"isOnPromotion\":\"false\"}";

//        OfflineRefundOrderDO refundOrderDO = JsonUtils.toObject(json, OfflineRefundOrderDO.class);
//        offlineOrderRepository.compensateRefundOrderData(refundOrderDO);

        OfflineRefundOrderDO refundOrderDOyy = JsonUtils.toObject(jsonyy, OfflineRefundOrderDO.class);
        offlineOrderRepository.compensateRefundOrderData(refundOrderDOyy);


      } catch (Exception e) {
        log.error("",e);
      }


    }
  }

}
