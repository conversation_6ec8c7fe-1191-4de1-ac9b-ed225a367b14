CREATE TABLE XF_TRANSSALESITEM_${schema} (
                                   id INT AUTO_INCREMENT PRIMARY KEY,
                                   XF_STORECODE VARCHAR(6),
                                   XF_TILLID VARCHAR(3),
                                   XF_TXDATE DATE,
                                   XF_TXSERIAL DOUBLE,
                                   XF_TXTIME VARCHAR(6),
                                   XF_TXBATCH DOUBLE,
                                   XF_DOCNO VARCHAR(10),
                                   XF_VOIDDOCNO VARCHAR(10),
                                   XF_TXTYPE DOUBLE,
                                   XF_TXHOUR DOUBLE,
                                   XF_CASHIER VARCHAR(10),
                                   XF_<PERSON>LESMAN VARCHAR(10),
                                   XF_VIPCODE VARCHAR(24),
                                   <PERSON>F_<PERSON>MOGRAPCODE VARCHAR(15),
                                   XF_DEMOGRAPDATA VARCHAR(15),
                                   XF_PLU VARCHAR(30),
                                   XF_STYLE VARCHAR(30),
                                   XF_COLOR VARCHAR(6),
                                   <PERSON>F_<PERSON>I<PERSON><PERSON> VARCHAR(14),
                                   <PERSON><PERSON>_<PERSON>EMLOTNUM VARCHAR(30),
                                   XF_QTYSOLD DECIMAL(16, 4),
                                   <PERSON>F_AMTSOLD DECIMAL(16, 4),
                                   <PERSON>F_COSTSOLD DECIMAL(18, 6),
                                   XF_MARKDOWNAMT DECIMAL(16, 4),
                                   XF_DISCOUNTAMT DECIMAL(16, 4),
                                   XF_PROMOTIONAMT DECIMAL(16, 4),
                                   XF_TAXAMOUNT1 DECIMAL(16, 4),
                                   XF_TAXAMOUNT2 DECIMAL(16, 4),
                                   XF_TAXRATE1 DECIMAL(16, 4),
                                   XF_TAXRATE2 DECIMAL(16, 4),
                                   XF_EXSTK2SALES DECIMAL(12, 4),
                                   XF_ORGUPRICE DECIMAL(16, 4),
                                   XF_ISDEPOSIT VARCHAR(1),
                                   XF_ISWHOLESALE VARCHAR(1),
                                   XF_ISPRICEALTERNATE VARCHAR(1),
                                   XF_ISPRICEOVERRIDE VARCHAR(1),
                                   XF_ISNEWITEM VARCHAR(1),
                                   XF_PRICEAPPROVE VARCHAR(10),
                                   XF_COUPONNUMBER VARCHAR(12),
                                   XF_DISCOUNTAPPROVE VARCHAR(10),
                                   XF_ITEMDISCOUNTAMT DECIMAL(16, 4),
                                   XF_TTLDISCOUNTLESS DECIMAL(16, 4),
                                   XF_PROMID1 VARCHAR(6),
                                   XF_PROMAMT1 DECIMAL(16, 4),
                                   XF_PROMQTY1 DECIMAL(16, 4),
                                   XF_PROMID2 VARCHAR(6),
                                   XF_PROMAMT2 DECIMAL(16, 4),
                                   XF_PROMQTY2 DECIMAL(16, 4),
                                   XF_PROMID3 VARCHAR(6),
                                   XF_PROMAMT3 DECIMAL(16, 4),
                                   XF_PROMQTY3 DECIMAL(16, 4),
                                   XF_PROMID4 VARCHAR(6),
                                   XF_PROMAMT4 DECIMAL(16, 4),
                                   XF_PROMQTY4 DECIMAL(16, 4),
                                   XF_PROMID5 VARCHAR(6),
                                   XF_PROMAMT5 DECIMAL(16, 4),
                                   XF_PROMQTY5 DECIMAL(16, 4),
                                   XF_SALESITEMREMARK VARCHAR(80),
                                   XF_EXTENDPARAM VARCHAR(250),
                                   XF_DESTLOCATIONLIST VARCHAR(250),
                                   XF_PRICECENTER VARCHAR(6),
                                   XF_COSTCENTER VARCHAR(6),
                                   XF_POSTDATE VARCHAR(21),
                                   XF_CREATETIME VARCHAR(21),
                                   XF_ISPOSTING VARCHAR(1),
                                   CRM_EXECUTED VARCHAR(1),
                                   CRM_EXECUTED1 VARCHAR(1),
                                   XF_HXALLDISCLESS1 DECIMAL(16, 4),
                                   XF_YIBAOLESS DECIMAL(16, 4),
                                   XF_PAYMENTLESS DECIMAL(16, 4),
                                   UNIQUE KEY (XF_STORECODE, XF_TILLID, XF_TXDATE, XF_TXSERIAL),
                                   KEY `idx_docNo_item` (`XF_STORECODE`,`XF_DOCNO`,`XF_TXDATE`,`XF_TILLID`),
                                   KEY `idx_create_time` (`XF_CREATETIME`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;