package com.yxt.order.atom.bootstrap.es;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import java.util.ArrayList;
import java.util.List;
import lombok.Getter;


public class ImportGoodsDataListener extends AnalysisEventListener<GoodsData> {

  @Getter
  public int count = 0;

  @Getter
  private List<GoodsData> dataList = new ArrayList<>();

  @Override
  public void onException(Exception e, AnalysisContext context) throws Exception {
    throw e;
  }

  @Override
  public void invoke(GoodsData goodsData, AnalysisContext analysisContext) {
    dataList.add(goodsData);
    count = count + 1;
  }

  @Override
  public void doAfterAllAnalysed(AnalysisContext analysisContext) {

  }

}