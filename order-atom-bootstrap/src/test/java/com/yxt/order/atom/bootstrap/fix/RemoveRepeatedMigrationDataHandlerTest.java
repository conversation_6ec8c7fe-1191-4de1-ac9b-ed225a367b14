package com.yxt.order.atom.bootstrap.fix;

import com.yxt.order.atom.bootstrap.BaseTest;

/**
 * @author: moatkon
 * @time: 2025/3/10 16:48
 */
public class RemoveRepeatedMigrationDataHandlerTest extends BaseTest {

//  @Resource
//  private RemoveRepeatedMigrationDataController removeRepeatedMigrationDataHandler;
//
//  @Test
//  public void testRemoveRepeatedOrderData() {
//    removeRepeatedMigrationDataHandler.removeRepeatedOrderData();
//  }
//
//  @Test
//  public void testRemoveRepeatedRefundOrderData() {
//    removeRepeatedMigrationDataHandler.removeRepeatedRefundOrderData();
//  }
}