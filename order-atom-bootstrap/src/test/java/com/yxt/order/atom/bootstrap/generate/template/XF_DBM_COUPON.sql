-- 只有云南公司启用了 YNHX_DATA01
CREATE TABLE XF_DBM_COUPON_${schema} (
                                 `id` int NOT NULL AUTO_INCREMENT,
                                 `XF_STORECODE` VARCHAR(10) NOT NULL DEFAULT '',
                                 `XF_TILLID` VARCHAR(10) NOT NULL DEFAULT '',
                                 `XF_TXDATE` VARCHAR(21) NOT NULL DEFAULT '',
                                 `XF_TXTIME` VARCHAR(10) NOT NULL DEFAULT '',
                                 `XF_DOCNO` VARCHAR(10) NOT NULL DEFAULT '',
                                 `XF_DBMID` VARCHAR(90) NOT NULL DEFAULT '',
                                 `XF_DESCI` VARCHAR(300) NOT NULL DEFAULT '',
                                 `XF_REBATEAMT` DECIMAL(16,4) NOT NULL DEFAULT 0,
                                 `XF_EXPIRETIME` VARCHAR(20) NOT NULL DEFAULT '',
                                 `XF_STATUS` VARCHAR(3) NOT NULL DEFAULT '',
                                 `XF_USEDDATE` VARCHAR(21) NOT NULL DEFAULT '',
                                 `XF_USEDTIME` VARCHAR(10) NOT NULL DEFAULT '',
                                 `XF_USEDDOCNO` VARCHAR(10) NOT NULL DEFAULT '',
                                 PRIMARY KEY (`id`),
                                 UNIQUE KEY (`XF_STORECODE`, `XF_TILLID`, `XF_TXDATE`, `XF_TXTIME`, `XF_DOCNO`),
                                 KEY `idx_docNo_storeCode` (`XF_DOCNO`,`XF_STORECODE`) USING BTREE,
                                 KEY `idx_usedDocNo_storeCode` (`XF_USEDDOCNO`,`XF_STORECODE`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;