package com.yxt.order.atom.sdk.online_order.refund_info.dto.req;

import com.yxt.order.atom.sdk.common.data.ErpBillInfoDTO;
import com.yxt.order.atom.sdk.common.data.ErpRefundInfoDTO;
import com.yxt.order.atom.sdk.common.data.OrderCommodityDetailCostPriceDTO;
import com.yxt.order.atom.sdk.common.data.OrderDetailCommodityCostPriceDTO;
import com.yxt.order.atom.sdk.common.data.OrderDetailDTO;
import com.yxt.order.atom.sdk.common.data.OrderInfoDTO;
import com.yxt.order.atom.sdk.common.data.OrderPickInfoDTO;
import com.yxt.order.atom.sdk.common.data.RefundDetailDTO;
import com.yxt.order.atom.sdk.common.data.RefundOrderDTO;
import com.yxt.order.common.base_order_dto.ErpBillInfo;
import com.yxt.order.common.base_order_dto.ErpRefundInfo;
import com.yxt.order.common.base_order_dto.OrderCommodityDetailCostPrice;
import com.yxt.order.common.base_order_dto.OrderDetailCommodityCostPrice;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UpdateRefundAuditOptionalReq {

  /**
   * 主退单
   */
  private RefundOrderDTO refundOrder;

  /**
   * 主退单的退款明细
   */
  private List<RefundDetailDTO> refundDetailList = new ArrayList<>();

  /**
   * 主退单下账信息
   */
  private ErpRefundInfoDTO erpRefundInfo;

  /**
   * 主订单
   */
  private OrderInfoDTO orderInfo;

  /**
   * 主订单对应的订单明细
   */
  private List<OrderDetailDTO> orderDetailList = new ArrayList<>();

  /**
   * 下账信息
   */
  private ErpBillInfoDTO erpBillInfo;

  //加权成本价重算
  private Boolean needUpdateCostFlag;
  private List<OrderDetailCommodityCostPriceDTO> orderDetailCommodityCostPriceList;

  private List<OrderCommodityDetailCostPriceDTO> orderCommodityDetailCostPriceList;

  /**
   * 该订单下其他的退款单
   */
  private List<RefundOrderDTO> otherRefundList;

  /**
   * 运费订单
   */
  private OrderInfoDTO freightOrderInfo;

  /**
   * 运费订单的退款单
   */
  private RefundOrderDTO freightRefundInfo;

  /**
   * 拣货信息
   */
  private List<OrderPickInfoDTO> orderPickInfoList;

}
