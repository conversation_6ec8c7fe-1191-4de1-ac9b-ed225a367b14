package com.yxt.order.atom.sdk.common.data;

import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

@Data
public class AccountCheckChannelDTO {

  private Long id;

  /**
   * 商户编码
   */
  private String merCode;

  /**
   * 平台编码
   */
  private String thirdPlatformCode;

  /**
   * 门店编码
   */
  private String onlineStoreCode;

  /**
   * 外部门店ID
   */
  private String outShopId;

  /**
   * 第三方平台订单号
   */
  private String thirdOrderNo;

  /**
   * md5编码
   */
  private String md5Code;

  /**
   * 商家实收
   */
  private BigDecimal merchantActualAmount;

  /**
   * 商品总金额
   */
  private BigDecimal totalAmount;

  /**
   * 平台优惠
   */
  private BigDecimal platformDiscount;

  /**
   * 商家优惠
   */
  private BigDecimal merchantDiscount;

  /**
   * 交易佣金
   */
  private BigDecimal brokerageAmount;

  /**
   * 平台配送费
   */
  private BigDecimal platformDeliveryFee;

  /**
   * 商家配送费
   */
  private BigDecimal merchantDeliveryFee;

  /**
   * 平台打包费
   */
  private BigDecimal platformPackFee;

  /**
   * 商家打包费
   */
  private BigDecimal merchantPackFee;

  /**
   * 原始数据
   */
  private String orginData;

  /**
   * 账单日期
   */
  private Date accountTime;

  /**
   * 创建时间
   */
  private Date createTime;

  /**
   * 末次修改时间
   */
  private Date modifyTime;
}
