package com.yxt.order.atom.sdk.online_order.account_check.req;

import com.yxt.order.types.order.MerCode;
import com.yxt.order.types.order.enums.PlatformCodeEnum;
import java.util.List;
import lombok.Data;

@Data
public class AccountCheckCleanReqDto {

  /**
   * 商户编码
   */
  private MerCode merCode;

  /**
   * 平台编码
   */
  private List<PlatformCodeEnum> platformCodeList;

  /**
   * 网店编码
   */
  private List<String> onlineStoreCodeList;

  /**
   * 清洗天数
   */
  private Integer cleanDays;

}
