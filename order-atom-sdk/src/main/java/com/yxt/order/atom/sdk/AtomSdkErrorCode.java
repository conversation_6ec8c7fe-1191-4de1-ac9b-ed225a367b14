package com.yxt.order.atom.sdk;

import lombok.Getter;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年04月08日 17:04
 * @email: <EMAIL>
 */
public enum AtomSdkErrorCode {
  REFUND_ORDER_NOT_HAVA_NORMAL_ORDER(800, "退单对应的正单未推到订单库,找不到订单"),
  ;


  /**
   * 编码:ATOM_SDK_ + code
   */
  @Getter
  private Integer sdkErrorCode;
  @Getter
  private String msg;

  AtomSdkErrorCode(Integer sdkErrorCode, String msg) {
    this.sdkErrorCode = sdkErrorCode;
    this.msg = msg;
  }

}
