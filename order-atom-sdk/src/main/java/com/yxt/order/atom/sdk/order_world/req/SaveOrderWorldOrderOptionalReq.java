package com.yxt.order.atom.sdk.order_world.req;

import com.yxt.order.atom.sdk.common.data.CommodityStockChangeRecordDTO;
import com.yxt.order.atom.sdk.common.order_world.OrderAmountDTO;
import com.yxt.order.atom.sdk.common.order_world.OrderDeliveryAddressDTO;
import com.yxt.order.atom.sdk.common.order_world.OrderDetailDTO;
import com.yxt.order.atom.sdk.common.order_world.OrderInfoDTO;
import com.yxt.order.atom.sdk.common.order_world.OrderPayDTO;
import com.yxt.order.atom.sdk.common.order_world.OrderUserInfoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

@Data
@ApiModel("订单新模型-新建订单请求")
public class SaveOrderWorldOrderOptionalReq {

  @ApiModelProperty(value = "订单信息")
  private OrderInfoDTO orderInfo;

  @ApiModelProperty(value = "订单明细信息")
  private List<OrderDetailDTO> orderDetailList;

  @ApiModelProperty(value = "订单收件人信息")
  private OrderDeliveryAddressDTO orderDeliveryAddress;

  @ApiModelProperty(value = "订单支付方式信息")
  private List<OrderPayDTO> orderPayList;

  @ApiModelProperty(value = "订单金额信息")
  private OrderAmountDTO orderAmount;

  @ApiModelProperty(value = "订单用户信息")
  private OrderUserInfoDTO orderUserInfo;

  @ApiModelProperty(value = "库存占用记录")
  private List<CommodityStockChangeRecordDTO> commodityStockChangeRecordList;

}
