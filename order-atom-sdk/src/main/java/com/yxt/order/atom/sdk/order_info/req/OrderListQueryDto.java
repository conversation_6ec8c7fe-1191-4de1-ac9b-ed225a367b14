package com.yxt.order.atom.sdk.order_info.req;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * @version: V1.0
 * @author: Gxb-2983
 * @className: OrderListQueryDto
 * @packageName: cn.hydee.business.order.b2c.service.baseinfo.order.dto
 * @description: 订单列表查询对象
 * @data: 2020/11/15 14:40
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class OrderListQueryDto extends Authority {

    @ApiModelProperty("系统订单号")
    private String orderNo;

    @ApiModelProperty("发货单号")
    private String omsShipNo;

    @ApiModelProperty("三方平台订单号")
    private String  thirdOrderNo;

//    @ApiModelProperty("发货单号")
//    private String  omsOrderNo;

    @ApiModelProperty("快递单号")
    private String  expressNo;

    @ApiModelProperty(value = "买家昵称，支持模糊查询")
    private String buyerName;

    @ApiModelProperty(value = "收货人，支持模糊查询")
    private String receiverName;

    @ApiModelProperty(value = "收货人电话")
    private String receiverPhone;

    @ApiModelProperty(value = "ERP商品编码")
    private String commodityCode;

    @ApiModelProperty(value = "商品名称，支持模糊查询")
    private String commodityName;

    @ApiModelProperty(value = "系统备注，支持模糊查询")
    private String remark;

    @ApiModelProperty(value = "时间类型：1 下单时间、2 付款时间、3 审核时间、4 发货时间、5 完成时间、6 下账时间")
    private Integer timeType;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "开始时间")
    private String beginTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "结束时间")
    private String endTime;

    @ApiModelProperty(value = "系统订单状态:  5 待审方,10 待处理,20 待拣货,30 待配送,40 待收货,100 已完成,102 已取消,101 已关闭")
    private  List<Integer> orderStatus;

    @ApiModelProperty(value="下账状态: 30 待下帐  100 已下账  110 已取消")
    private Integer erpState;

    @ApiModelProperty(value = "订单类型：1 处方订单（处）、2 导入订单（导）、3 手工订单（手）、4 拆分订单（拆）、5.手工补发单（补）、6.邮件订单（邮）")
    private Integer orderType;

    @ApiModelProperty(value = "订单拆分状态：0 未拆单，1 拆分订单的源头订单，2 非源头的拆分订单. 3 拆分中间状态")
    private Integer splitStatus;

    @ApiModelProperty(value = "付款方式 1：在线支付，2：货到付款")
    private String payType;

    @ApiModelProperty(value="选地区: 使用path的数组 [湖南省-长沙市-岳麓区，湖北省-武汉市-洪山区]")
    private List<String> paths;

    /** */
    @ApiModelProperty(value="商户编码  用于查询传参 不需要作为前台入参" )
    private String merCode;

    @ApiModelProperty(value="服务商编码  用于查询传参 不需要作为前台入参")
    private String supplierCode;


    @JsonIgnore
    private Integer isPrescription;

    @ApiModelProperty(value = "快递公司")
    private Long expressId;



    @ApiModelProperty(value = "有无异常 1：有异常 0:无异常 null:全部")
    private Integer exStatus;

    @ApiModelProperty("1 有备注 2 无备注 0 查全部")
    private Integer remarkType = 0;

    @ApiModelProperty("备注信息")
    private String remarkText;


    private OrderTag tag;

    @ApiModelProperty(value = "传1代表新建售后单的退回退款类型,已此字段来过滤邮费单")
    private Integer returnType;

    @ApiModelProperty("推广门店集合")
    private List<String> spreadStoreCodes;

    @ApiModelProperty("供应商编码集合 无供应商的默认填空 履约方-商家履约则为0，服务商履约则为服务商编码")
    private List<String> supplierCodes;

    @ApiModelProperty(value="商户编码集合，用于服务商登录查询")
    private List<String> merCodes;
    @ApiModelProperty(value="订单类型 0为商户，1为供应商")
    private Integer orderOwnerType;

    @ApiModelProperty(value = "系统订单集合")
    private List<Long> omsOrderNoList;

    @ApiModelProperty("推广门店权限集合")
    private List<String> spreadPermissionStoreCodes;

    @ApiModelProperty("是否过滤邮费单")
    private Boolean isFilterPostFee;

    @ApiModelProperty(value = "异常类型")
    private Integer exceptionType;
}
