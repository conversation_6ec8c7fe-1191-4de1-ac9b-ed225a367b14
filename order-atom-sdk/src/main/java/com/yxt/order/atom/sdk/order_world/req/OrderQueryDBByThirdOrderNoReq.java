package com.yxt.order.atom.sdk.order_world.req;

import com.yxt.order.types.order_world.OrderQueryScaleEnum;
import com.yxt.order.types.order_world.ReturnQueryScaleEnum;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class OrderQueryDBByThirdOrderNoReq {

  @ApiModelProperty("三方单号")
  @NotBlank(message = "三方单号不能为空")
  private String thirdOrderNo;

  @ApiModelProperty("分表位置")
  @NotBlank(message = "分表位置不能为空")
  private String tableIndex;

  @ApiModelProperty("查询规模")
  private List<OrderQueryScaleEnum> qryScaleList;
}
