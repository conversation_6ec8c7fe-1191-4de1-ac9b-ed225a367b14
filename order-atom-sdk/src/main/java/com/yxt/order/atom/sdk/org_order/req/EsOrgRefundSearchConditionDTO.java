package com.yxt.order.atom.sdk.org_order.req;

import com.yxt.order.types.order.enums.RefundSearchTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "退单查询条件")
public class EsOrgRefundSearchConditionDTO {

  @ApiModelProperty("查询条件类型")
  private RefundSearchTypeEnum searchType;

  @ApiModelProperty("条件值")
  private String searchData;

}
