//package com.yxt.order.atom.sdk.inner;
//
//import java.io.Serializable;
//import java.util.Date;
//import lombok.Data;
//import lombok.NoArgsConstructor;
//import lombok.experimental.Accessors;
//
///**
// * <p>
// * 商家门店下账配置信息表
// * </p>
// * <AUTHOR> 2020-08-03 下账调整
// * @since 2020-08-03
// */
//@Data
//@NoArgsConstructor
//@Accessors(chain = true)
//public class StoreBillConfigResDto implements Serializable {
//
//	private static final long serialVersionUID = -5669763934755421015L;
//
//    private Long id;
//
//    /**
//     * 配置名称
//     */
//    private String name;
//
//    /**
//     * 企业编码
//     */
//    private String merCode;
//
//    /**
//     * 平台编码
//     */
//    private String platformCode;
//
//    /**
//     * 网店编码
//     */
//    private String clientCode;
//
//    /**
//     * 门店编码
//     */
//    private String storeCode;
//
//    /**
//     * 门店名称
//     */
//    private String storeName;
//    /**
//     * 配送费, 收取方式: 1-平台收取, 2-商家收取
//     */
//    private Integer freightFeeFetch;
//
//    /**
//     * 配送费, 是否下账: 0-不下账, 1-下账
//     */
//    private Boolean freightFeeInventory;
//
//    /**
//     * 包装费, 收取方式: 1-平台收取, 2-商家收取
//     */
//    private Integer packageFeeFetch;
//
//    /**
//     * 包装费, 是否下账: 0-不下账, 1-下账
//     */
//    private Boolean packageFeeInventory;
//
//    /**
//     * 商家优惠金额, 是否下账: 0-不下账, 1-下账
//     */
//    private Boolean mcDiscountInventory;
//
//    /**
//     * 商家优惠金额, 是否分摊: 0-下账商家优惠金额, 1-分摊至商品明细
//     */
//    private Boolean mcDiscountShare;
//
//    /**
//     * 平台优惠金额, 是否下账: 0-不下账, 1-下账
//     */
//    private Boolean ptfDiscountInventory;
//
//    /**
//     * 平台优惠金额, 是否分摊: 0-下账平台优惠金额, 1-分摊至商品明细
//     */
//    private Boolean ptfDiscountShare;
//
//    /**
//     * 平台收取佣金, 是否下账: 0-不下账, 1-下账
//     */
//    private Boolean ptfCommissionInventory;
//
//    /**
//     * 平台收取佣金, 是否分摊: 0-下账平台佣金, 1-分摊至商品明细
//     */
//    private Boolean ptfCommissionShare;
//
//    /**
//     * 商品明细优惠金额, 是否下账: 0-不下账, 1-下账
//     */
//    private Boolean mcDtlDiscountInventory;
//
//    /**
//     * 商品明细优惠金额, 是否分摊: 0-下账商品明细优惠金额, 1-分摊至商品明细
//     */
//    private Boolean mcDtlDiscountShare;
//
//    /**
//     * 收银员取值: 1-同拣货员, 2-ERP中获取
//     */
//    private Integer cashierSource;
//
//    /**
//     * 创建人
//     */
//    private String creator;
//
//    /**
//     * 创建时间
//     */
//    private Date createTime;
//
//    /**
//     * 末次修改时间
//     */
//    private Date modifyTime;
//
//    /**
//     * 配置类型（1:初始化配置 2:历史配置 3:当前使用配置）
//     */
//    private Integer confType;
//
//    /**
//     * 平台名称
//     */
//    private String platformName;
//
//    /**
//     * 是否开启收银员录入 1：开启 0：关闭）
//     */
//    private Integer checkerType;
//
//    /**
//     * 是否开启班次录入 1：开启 0：关闭
//     */
//    private Integer frequencyType;
//
//    /**
//     * 下账erp设置 1:下账至erp 0:不下账至erp
//     */
//    private Integer billErpSetting;
//
//    /**
//     * 是否按批次下账
//     */
//    private Integer isBatchNoStock;
//
//    /**
//     * 自动下账时机设置：0-配送出库后；1-拣货完成后，默认0
//     */
//    private Integer autoEnterAccountFlag;
//
//    /**
//     * 下账机构，0-下账至仓库绑定的下账门店、1-下账至推广门店，默认值为：0-下账至仓库绑定的下账门店
//     */
//    private Integer billOrganizationFlag;
//
//    /**
//     * 依据前端传递过来的下账配置信息, 构建存储数据库的实体对象
//     *
//     * @param configInfo 下账配置信息
//     * @return
//     */
////    public static StoreBillConfig buildEntity(StoreBillConfigInfo configInfo) {
////        StoreBillConfig entity = new StoreBillConfig()
////                .setFreightFeeFetch(configInfo.getFreightFee().getFeeRecipient()) // 配送费.费用收取方
////                .setFreightFeeInventory(configInfo.getFreightFee().getInventory()) // 配送费.是否下账
////                .setPackageFeeFetch(configInfo.getPackingCharge().getFeeRecipient()) // 包装费.费用收取方
////                .setPackageFeeInventory(configInfo.getPackingCharge().getInventory()) // 包装费.是否下账
////                .setMcDiscountInventory(configInfo.getMerchantDiscount().getInventory()) // 商家优惠信息.是否下账
////                .setMcDiscountShare(configInfo.getMerchantDiscount().getShare()) // 商家优惠信息.是否分摊
////                .setPtfDiscountInventory(configInfo.getPlatformDiscount().getInventory()) // 平台优惠信息.是否下账
////                .setPtfDiscountShare(configInfo.getPlatformDiscount().getShare()) // 平台优惠信息.是否分摊
////                .setMcDtlDiscountInventory(configInfo.getWareDetailDiscount().getInventory()) // 商品明细优惠信息.是否下账
////                .setMcDtlDiscountShare(configInfo.getWareDetailDiscount().getShare()) // 商品明细优惠信息.是否分摊
////                .setPtfCommissionInventory(configInfo.getPlatformCommission().getInventory()) // 平台收取佣金.是否下账
////                .setPtfCommissionShare(configInfo.getPlatformCommission().getShare()) // 平台收取佣金.是否分摊
////                .setCheckerType(configInfo.getCheckerType())
////                .setFrequencyType(configInfo.getFrequencyType())
////                .setBillErpSetting(configInfo.getBillErpSetting())
////                .setIsBatchNoStock(configInfo.getIsBatchNoStock())
////                ;
////
////
////        return entity;
////    }
//
//
//}
