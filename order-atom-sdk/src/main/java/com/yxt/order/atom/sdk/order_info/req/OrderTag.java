package com.yxt.order.atom.sdk.order_info.req;


import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 订单标记
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-09
 */
@Accessors(chain = true)
@Data
public class OrderTag {

    /**
     * 合单标记
     */
    private MergeTag mergeOrder;

    /**
     * 空单标记
     */
    private EmptyTag emptyOrder;

    /**
     * 物流回传成功标记,status为1成功
     */
    private ResendLogisticTag resendLogisticTag;
    /**
     * 预售单标记
     */
    private PreSellTag preSellOrder;


    /**
     * 修改地址标记
     */
    private ModifyAddressTag modifyAddressTag;

    /**
     * 物流拦截标识列表
     * */
    private List<LogisticsInterceptTag> logisticsInterceptTag = new ArrayList<>();

    /**
     * 物流修改地址标识
     * */
    private List<LogisticsUpAddressesTag> logisticsUpAddressesTag = new ArrayList<>();

    /**
     * 代发单标记
     */
    private AgentDeliveryTag agentDeliveryOrder;


    @Data
    public static class AbstractTag {
        /**
         * 标记状态  可自定义， 建议 0或null为无该标记，1为有该标记
         */
        Integer status;
    }

    @EqualsAndHashCode(callSuper = true)
    @Accessors(chain = true)
    @Data
    public static class MergeTag extends AbstractTag {
        // 合单状态   0或null为未合单，1为被合并，2为合并单

        /**
         * 合并后订单的系统订单号
         */
        Long mergedOmsOrderNo;
    }

    @EqualsAndHashCode(callSuper = true)
    @Accessors(chain = true)
    @Data
    public static class EmptyTag extends AbstractTag {
        /**
         * 策略快照版本id
         */
        Long configId;
    }

    @EqualsAndHashCode(callSuper = true)
    @Accessors(chain = true)
    @Data
    public static class ResendLogisticTag extends AbstractTag {
        public ResendLogisticTag(Integer status) {
            super.setStatus(status);
        }
    }

    @EqualsAndHashCode(callSuper = true)
    @Accessors(chain = true)
    @Data
    public static class PreSellTag extends AbstractTag {

    }

    @EqualsAndHashCode(callSuper = true)
    @Accessors(chain = true)
    @Data
    public static class  ModifyAddressTag extends  AbstractTag{
    }

    @EqualsAndHashCode(callSuper = true)
    @Accessors(chain = true)
    @Data
    public static class  LogisticsInterceptTag extends  AbstractTag{
        private String logisticsNo;
    }

    @EqualsAndHashCode(callSuper = true)
    @Accessors(chain = true)
    @Data
    public static class LogisticsUpAddressesTag extends  AbstractTag {
        private String logisticsNo;
    }

    @EqualsAndHashCode(callSuper = true)
    @Accessors(chain = true)
    @Data
    public static class  AgentDeliveryTag extends  AbstractTag{
    }
}
