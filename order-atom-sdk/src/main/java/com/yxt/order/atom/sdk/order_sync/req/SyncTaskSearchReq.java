package com.yxt.order.atom.sdk.order_sync.req;

import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class SyncTaskSearchReq {

  /**
   * 上下文id
   */
  private String contextId;

  /**
   * 任务类型
   */
  private String taskType;

  /**
   * 任务状态
   */
  private String taskState;

  /**
   * 当前时间,默认当前时间
   */
  private LocalDateTime currentTime = LocalDateTime.now();

  /**
   * 任务参数条件
   */
  private String taskParamCondition;

  /**
   * 查询数量
   */
  private Integer processBatchSize;

}
