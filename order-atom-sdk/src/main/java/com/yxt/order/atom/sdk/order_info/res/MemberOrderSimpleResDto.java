package com.yxt.order.atom.sdk.order_info.res;

import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 会员消费记录详细信息
 *
 * @author: moatkon
 * @time: 2024/12/10 16:36
 *
 * @apiNote  不含明细
 */
@Data
public class MemberOrderSimpleResDto {

  /**
   * 系统单号
   */
  private String orderNo;
  /**
   * 三方订单号
   */
  private String thirdOrderNo;

  /**
   * 订单状态 5待处理,10待接单,20待拣货,30待配送,
   * <p>
   * 40待收货,100已完成,102已取消,101已关闭
   */
  private Integer orderStatus;

  /**
   * 下单时间
   */
  private Date created;

  /**
   * 支付时间
   */
  private Date payTime;

  /**
   * 客户实付金额
   */
  private BigDecimal buyerActualAmount;

  /**
   * 运费金额
   */
  private BigDecimal deliveryFeeAmount;

  /**
   * 订单来源 ONLINE-线上订单 POS-线下订单
   */
  private String orderSource;

  /**
   * （下单）门店编码
   */
  private String storeCode;

  /**
   * （下单）门店名称
   */
  private String storeName;


  /**
   * 是否含退单
   */
  private Boolean hasRefundOrder;

  /**
   * 下账金额
   */
  private BigDecimal billAmount;

  private String cashier; // 收银员
  private String cashierName;// 收银员姓名


}
