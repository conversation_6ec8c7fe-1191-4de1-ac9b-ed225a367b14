//package com.yxt.order.atom.sdk.inner;
//
//import io.swagger.annotations.ApiModelProperty;
//import java.io.Serializable;
//import java.math.BigDecimal;
//import java.util.Date;
//import lombok.AllArgsConstructor;
//import lombok.Builder;
//import lombok.Data;
//import lombok.EqualsAndHashCode;
//import lombok.NoArgsConstructor;
//import lombok.experimental.Accessors;
//
///**
// * <p>
// * 支付信息表
// * </p>
// *
// * <AUTHOR>
// * @since 2019-12-17
// */
//@Data
//@EqualsAndHashCode(callSuper = false)
//@Accessors(chain = true)
//@Builder
//@AllArgsConstructor
//@NoArgsConstructor
//public class OrderPayInfoResDto implements Serializable {
//
//    private static final long serialVersionUID = 1L;
//
//    /**
//     * ID
//     */
//    private Long id;
//
//    /**
//     * 订单号
//     */
//    private Long orderNo;
//
//    /**
//     * 支付状态, 0未支付，1已支付
//     */
//    @ApiModelProperty(value = "支付状态, 0未支付，1已支付")
//    private String payStatus;
//
//    /**
//     * 支付方式,1是在线支付,2是货到付款吧
//     */
//    @ApiModelProperty(value = "支付方式,1是在线支付,2是货到付款吧")
//    private String payType;
//
//    @ApiModelProperty("ERP支付方式编码")
//    private String payCode;
//
//    /**
//     * 支付渠道，如微信支付
//     */
//    @ApiModelProperty(value = "支付渠道，如微信支付")
//    private String payChannel;
//
//    /**
//     * 客户实付
//     */
//    @ApiModelProperty(value = "客户实付")
//    private BigDecimal buyerActualAmount;
//
//    /**
//     * 商家实收
//     */
//    @ApiModelProperty(value = "商家实收")
//    private BigDecimal merchantActualAmount;
//
//    /**
//     * 商品总金额
//     */
//    @ApiModelProperty(value = "商品总金额")
//    private BigDecimal totalAmount;
//
//    /**
//     * 订单总优惠
//     */
//    @ApiModelProperty(value = "订单总优惠")
//    private BigDecimal totalDiscount;
//
//    /**
//     * 商家整单优惠
//     */
//    @ApiModelProperty(value = "商家整单优惠")
//    private BigDecimal merchantDiscountSum;
//
//    /**
//     * 商品明细折扣汇总（此值不使用）
//     */
//    @ApiModelProperty(value = "商品明细折扣汇总")
//    private BigDecimal discountFeeDtl;
//
//    /**
//     * 平台优惠
//     */
//    @ApiModelProperty(value = "平台优惠")
//    private BigDecimal platformDiscount;
//
//    /**
//     * 运费优惠
//     */
//    @ApiModelProperty(value = "运费优惠")
//    private BigDecimal postFeeDiscount;
//
//    /**
//     * 商家配送费
//     */
//    @ApiModelProperty(value = "商家配送费")
//    private BigDecimal merchantDeliveryFee;
//
//    /**
//     * 商家打包费
//     */
//    @ApiModelProperty(value = "商家打包费")
//    private BigDecimal merchantPackFee;
//
//    /**
//     * 平台配送费
//     */
//    @ApiModelProperty(value = "平台配送费")
//    private BigDecimal platformDeliveryFee;
//
//    /**
//     * 平台打包费
//     */
//    @ApiModelProperty(value = "平台打包费")
//    private BigDecimal platformPackFee;
//
//    /**
//     * 买家到付服务费
//     */
//    @ApiModelProperty(value = "买家到付服务费")
//    private BigDecimal buyerCodServiceFee;
//
//    /**
//     * 卖家到付服务费
//     */
//    @ApiModelProperty(value = "卖家到付服务费")
//    private BigDecimal sellerCodServiceFee;
//
//    /**
//     * 交易佣金
//     */
//    @ApiModelProperty(value = "交易佣金")
//    private BigDecimal brokerageAmount;
//
//    /**
//     * （买家）到付金额
//     */
//    @ApiModelProperty(value = "（买家）到付金额")
//    private BigDecimal buyerCodAmount;
//
//    /**
//     * 代收平台费
//     */
//    @ApiModelProperty(value = "代收平台费")
//    private BigDecimal platformFeeCollection;
//
//    /**
//     * 手工调整金额
//     */
//    @ApiModelProperty(value = "手工调整金额")
//    private BigDecimal manualFixAmount;
//
//    /**
//     * 商品明细折扣汇总
//     */
//    @ApiModelProperty(value = "商品明细折扣汇总")
//    private BigDecimal detailDiscountCollect;
//
//    /**
//     * 差异金额
//     */
//    @ApiModelProperty(value = "差异金额")
//    private BigDecimal differentAmount;
//
//    /**
//     * 创建时间
//     */
//    private Date createTime;
//
//    /**
//     * 修改时间
//     */
//    private Date modifyTime;
//
//    /**
//     * 配送费（记录原始值）
//     */
//    private BigDecimal deliveryFee;
//
//    /**
//     * 包装费（记录原始值）
//     */
//    private BigDecimal packFee;
//
//    /**
//     * 医保支付金额 由第三方平台直接返回
//     */
//    private BigDecimal medicareAmount;
//
//    /**
//     * 医保结算id 由第三方平台直接返回
//     */
//    private String medicareOrderId;
//
//    /**
//     * B2C系统订单-发货单
//     */
//    private Long omsOrderNo;
//
//    @ApiModelProperty("仓库待发费用")
//    private BigDecimal warehouseAgencyFee;
//
//    /**
//     * 健康贝数量
//     */
//    private Integer healthNum;
//
//    /**
//     * 健康贝换算金额
//     */
//    private BigDecimal healthValue;
//
//    @ApiModelProperty(value = "支付券信息")
//    private String paySaleInfo;
//    /**
//     * 平台配送费优惠金额
//     */
//    @ApiModelProperty("平台配送费优惠")
//    private BigDecimal platformDeliveryFeeDiscount;
//
//    /**
//     * 商家配送费优惠金额
//     */
//    @ApiModelProperty("商家配送费优惠")
//    private BigDecimal merchantDeliveryFeeDiscount;
//
//    /**
//     * 商家商品总优惠
//     */
//    @ApiModelProperty(value = "商家商品总优惠")
//    private BigDecimal merchantTotalDiscountSumNotDeliveryFee;
//
//    @ApiModelProperty(value = "商品优惠总额")
//    private BigDecimal totalDiscountSumNotDeliveryFee;
//
//    @ApiModelProperty(value = "应收金额")
//    private BigDecimal accountsReceivable;
//
//    @ApiModelProperty("平台商品优惠")
//    private BigDecimal platformDiscountSumNotDeliveryFee;
//}
