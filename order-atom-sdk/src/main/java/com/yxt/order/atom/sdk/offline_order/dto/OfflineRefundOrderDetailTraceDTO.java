package com.yxt.order.atom.sdk.offline_order.dto;

import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 心云退单明细追溯码信息
 */

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ToString
public class OfflineRefundOrderDetailTraceDTO {

  /**
   * 退款单号
   */
  private String refundNo;

  /**
   * 退款单明细唯一号
   */
  private String refundDetailNo;


  /**
   * 商品编码
   */
  private String erpCode;

  /**
   * 商品批号
   */
  private String makeNo;

  /**
   * 追溯码
   */
  private String traceCode;

  /**
   * 医保上报标识
   */
  private String nhsaReportFlag;

  /**
   * 药监上报标识
   */
  private String draReportFlag;

  /**
   * 平台创建时间
   */
  private Date created;

  /**
   * 平台更新时间
   */
  private Date updated;

  /**
   * 创建人
   */
  private String createdBy;

  /**
   * 更新人
   */
  private String updatedBy;

  /**
   * 系统创建时间
   */
  private Date sysCreateTime;

  /**
   * 系统更新时间
   */
  private Date sysUpdateTime;

  /**
   * 数据版本，每次update+1
   */
  private Long version;
}