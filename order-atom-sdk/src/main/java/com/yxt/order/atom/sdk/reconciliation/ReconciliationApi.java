package com.yxt.order.atom.sdk.reconciliation;

import static com.yxt.order.atom.sdk.OrderAtomServiceName.RECONCILIATION_ENDPOINT;

import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.atom.sdk.reconciliation.req.ReconciliationListReq;
import com.yxt.order.atom.sdk.reconciliation.req.ReconciliationReq;
import com.yxt.order.atom.sdk.reconciliation.res.ReconciliationListRes;
import com.yxt.order.atom.sdk.reconciliation.res.ReconciliationRes;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @author: moatkon
 * @time: 2025/4/23 16:13
 */
public interface ReconciliationApi {


  /**
   * 正单和退单一起对账
   *
   * @param
   */
  @PostMapping(RECONCILIATION_ENDPOINT + "/offline/union")
  ResponseBase<ReconciliationRes> offlineUnionReconciliation(
      @RequestBody @Valid ReconciliationReq req);

  /**
   * 获取对账列表
   * @param req
   * @return
   */
  @PostMapping(RECONCILIATION_ENDPOINT + "/offline/union/list")
  ResponseBase<ReconciliationListRes> offlineUnionListReconciliation( @RequestBody  @Valid  ReconciliationListReq req);



  /**
   * 正单对账
   *
   * @param
   */
  @PostMapping(RECONCILIATION_ENDPOINT + "/offline/order")
  ResponseBase<ReconciliationRes> offlineOrderReconciliation(
      @RequestBody @Valid ReconciliationReq req);


  /**
   * 退单对账
   *
   * @param req
   * @return
   */
  @PostMapping(RECONCILIATION_ENDPOINT + "/offline/refund")
  ResponseBase<ReconciliationRes> offlineRefundReconciliation(
      @RequestBody @Valid ReconciliationReq req);


}
