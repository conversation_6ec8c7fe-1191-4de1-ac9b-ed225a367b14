package com.yxt.order.atom.sdk.common.order_world;

import lombok.Data;

/**
 * 订单用户信息
 */
@Data
public class PlatformOrderUserDTO {

  private Long id;

  /**
   * 三方订单号
   */
  private String thirdOrderNo;

  /**
   * 三方平台
   */
  private String thirdPlatformCode;

  /**
   * 会员id
   */
  private String userId;

  /**
   * 发起人id，目前仅B2B有值
   */
  private String launchUserId;

  /**
   * 会员名称
   */
  private String userName;

  /**
   * 会员标记
   */
  private String userTag;

  /**
   * 会员卡号
   */
  private String userCardNo;

  /**
   * 会员手机号
   */
  private String userMobile;


}
