package com.yxt.order.atom.sdk.common.data;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年07月11日 15:38
 * @email: <EMAIL>
 */
@Data
public class OrderMultiPayInfoDTO implements Serializable {

  private static final long serialVersionUID = 3111593572192650622L;

  private Long id;

  @ApiModelProperty(value = "系统订单号")
  private Long orderNo;

  @ApiModelProperty(value = "退款单号")
  private Long refundNo;

  @ApiModelProperty(value = "支付编码,-对应ERP支付编码")
  private String payCode;

  @ApiModelProperty(value = "支付类型")
  private String payName;

  @ApiModelProperty(value = "支付类型对应的金额")
  private BigDecimal payPrice;

  @ApiModelProperty(value = "创建时间")
  private Date createTime;

  @ApiModelProperty(value = "末次修改时间")
  private Date modifyTime;


}