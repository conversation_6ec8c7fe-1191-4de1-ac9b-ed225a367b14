package com.yxt.order.atom.sdk.online_order.account;

import static com.yxt.order.atom.sdk.OrderAtomServiceName.ORDER_ENDPOINT;

import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.atom.sdk.online_order.account.dto.req.ApplyAccountOrderReqDto;
import com.yxt.order.atom.sdk.online_order.account.dto.req.ApplyAccountRefundOrderReqDto;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * Created by Intellij IDEA.
 *
 * <AUTHOR> Date:  2024/8/1
 */
public interface AccountOrderAtomCmdApi {


  @PostMapping(ORDER_ENDPOINT + "/account/applyOrderAccount")
  ResponseBase<Boolean> applyOrderAccount(
      @RequestBody ApplyAccountOrderReqDto reqDto);


  @PostMapping(ORDER_ENDPOINT + "/account/applyRefundOrderAccount")
  ResponseBase<Boolean> applyRefundOrderAccount(
      @RequestBody ApplyAccountRefundOrderReqDto reqDto);

}
