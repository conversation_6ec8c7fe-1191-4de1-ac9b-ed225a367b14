package com.yxt.order.atom.sdk.offline_order.dto;

import com.yxt.order.types.offline.NumberType;
import com.yxt.order.types.offline.StagingType;
import lombok.Data;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年08月13日 16:07
 * @email: <EMAIL>
 */
@Data
public class OfflineOrderStagingReqDto {

  /**
   * 是否是迁移
   */
  private Boolean migration;

  /**
   * 号类型 ORDER,REFUND
   */
  private NumberType numberType;

  /**
   * 暂存类型
   */
  private StagingType stagingType;

  /**
   * 原始数据
   */
  private String data;


  private String storeCode;
  private String thirdPlatformCode;
  private String thirdOrderNo; // numberType=ORDER
  private String thirdRefundNo;// numberType=REFUND
  private String defineNo; // 用于确定分表位置
  private String userCardNo;

}
