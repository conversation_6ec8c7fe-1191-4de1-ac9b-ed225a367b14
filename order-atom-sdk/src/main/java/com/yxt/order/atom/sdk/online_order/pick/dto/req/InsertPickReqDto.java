package com.yxt.order.atom.sdk.online_order.pick.dto.req;

import com.yxt.order.atom.sdk.common.data.OrderInfoDTO;
import com.yxt.order.atom.sdk.common.data.OrderPickInfoDTO;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年07月11日 15:29
 * @email: <EMAIL>
 */
@Data
public class InsertPickReqDto {

  /**
   * 是否删除旧数据
   */
  private Boolean deleteOldData;

  /**
   * 主单拣货信息
   */
  private List<OrderPickInfoDTO> orderPickInfoDTOList;
  /**
   * 主单信息
   */
  private OrderInfoDTO orderInfoDTO;
  /**
   * 运费单信息
   */
  private OrderInfoDTO freightOrderInfoDTO;



}
