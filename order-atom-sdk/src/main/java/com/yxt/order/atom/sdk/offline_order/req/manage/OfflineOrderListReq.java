package com.yxt.order.atom.sdk.offline_order.req.manage;

import com.yxt.lang.dto.PageBase;
import com.yxt.order.types.offline.enums.ThirdPlatformCodeEnum;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * @author: moatkon
 * @time: 2025/3/27 11:55
 */
@Data
public class OfflineOrderListReq extends PageBase {

  @ApiModelProperty("系统单号")
  private String orderNo;

  /**
   * @see ThirdPlatformCodeEnum
   */
  @ApiModelProperty("POS")
  private String thirdPlatformCode;

  @ApiModelProperty("平台单号")
  private String thirdOrderNo;

  @ApiModelProperty("门店")
  private List<String> storeCodeList;

  @ApiModelProperty("公司编码")
  private List<String> companyCodeList;

  @ApiModelProperty("下单开始时间")
  private Date createdStart;

  @ApiModelProperty("下单结束时间")
  private Date createdEnd;

  @ApiModelProperty("商品编码")
  private String erpCode;


}
