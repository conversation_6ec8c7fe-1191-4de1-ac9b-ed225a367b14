package com.yxt.order.atom.sdk.org_order.res;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;

@Data
public class EsOrgRefundResDTO {

  private String id;

  @ApiModelProperty("系统订单号")
  private String orderNo;

  @ApiModelProperty("三方订单号")
  private String thirdOrderNo;

  @ApiModelProperty("系统退单号")
  private String refundNo;

  @ApiModelProperty("三方退单号")
  private String thirdRefundNo;

  @ApiModelProperty("下单时间")
  private Date created;

  @ApiModelProperty("创建时间")
  private Date createTime;

  @ApiModelProperty("订单支付时间")
  private Date orderCreated;

  @ApiModelProperty("线上门店编码")
  private String storeCode;

  @ApiModelProperty("机构编码（线下实际发货门店）")
  private String orgCode;

  @ApiModelProperty("下单线上门店编码")
  private String sourceStoreCode;

  @ApiModelProperty("下单线下机构编码")
  private String sourceOrgCode;

  @ApiModelProperty("0, 待退款,20, 待退货,100, 已完成,102, 已拒绝,103, 已取消")
  private Integer refundStatus;

  @ApiModelProperty("下账状态 20, 待下账 99, 下账失败  100, 已下账 102, 已取消")
  private String erpStatus;

  @ApiModelProperty("下账时间")
  private Date erpTime;

  @ApiModelProperty("退款单零售流水")
  private String erpRefundNo;

  @ApiModelProperty("订单来源 ONLINE-线上订单 OFFLINE-线下订单")
  private String orderSource;

  @ApiModelProperty("平台")
  private String platformCode;

  @ApiModelProperty("退款类型, PART_REFUND-部分退款，REFUND_AMOUNT-全额退款")
  private String refundType;

  @ApiModelProperty("售后单类型 AFTER_SALE_AMOUNT-退款 、AFTER_SALE_GOODS-退货 、AFTER_SALE_AMOUNT_GOODS-退货退款")
  private String afterSaleType;

  @ApiModelProperty("退单标记")
  private List<String> refundFlags;

  @ApiModelProperty("下账金额")
  private BigDecimal refundBillAmount;

  @ApiModelProperty("退单明细")
  private List<EsOrgRefundDetailDTO> detailList;

  @ApiModelProperty("会员编码(唯一值)")
  private String userCardNo;

  @ApiModelProperty("会员ID (心云)")
  private String userId;

  @ApiModelProperty("门店类型 DIRECT_SALES-直营 JOIN-加盟")
  private String storeType;

  // 模型自由字段,其他表适配该字段
  // 默认0-未删除
  private Long deleted;

}
