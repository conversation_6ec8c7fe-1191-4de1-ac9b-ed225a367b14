package com.yxt.order.atom.sdk.online_order.merchant.res;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年09月14日 9:35
 * @email: <EMAIL>
 */
@Data
public class MerchantConfigRes {


  private Long id;

  @ApiModelProperty(value = "商户编码")
  private String merCode;

  /**
   * 是否调用ERP接口标识（0不调用 1调用）
   */
  @ApiModelProperty(value = "是否调用ERP接口标识（0不调用 1调用）")
  private Integer callErpFlag;

  /**
   * 货到付款是否下账: 0-不下账, 1-下账
   */
  @ApiModelProperty(value = "货到付款是否下账: 0-不下账, 1-下账")
  private Integer codBillFlag;

  /**
   * 同步美团订单：0.不同步，1.同步
   */
  private boolean syncMeituanBill;

  @ApiModelProperty(value = "货到付款是否下账: 0-不下账, 1-下账")
  private Integer accountCheckFlag;

  @ApiModelProperty(value = "平台对账接口是否授权(美团)，0-未授权，1-已授权")
  private Integer accountCheckMeituan;

  @ApiModelProperty(value = "平台对账接口是否授权(饿百)，0-未授权，1-已授权")
  private Integer accountCheckEle;

  @ApiModelProperty(value = "平台对账接口是否授权(京东到家)，0-未授权，1-已授权")
  private Integer accountCheckJd;

  private Date createTime;

  /**
   * 末次修改时间
   */
  private Date modifyTime;


}
