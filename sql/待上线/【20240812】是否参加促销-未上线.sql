ALTER TABLE `offline_order_0` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_2406` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_2407` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_2408` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_1` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_2` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_3` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_4` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_5` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_6` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_7` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_8` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_9` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_10` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_11` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_12` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_13` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_14` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_15` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_16` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_17` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_18` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_19` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_20` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_21` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_22` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_23` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_24` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_25` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_26` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_27` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_28` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_29` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_30` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_31` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_32` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_33` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_34` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_35` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_36` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_37` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_38` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_39` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_40` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_41` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_42` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_43` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_44` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_45` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_46` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_47` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_48` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_49` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_50` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_51` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_52` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_53` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_54` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_55` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_56` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_57` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_58` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_59` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_60` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_61` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_62` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_63` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_64` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_65` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_66` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_67` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_68` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_69` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_70` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_71` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_72` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_73` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_74` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_75` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_76` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_77` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_78` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_79` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_80` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_81` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_82` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_83` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_84` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_85` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_86` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_87` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_88` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_89` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_90` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_91` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_92` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_93` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_94` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_95` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_96` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_97` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_98` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_99` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_100` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_101` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_102` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_103` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_104` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_105` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_106` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_107` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_108` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_109` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_110` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_111` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_112` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_113` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_114` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_115` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_116` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_117` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_118` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_119` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_120` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_121` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_122` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_123` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_124` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_125` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_126` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_127` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_128` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_129` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_130` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_131` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_132` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_133` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_134` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_135` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_136` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_137` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_138` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_139` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_140` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_141` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_142` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_143` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_144` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_145` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_146` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_147` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_148` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_149` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_150` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_151` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_152` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_153` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_154` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_155` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_156` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_157` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_158` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_159` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_160` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_161` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_162` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_163` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_164` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_165` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_166` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_167` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_168` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_169` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_170` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_171` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_172` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_173` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_174` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_175` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_176` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_177` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_178` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_179` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_180` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_181` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_182` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_183` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_184` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_185` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_186` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_187` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_188` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_189` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_190` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_191` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_192` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_193` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_194` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_195` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_196` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_197` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_198` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_199` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_200` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_201` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_202` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_203` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_204` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_205` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_206` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_207` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_208` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_209` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_210` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_211` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_212` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_213` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_214` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_215` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_216` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_217` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_218` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_219` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_220` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_221` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_222` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_223` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_224` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_225` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_226` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_227` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_228` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_229` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_230` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_231` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_232` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_233` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_234` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_235` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_236` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_237` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_238` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_239` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_240` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_241` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_242` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_243` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_244` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_245` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_246` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_247` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_248` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_249` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_250` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_251` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_252` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_253` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_254` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_255` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';



ALTER TABLE `offline_order_detail_0` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_2406` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_2407` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_2408` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_1` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_2` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_3` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_4` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_5` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_6` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_7` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_8` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_9` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_10` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_11` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_12` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_13` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_14` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_15` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_16` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_17` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_18` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_19` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_20` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_21` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_22` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_23` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_24` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_25` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_26` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_27` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_28` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_29` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_30` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_31` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_32` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_33` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_34` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_35` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_36` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_37` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_38` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_39` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_40` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_41` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_42` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_43` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_44` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_45` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_46` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_47` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_48` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_49` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_50` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_51` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_52` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_53` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_54` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_55` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_56` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_57` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_58` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_59` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_60` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_61` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_62` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_63` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_64` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_65` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_66` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_67` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_68` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_69` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_70` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_71` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_72` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_73` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_74` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_75` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_76` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_77` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_78` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_79` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_80` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_81` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_82` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_83` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_84` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_85` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_86` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_87` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_88` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_89` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_90` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_91` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_92` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_93` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_94` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_95` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_96` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_97` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_98` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_99` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_100` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_101` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_102` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_103` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_104` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_105` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_106` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_107` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_108` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_109` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_110` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_111` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_112` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_113` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_114` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_115` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_116` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_117` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_118` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_119` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_120` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_121` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_122` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_123` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_124` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_125` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_126` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_127` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_128` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_129` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_130` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_131` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_132` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_133` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_134` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_135` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_136` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_137` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_138` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_139` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_140` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_141` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_142` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_143` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_144` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_145` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_146` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_147` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_148` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_149` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_150` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_151` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_152` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_153` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_154` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_155` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_156` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_157` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_158` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_159` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_160` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_161` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_162` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_163` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_164` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_165` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_166` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_167` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_168` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_169` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_170` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_171` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_172` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_173` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_174` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_175` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_176` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_177` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_178` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_179` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_180` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_181` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_182` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_183` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_184` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_185` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_186` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_187` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_188` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_189` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_190` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_191` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_192` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_193` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_194` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_195` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_196` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_197` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_198` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_199` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_200` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_201` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_202` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_203` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_204` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_205` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_206` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_207` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_208` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_209` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_210` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_211` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_212` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_213` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_214` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_215` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_216` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_217` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_218` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_219` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_220` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_221` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_222` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_223` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_224` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_225` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_226` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_227` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_228` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_229` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_230` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_231` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_232` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_233` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_234` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_235` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_236` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_237` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_238` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_239` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_240` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_241` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_242` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_243` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_244` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_245` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_246` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_247` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_248` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_249` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_250` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_251` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_252` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_253` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_254` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
ALTER TABLE `offline_order_detail_255` ADD COLUMN `is_on_promotion` varchar(6) NULL COMMENT '是否参加促销 TRUE、FALSE';
