package com.yxt.order.atom.order.es.sync.operate;

import com.yxt.common.logic.es.AbstractEsOperate;
import com.yxt.lang.util.JsonUtils;
import com.yxt.order.atom.order.es.doc.EsMemberRefundOrder;
import com.yxt.order.atom.order.es.mapper.EsMemberRefundOrderMapper;
import com.yxt.order.atom.order.es.sync.member_transaction.model.MemberRefundOrderModel;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.dromara.easyes.core.conditions.select.LambdaEsQueryWrapper;
import org.springframework.stereotype.Component;

/**
 * @author: moatkon
 * @time: 2024/12/10 15:03
 */
@Component
@Slf4j
public class MemberRefundOrderModelOperate extends AbstractEsOperate<MemberRefundOrderModel> {

  @Resource
  private EsMemberRefundOrderMapper esMemberRefundOrderMapper;

  public MemberRefundOrderModelOperate() {
    super(MemberRefundOrderModel.class);
  }

  @Override
  protected Boolean exec() {
    if (Type.DELETE.equals(getType())) {
      return delete(getT());
    } else if (Type.SAVE.equals(getType())) {
      return save(getT());
    }
    return false;
  }


  private Boolean delete(MemberRefundOrderModel memberRefundOrderModel) {
    return esMemberRefundOrderMapper.deleteById(memberRefundOrderModel.routeKey(),memberRefundOrderModel.defineId()) > 0;
  }

  private Boolean save(MemberRefundOrderModel memberRefundOrderModel) {
    EsMemberRefundOrder esMemberRefundOrder = memberRefundOrderModel.create();

    LambdaEsQueryWrapper<EsMemberRefundOrder> memberRefundOrderQuery = new LambdaEsQueryWrapper<>();
    memberRefundOrderQuery.eq(EsMemberRefundOrder::getId, memberRefundOrderModel.defineId());
    memberRefundOrderQuery.routing(memberRefundOrderModel.routeKey());
    Long count = esMemberRefundOrderMapper.selectCount(memberRefundOrderQuery);
    if (count > 0) {
      boolean update = esMemberRefundOrderMapper.updateById(memberRefundOrderModel.routeKey(),esMemberRefundOrder) > 0;
      if (!update) {
        log.warn("更新索引数据失败,{}", JsonUtils.toJson(esMemberRefundOrder));
      }
      return update;
    } else {
      boolean create = esMemberRefundOrderMapper.insert(memberRefundOrderModel.routeKey(),esMemberRefundOrder) > 0;
      if (!create) {
        log.warn("创建索引数据失败,{}", JsonUtils.toJson(esMemberRefundOrder));
      }
      return create;
    }
  }
}
