package com.yxt.order.atom.migration.fix.dto;

import com.fasterxml.jackson.core.type.TypeReference;
import com.yxt.lang.util.JsonUtils;
import com.yxt.order.atom.order.entity.DeletedDataDO;
import com.yxt.order.atom.order.entity.OfflineOrderDO;
import com.yxt.order.atom.order.entity.OfflineRefundOrderDO;
import com.yxt.order.types.canal.Table;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * @author: moatkon
 * @time: 2025/3/11 18:04
 */
@Data
public class RepeatedEsDataOperate {

  private String businessNo;
  private String tableName;
  private String userId;
  private OfflineOrderDO offlineOrderDO;
  private OfflineRefundOrderDO offlineRefundOrderDO;

  public RepeatedEsDataOperate(OfflineOrderDO offlineOrderDO) {
    this.tableName = Table.OFFLINE_ORDER_REGEX;
    this.offlineOrderDO = offlineOrderDO;
    this.businessNo = this.offlineOrderDO.getOrderNo();
    this.userId = this.offlineOrderDO.getUserId();
  }

  public RepeatedEsDataOperate(OfflineRefundOrderDO offlineRefundOrderDO) {
    this.tableName = Table.OFFLINE_REFUND_ORDER_REGEX;
    this.offlineRefundOrderDO = offlineRefundOrderDO;
    this.businessNo = this.offlineRefundOrderDO.getRefundNo();
    this.userId = this.offlineRefundOrderDO.getUserId();
  }

  public RepeatedEsDataOperate(DeletedDataDO deletedDataDO) {
    this.businessNo = deletedDataDO.getBusinessNo();
    this.tableName = deletedDataDO.getTableName();
    String deletedData = deletedDataDO.getDeletedData();
    if (offlineOrderTable()) {
      this.offlineOrderDO = JsonUtils.toObject(deletedData, new TypeReference<OfflineOrderDO>() {
      });
      this.userId = this.offlineOrderDO.getUserId();
    }
    if (offlineRefundOrderTable()) {
      this.offlineRefundOrderDO = JsonUtils.toObject(deletedData,
          new TypeReference<OfflineRefundOrderDO>() {
          });
      this.userId = this.offlineRefundOrderDO.getUserId();
    }
  }

  public String esMemberOrderId() {
    return orderEsId();
  }

  public String esMemberOrderIdRouting() {
    return offlineOrderDO.getUserId();
  }


  public String esMemberRefundOrderId() {
    return refundOrderEsId();
  }

  public String esMemberRefundOrderIdRouting() {
    return offlineRefundOrderDO.getUserId();
  }


  public String esOrderId() {
    if (StringUtils.isEmpty(businessNo)) {
      throw new RuntimeException("businessNo can not be null");
    }
    return businessNo; // 正单是orderNo,退单是refundNo,所以直接去businessNo就行了
  }

  public String orgOrderId() {
    return orderEsId();
  }
  public String orgOrderIdRouting() {
    return offlineOrderDO.getStoreCode();
  }

  public String orgRefundOrderId() {
    return refundOrderEsId();
  }
  public String orgRefundOrderIdRouting() {
    return offlineRefundOrderDO.getStoreCode();
  }


  public Boolean offlineOrderTable() {
    return Table.OFFLINE_ORDER_REGEX.equals(tableName);
  }

  public Boolean offlineRefundOrderTable() {
    return Table.OFFLINE_REFUND_ORDER_REGEX.equals(tableName);
  }

  private String orderEsId() {
    return offlineOrderDO.getThirdPlatformCode() + "-" + offlineOrderDO.getOrderNo();
  }

  private String refundOrderEsId() {
    return offlineRefundOrderDO.getThirdPlatformCode() + "-" + offlineRefundOrderDO.getRefundNo();
  }

}
