package com.yxt.order.atom.order.repository.abstracts.order.update;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yxt.order.atom.order.entity.OrderDetailCommodityCostPriceDO;
import com.yxt.order.atom.order.repository.abstracts.order.AbstractUpdate;
import com.yxt.order.atom.order.repository.batch.OrderDetailCommodityCostPriceBatchRepository;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

@Component
public class OrderDetailCommodityCostPriceUpdate  extends AbstractUpdate<List<OrderDetailCommodityCostPriceDO>> {


  @Resource
  private OrderDetailCommodityCostPriceBatchRepository orderDetailCommodityCostPriceBatchRepository;
  
  @Override
  protected Boolean canUpdate() {
    return CollUtil.isNotEmpty(req.getOrderDetailCommodityCostPriceList());
  }

  @Override
  protected Integer update(List<OrderDetailCommodityCostPriceDO> orderDetailCommodityCostPriceDOS) {
    List<Long> orderNoList = orderDetailCommodityCostPriceDOS.stream().map(OrderDetailCommodityCostPriceDO::getOrderNo).collect(Collectors.toList());
    QueryWrapper<OrderDetailCommodityCostPriceDO> queryWrapper = new QueryWrapper<>();
    queryWrapper.lambda().in(OrderDetailCommodityCostPriceDO::getOrderNo,orderNoList);
    List<OrderDetailCommodityCostPriceDO> existDataList = orderDetailCommodityCostPriceBatchRepository.list(queryWrapper);
    if(CollectionUtils.isEmpty(existDataList)){
      existDataList = Collections.emptyList();
    }
    Map<String,OrderDetailCommodityCostPriceDO> uniqueKeyEntityMap = existDataList.stream().collect(Collectors.toMap(OrderDetailCommodityCostPriceDO::uniqueKey, a->a,(v1, v2)->v1));
    List<OrderDetailCommodityCostPriceDO> updateDataList = orderDetailCommodityCostPriceDOS.stream().filter(data->uniqueKeyEntityMap.containsKey(data.uniqueKey())).collect(Collectors.toList());
    List<OrderDetailCommodityCostPriceDO> insertDataList = orderDetailCommodityCostPriceDOS.stream().filter(data->!uniqueKeyEntityMap.containsKey(data.uniqueKey())).collect(Collectors.toList());
    if(!CollectionUtils.isEmpty(updateDataList)){
      updateDataList.forEach(data->{
        OrderDetailCommodityCostPriceDO existEntity =uniqueKeyEntityMap.get(data.uniqueKey());
        data.setId(existEntity.getId());
        if(req.getNeedUpdateCostFlag()) {
          data.setAveragePrice(existEntity.getAveragePrice());
        }else{
          data.setBatchNo(existEntity.getBatchNo());
          data.setMakeNo(existEntity.getMakeNo());
          data.setCostPrice(existEntity.getCostPrice());
        }
      });
      orderDetailCommodityCostPriceBatchRepository.updateBatchById(updateDataList);
    }
    if(!CollectionUtils.isEmpty(insertDataList)){
      orderDetailCommodityCostPriceBatchRepository.saveBatch(insertDataList);
    }
    return updateDataList.size() + insertDataList.size();
  }

  /**
   * 转换成目标对象T
   *
   * @return
   */
  @Override
  protected List<OrderDetailCommodityCostPriceDO> convert() {
    return BeanUtil.copyToList(req.getOrderDetailCommodityCostPriceList(), OrderDetailCommodityCostPriceDO.class);
  }
}
