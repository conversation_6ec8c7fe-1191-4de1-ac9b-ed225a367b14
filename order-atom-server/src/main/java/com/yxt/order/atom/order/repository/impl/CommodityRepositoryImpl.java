package com.yxt.order.atom.order.repository.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yxt.order.atom.order.entity.CommodityStockDO;
import com.yxt.order.atom.order.mapper.CommodityStockMapper;
import com.yxt.order.atom.order.repository.CommodityRepository;
import com.yxt.order.atom.sdk.online_order.commodity.req.CommodityStockQueryReq;
import com.yxt.order.atom.sdk.online_order.commodity.res.CommodityStockRes;
import org.springframework.stereotype.Repository;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年09月14日 10:17
 * @email: <EMAIL>
 */
@Repository
public class CommodityRepositoryImpl implements CommodityRepository {

    @Resource
    private CommodityStockMapper commodityStockMapper;

    @Override
    public List<CommodityStockRes> queryCommodityStock(CommodityStockQueryReq req) {
        Assert.isTrue(!StringUtils.isEmpty(req.getOrderNo()), "orderNo can not empty");
        LambdaQueryWrapper<CommodityStockDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CommodityStockDO::getOrderNo, req.getOrderNo());
        return BeanUtil.copyToList(commodityStockMapper.selectList(queryWrapper),
                CommodityStockRes.class);
    }

}
