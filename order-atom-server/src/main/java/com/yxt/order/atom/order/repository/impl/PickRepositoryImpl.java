package com.yxt.order.atom.order.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yxt.order.atom.common.enmus.OrderAtomErrorCode;
import com.yxt.order.atom.order.converter.OrderInfo2DOConverter;
import com.yxt.order.atom.order.converter.OrderPickInfo2DOConverter;
import com.yxt.order.atom.order.entity.OrderInfoDO;
import com.yxt.order.atom.order.entity.OrderPickInfoDO;
import com.yxt.order.atom.order.mapper.OrderInfoMapper;
import com.yxt.order.atom.order.mapper.OrderPickInfoMapper;
import com.yxt.order.atom.order.repository.PickRepository;
import com.yxt.order.atom.sdk.online_order.pick.dto.req.InsertPickReqDto;
import com.yxt.order.common.YxtPreconditions;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.support.TransactionTemplate;

/**
 * Created by Intellij IDEA.
 *
 * <AUTHOR> Date:  2024/8/8
 */
@Slf4j
@Repository
public class PickRepositoryImpl implements PickRepository {


  @Autowired
  OrderPickInfoMapper orderPickInfoMapper;

  @Autowired
  OrderInfoMapper orderInfoMapper;


  @Autowired
  private TransactionTemplate transactionTemplate;

  @Override

  public Boolean insertPick(InsertPickReqDto req) {

    try {
      OrderPickInfo2DOConverter pickInfoInstance = OrderPickInfo2DOConverter.INSTANCE;
      List<OrderPickInfoDO> pickInfoList = pickInfoInstance.toDO(req.getOrderPickInfoDTOList());

      OrderInfo2DOConverter orderInfoInstance = OrderInfo2DOConverter.INSTANCE;
      OrderInfoDO orderInfoDO = orderInfoInstance.toDO(req.getOrderInfoDTO());
      OrderInfoDO freightOrderInfoDTO = orderInfoInstance.toDO(req.getFreightOrderInfoDTO());

      OrderInfoDO updateOrderInfoDO = buildUpdateOrderInfo(orderInfoDO);
      OrderInfoDO updateFreightOrderInfo = buildUpdateFreightOrderInfo(freightOrderInfoDTO);

      insertPickTransaction(req.getDeleteOldData(), pickInfoList, updateOrderInfoDO,
          updateFreightOrderInfo);
    } catch (Exception e) {
      log.error("Error inserting pick info", e);
      return Boolean.FALSE;
    }
    return Boolean.TRUE;
  }


  private OrderInfoDO buildUpdateOrderInfo(OrderInfoDO orderInfoDO) {
    OrderInfoDO updateOrderInfoDO = new OrderInfoDO();
    updateOrderInfoDO.setOrderNo(orderInfoDO.getOrderNo());
    updateOrderInfoDO.setErpAdjustNo("");
    updateOrderInfoDO.setErpState(orderInfoDO.getErpState());
    updateOrderInfoDO.setOrderPickType(orderInfoDO.getOrderPickType());
    updateOrderInfoDO.setOrderState(orderInfoDO.getOrderState());
    updateOrderInfoDO.setPickerId(orderInfoDO.getPickerId());
    updateOrderInfoDO.setPickerName(orderInfoDO.getPickerName());
    updateOrderInfoDO.setPickOperatorId(orderInfoDO.getPickOperatorId());
    updateOrderInfoDO.setPickOperatorName(orderInfoDO.getPickOperatorName());
    updateOrderInfoDO.setPickTime(orderInfoDO.getPickTime());
    updateOrderInfoDO.setDataVersion(orderInfoDO.getDataVersion());
    return updateOrderInfoDO;
  }

  private OrderInfoDO buildUpdateFreightOrderInfo(OrderInfoDO freightOrderInfoDTO) {
    if (null == freightOrderInfoDTO) {
      return null;
    }
    OrderInfoDO updateFreightOrderInfo = new OrderInfoDO();
    updateFreightOrderInfo.setOrderNo(freightOrderInfoDTO.getOrderNo());
    updateFreightOrderInfo.setErpAdjustNo(freightOrderInfoDTO.getErpAdjustNo());
    updateFreightOrderInfo.setErpState(freightOrderInfoDTO.getErpState());
    updateFreightOrderInfo.setOrderPickType(freightOrderInfoDTO.getOrderPickType());
    updateFreightOrderInfo.setPickerId(freightOrderInfoDTO.getPickerId());
    updateFreightOrderInfo.setPickerName(freightOrderInfoDTO.getPickerName());
    updateFreightOrderInfo.setPickOperatorId(freightOrderInfoDTO.getPickOperatorId());
    updateFreightOrderInfo.setPickOperatorName(freightOrderInfoDTO.getPickOperatorName());
    updateFreightOrderInfo.setPickTime(freightOrderInfoDTO.getPickTime());
    updateFreightOrderInfo.setDataVersion(freightOrderInfoDTO.getDataVersion());
    return updateFreightOrderInfo;
  }


  private void insertPickTransaction(Boolean deleteOldData, List<OrderPickInfoDO> pickInfoList,
      OrderInfoDO updateOrderInfoDO, OrderInfoDO freightOrderInfoDTO) {

    transactionTemplate.execute(status -> {
      if (Boolean.TRUE.equals(deleteOldData)) {
        List<Long> orderDetailIds = pickInfoList.stream().map(OrderPickInfoDO::getOrderDetailId)
            .collect(Collectors.toList());
        LambdaQueryWrapper<OrderPickInfoDO> orderPickInfoWrapper = Wrappers.lambdaQuery();
        orderPickInfoWrapper.in(OrderPickInfoDO::getOrderDetailId, orderDetailIds);
        orderPickInfoMapper.delete(orderPickInfoWrapper);
      }
      //插入拣货信息
      pickInfoList.forEach(orderPickInfo -> orderPickInfoMapper.insert(orderPickInfo));
      //更新主单信息
      LambdaQueryWrapper<OrderInfoDO> orderInfoWrapper = Wrappers.lambdaQuery();
      orderInfoWrapper.eq(OrderInfoDO::getOrderNo, updateOrderInfoDO.getOrderNo());
      orderInfoWrapper.eq(OrderInfoDO::getDataVersion, updateOrderInfoDO.getDataVersion());
      int update = orderInfoMapper.update(updateOrderInfoDO, orderInfoWrapper);
      YxtPreconditions.checkArgument(update > 0, OrderAtomErrorCode.DB_FOR_UPDATE_FAIL);
      if (null != freightOrderInfoDTO) {
        //更新运费单信息
        LambdaQueryWrapper<OrderInfoDO> freightOrderInfoWrapper = Wrappers.lambdaQuery();
        freightOrderInfoWrapper.eq(OrderInfoDO::getOrderNo, freightOrderInfoDTO.getOrderNo());
        freightOrderInfoWrapper.eq(OrderInfoDO::getDataVersion,
            freightOrderInfoDTO.getDataVersion());
        int updateFreight = orderInfoMapper.update(freightOrderInfoDTO, freightOrderInfoWrapper);
        YxtPreconditions.checkArgument(updateFreight > 0, OrderAtomErrorCode.DB_FOR_UPDATE_FAIL);
      }

      return true;
    });


  }


}
