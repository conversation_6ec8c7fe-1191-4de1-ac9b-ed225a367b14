package com.yxt.order.atom.order.repository;

import com.yxt.order.atom.sdk.common.data.ErpRefundInfoDTO;
import com.yxt.order.atom.sdk.common.data.RefundDetailDTO;
import com.yxt.order.atom.sdk.common.data.RefundOrderDTO;
import com.yxt.order.atom.sdk.online_order.refund_info.dto.req.ErpRefundQueryReqDto;
import com.yxt.order.atom.sdk.online_order.refund_info.dto.req.RefundAuditOptionalReq;
import com.yxt.order.atom.sdk.online_order.refund_info.dto.req.RefundDetailInsertReq;
import com.yxt.order.atom.sdk.online_order.refund_info.dto.req.RefundInfoQryBatchReqDto;
import com.yxt.order.atom.sdk.online_order.refund_info.dto.req.RefundInfoQryReqDto;
import com.yxt.order.atom.sdk.online_order.refund_info.dto.req.RefundLogOptionalReq;
import com.yxt.order.atom.sdk.online_order.refund_info.dto.req.RefundWithOrderQryReqDto;
import com.yxt.order.atom.sdk.online_order.refund_info.dto.res.RefundInfoQryResDto;
import java.util.List;

public interface RefundRepository {

  RefundInfoQryResDto queryRefundInfo(RefundInfoQryReqDto request);

  RefundOrderDTO queryLatestRefundInfo(RefundWithOrderQryReqDto request);

  void saveRefundAuditResult(RefundAuditOptionalReq request);

  void saveRefundLog(RefundLogOptionalReq request);

  List<ErpRefundInfoDTO> queryErpRefund(ErpRefundQueryReqDto request);

  List<RefundDetailDTO> createRefundDetail(RefundDetailInsertReq request);

  List<RefundInfoQryResDto> queryRefundInfoBatch(RefundInfoQryBatchReqDto request);
}
