package com.yxt.order.atom.order.es.sync;

import com.yxt.common.logic.flash.AbstractFlashData;
import com.yxt.common.logic.flash.Scene;
import com.yxt.order.atom.common.sharding.OfflineOrderHit;
import com.yxt.order.atom.common.sharding.OfflineOrderHit.QueryHit;
import com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm;
import com.yxt.order.atom.common.utils.OrderDateUtils;
import com.yxt.order.atom.common.utils.RedisStringUtil;
import com.yxt.order.atom.common.utils.ShardingUtils;
import com.yxt.order.types.utils.ShardingHelper;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import joptsimple.internal.Strings;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.shardingsphere.api.hint.HintManager;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * 抽象刷数逻辑
 *
 * @param <Source>        源数据类型
 * @param <TargetData>    Canal数据\自定义数据等
 * @param <BusinessScene> 业务场景类型（仅作为语义标记）
 * @author: moatkon
 * @time: 2024/12/13 10:01
 */
@Slf4j
public abstract class AbstractFlash<Source, TargetData, BusinessScene extends Scene> extends
    AbstractFlashData<Source, TargetData, BusinessScene> {

  protected Boolean isSharding() {
    return Boolean.FALSE;
  }

  /**
   * 是否处理非会员数据
   *
   * @return
   */
  protected Boolean isHandleNonVipData() {
    return Boolean.FALSE;
  }

  @Override
  protected void flash() {
    try {
      if (isSharding()) {
        shardingExec();
      } else {
        innerExec();
      }
    } finally {
      customDataThreadLocal.remove();
    }
  }

  @Data
  public static class CustomData {
    private List<String> shardingValueList;
    private Long startId;
    private Long endId;
    private String suffix;
    private Boolean onlyHandleKeChuan = false; // 只处理科传

    public String removeEsDataMonitorKey(){
      return String.format("%s_%s_%s",suffix,startId,endId);
    }

  }

  /**
   * 自定义分表集合
   * note: 如果后期字段多了，可以拓展为DTO
   */
  private final ThreadLocal<CustomData> customDataThreadLocal = new ThreadLocal<>();
  public void customData(CustomData customData) {
    this.customDataThreadLocal.set(customData);
  }

  protected CustomData getCustomData(){
   return customDataThreadLocal.get();
  }

  /**
   * 分表集合
   *
   * @return
   */
  protected List<String> shardingValueList() {
    CustomData customData = getCustomData();
    if(Objects.nonNull(customData)){
      List<String> shardingValueList = customData.getShardingValueList();
      if (!CollectionUtils.isEmpty(shardingValueList)) {
        return shardingValueList;
      }
    }

    return ShardingUtils.shardingValueList(isHandleNonVipData());
  }

  /**
   * 分表执行
   */
  protected void shardingExec() {
    List<String> shardingValueList = shardingValueList();

    String no = Strings.EMPTY;
    List<String> noList = getFlashParam().getNoList();
    if (!CollectionUtils.isEmpty(noList) && noList.size() == 1) {
      String tableIndexByNo = ShardingHelper.getTableIndexByNo(noList.get(0));
      if (shardingValueList.contains(tableIndexByNo)) {
        no = noList.get(0);
      } else {
        log.info("不处理,忽略，{}", noList);
        return;
      }
    }

    for (String shardingValue : shardingValueList) {
      try (HintManager hintManager = HintManager.getInstance()) {
        OfflineOrderHit hit = new OfflineOrderHit();
        if (!StringUtils.isEmpty(no)) {
          hit.setDefineNo(no);
        } else {
          hit.setQueryHit(QueryHit.builder().seq(shardingValue).build());
        }
        OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);
        log.info("flash sharding value:{}", StringUtils.isEmpty(no) ? shardingValue : no);

        innerExec();
      }

      // 指定了单号,就跳出
      if (!StringUtils.isEmpty(no)) {
        break;
      }
    }
  }

  @Override
  protected void monitor(String tips) {
    if (!StringUtils.isEmpty(getFlashParam().getMonitorKey())) {
      String flashKey = flashKey();
      String message = tips + OrderDateUtils.formatYYMMDD(new Date());

      String redisMonitorKey = String.format("%s_%s", getFlashParam().getMonitorKey(), flashKey);
      log.info("{}-{}", redisMonitorKey, message);
      RedisStringUtil.setValue(redisMonitorKey, message, 30L, TimeUnit.DAYS);
    }
  }


  private String flashKey() {
    String key = String.format("Flash_%s", this.getClass().getName());
    if (!CollectionUtils.isEmpty(getFlashParam().getNoList())) {
      key = key + "_" + getFlashParam().getNoList().get(0);// 取第一个简单标识一下
    }
    return key;
  }


}


