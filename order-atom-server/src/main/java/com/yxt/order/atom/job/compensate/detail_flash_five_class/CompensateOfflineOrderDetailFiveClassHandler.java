package com.yxt.order.atom.job.compensate.detail_flash_five_class;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yxt.order.atom.migration.service.MigrationCommonComponent;
import com.yxt.order.atom.migration.service.dto.GoodsInfo;
import com.yxt.order.atom.order.entity.OfflineOrderDetailDO;
import com.yxt.order.atom.order.es.sync.AbstractFlash;
import com.yxt.order.atom.order.mapper.OfflineOrderDetailMapper;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * 补偿订单明细五级分类-线下单明细
 *
 * @author: moatkon
 * @time: 2025/2/10 11:20
 */
@Component
public class CompensateOfflineOrderDetailFiveClassHandler extends
    AbstractFlash<OfflineOrderDetailDO, OfflineOrderDetailDO, FlashDetailFiveClass> {

  @Resource
  private OfflineOrderDetailMapper offlineOrderDetailMapper;

  @Resource
  private MigrationCommonComponent migrationCommonComponent;


  @Value("${flashFiveClassLimit:500}")
  private Integer flashFiveClassLimit;

  @Override
  protected Long queryCursorStartId() {
    return offlineOrderDetailMapper.selectMinId(getFlashParam());
  }

  @Override
  protected Long queryCursorEndId() {
    return offlineOrderDetailMapper.selectMaxId(getFlashParam());
  }

  @Override
  protected List<OfflineOrderDetailDO> getSourceList() {
    LambdaQueryWrapper<OfflineOrderDetailDO> query = new LambdaQueryWrapper<>();
    query.ge(OfflineOrderDetailDO::getId, getFlashParam().getCursorStartId());
    query.lt(OfflineOrderDetailDO::getId, getFlashParam().getCursorStartId() + defaultLimit());
    return offlineOrderDetailMapper.selectList(query);
  }

  @Override
  protected List<OfflineOrderDetailDO> assembleTargetData(
      List<OfflineOrderDetailDO> offlineOrderDetailDOS) {
    return offlineOrderDetailDOS;
  }

  @Override
  protected void flash(List<OfflineOrderDetailDO> offlineOrderDetailDOS) {

    if (CollectionUtils.isEmpty(offlineOrderDetailDOS)) {
      return;
    }

    Set<String> erpCodeSet = offlineOrderDetailDOS.stream()
        .filter(CompensateOfflineOrderDetailFiveClassHandler::missFiveClass)
        .map(OfflineOrderDetailDO::getErpCode).collect(Collectors.toSet());
    if(CollectionUtils.isEmpty(erpCodeSet)){
      return;
    }

    Map<String, GoodsInfo> goodsInfoMap = migrationCommonComponent.getGoodsInfo(erpCodeSet);

    for (OfflineOrderDetailDO offlineOrderDetailDO : offlineOrderDetailDOS) {
      if (!missFiveClass(offlineOrderDetailDO) || Objects.isNull(offlineOrderDetailDO.getId())) {
        continue;
      }

      GoodsInfo goodsInfo = goodsInfoMap.get(offlineOrderDetailDO.getErpCode());
      if(Objects.isNull(goodsInfo)){
        continue;
      }


      offlineOrderDetailDO.setFiveClass(goodsInfo.getFiveClass());
      offlineOrderDetailDO.setFiveClassName(goodsInfo.getFiveClassName());
      offlineOrderDetailMapper.updateById(offlineOrderDetailDO);
    }

  }

  private static boolean missFiveClass(OfflineOrderDetailDO offlineOrderDetailDO) {
    return StringUtils.isEmpty(offlineOrderDetailDO.getFiveClass()) || StringUtils.isEmpty(
        offlineOrderDetailDO.getFiveClassName());
  }

  @Override
  protected Boolean isSharding() {
    return Boolean.TRUE;
  }


  @Override
  protected Integer defaultLimit() {
    return flashFiveClassLimit;
  }
}
