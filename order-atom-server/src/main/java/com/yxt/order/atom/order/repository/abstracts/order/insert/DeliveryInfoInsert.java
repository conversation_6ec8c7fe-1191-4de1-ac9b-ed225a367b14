package com.yxt.order.atom.order.repository.abstracts.order.insert;

import com.yxt.order.atom.order.entity.OrderInfoDO;
import com.yxt.order.atom.order.mapper.SelfGetDeliveryInfoMapper;
import com.yxt.order.atom.order.repository.abstracts.order.AbstractInsert;
import java.util.Objects;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年09月13日 14:56
 * @email: <EMAIL>
 */
@Component
public class DeliveryInfoInsert extends AbstractInsert<OrderInfoDO> {

  @Resource
  private SelfGetDeliveryInfoMapper selfGetDeliveryInfoMapper;

  @Override
  protected Boolean canInsert() {
    return Objects.nonNull(saveDataOptional.getSaveDeliveryInfo());
  }

  @Override
  protected Integer insert(OrderInfoDO orderInfoDO) {
    return selfGetDeliveryInfoMapper.saveDeliveryInfo(orderInfoDO);
  }

  @Override
  protected OrderInfoDO data() {
    return saveDataOptional.getSaveDeliveryInfo();
  }


}
