package com.yxt.order.atom.migration.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yxt.order.atom.common.LocalConst.DATA_SOURCE;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年04月15日 16:40
 * @email: <EMAIL>
 */
@DS(DATA_SOURCE.ORDER_OFFLINE)
@Mapper
public interface ModifyPlatformExistsDOMapper extends BaseMapper<ModifyPlatformExistsDO> {

}
