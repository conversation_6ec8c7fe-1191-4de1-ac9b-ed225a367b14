package com.yxt.order.atom.order_sync.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yxt.order.atom.common.LocalConst.DATA_SOURCE;
import com.yxt.order.atom.order_sync.entity.OrderSyncMappingDO;
import com.yxt.order.atom.order_sync.mapper.OrderSyncMappingMapper;
import com.yxt.order.atom.sdk.common.order_world.OrderSyncMappingDTO;
import com.yxt.order.atom.sdk.order_sync.req.OrderSyncMappingSearchReq;
import java.util.ArrayList;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@DS(DATA_SOURCE.ORDER_OFFLINE)
public class OrderSyncService {

  @Autowired
  private OrderSyncMappingMapper orderSyncMappingMapper;

  /**
   * 查询业务单号同步记录
   *
   * @param request 查询条件
   * @return 同步记录列表
   */
  public List<OrderSyncMappingDTO> orderSyncMappingSearch(OrderSyncMappingSearchReq request) {
    // 构建查询条件
    LambdaQueryWrapper<OrderSyncMappingDO> queryWrapper = new LambdaQueryWrapper<>();

    // 根据请求参数添加查询条件
    if (StrUtil.isNotBlank(request.getOriginBusinessNo())) {
      queryWrapper.eq(OrderSyncMappingDO::getOriginBusinessNo, request.getOriginBusinessNo());
    }

    if (StrUtil.isNotBlank(request.getTargetBusinessNo())) {
      queryWrapper.eq(OrderSyncMappingDO::getTargetBusinessNo, request.getTargetBusinessNo());
    }

    if (StrUtil.isNotBlank(request.getThirdBusinessNo())) {
      queryWrapper.eq(OrderSyncMappingDO::getThirdBusinessNo, request.getThirdBusinessNo());
    }

    if (StrUtil.isNotBlank(request.getThirdPlatformCode())) {
      queryWrapper.eq(OrderSyncMappingDO::getThirdPlatformCode, request.getThirdPlatformCode());
    }

    if (StrUtil.isNotBlank(request.getOrgCode())) {
      queryWrapper.eq(OrderSyncMappingDO::getOrgCode, request.getOrgCode());
    }

    if (StrUtil.isNotBlank(request.getBusinessType())) {
      queryWrapper.eq(OrderSyncMappingDO::getBusinessType, request.getBusinessType());
    }

    if (StrUtil.isNotBlank(request.getSyncType())) {
      queryWrapper.eq(OrderSyncMappingDO::getSyncType, request.getSyncType());
    }

    // 按创建时间倒序排序
    queryWrapper.orderByDesc(OrderSyncMappingDO::getCreatedTime);

    // 查询数据库
    List<OrderSyncMappingDO> mappingList = orderSyncMappingMapper.selectList(queryWrapper);
    if (CollUtil.isEmpty(mappingList)) {
      return new ArrayList<>(0);
    }

    // 转换为DTO对象
    return BeanUtil.copyToList(mappingList, OrderSyncMappingDTO.class);
  }

  public void orderSyncMappingSaveOrUpdate(OrderSyncMappingDTO request) {

    OrderSyncMappingDO orderSyncMappingDO = BeanUtil.toBean(request, OrderSyncMappingDO.class);
    if (ObjectUtil.isNull(orderSyncMappingDO.getId())) {
      orderSyncMappingMapper.insert(orderSyncMappingDO);
    } else {
      orderSyncMappingMapper.updateById(orderSyncMappingDO);
    }
  }
}
