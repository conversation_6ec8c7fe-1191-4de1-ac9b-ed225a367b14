package com.yxt.order.atom.order.repository.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yxt.order.atom.order.entity.MerchantConfigDO;
import com.yxt.order.atom.order.mapper.MerchantConfigMapper;
import com.yxt.order.atom.order.repository.MerchantConfigRepository;
import com.yxt.order.atom.sdk.online_order.merchant.req.MerchantConfigReq;
import com.yxt.order.atom.sdk.online_order.merchant.res.MerchantConfigRes;
import javax.annotation.Resource;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年09月14日 9:41
 * @email: <EMAIL>
 */
@Repository
public class MerchantConfigRepositoryImpl implements MerchantConfigRepository {

  @Resource
  private MerchantConfigMapper merchantConfigMapper;

  @Override
  public MerchantConfigRes queryMerchantConfig(MerchantConfigReq req) {
    Wrapper<MerchantConfigDO> queryWrapper = Wrappers.<MerchantConfigDO>lambdaQuery()
        .eq(MerchantConfigDO::getMerCode, req.getMerCode());
    MerchantConfigDO merchantConfigDO = merchantConfigMapper.selectOne(queryWrapper);
    return BeanUtil.copyProperties(merchantConfigDO, MerchantConfigRes.class);
  }
}
