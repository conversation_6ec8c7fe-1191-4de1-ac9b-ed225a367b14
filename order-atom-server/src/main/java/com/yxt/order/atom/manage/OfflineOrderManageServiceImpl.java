package com.yxt.order.atom.manage;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.google.common.collect.Lists;
import com.yxt.lang.dto.api.PageDTO;
import com.yxt.order.atom.common.LocalConst.DATA_SOURCE;
import com.yxt.order.atom.common.utils.OrderDateUtils;
import com.yxt.order.atom.order.es.doc.EsOfflineOrderManage;
import com.yxt.order.atom.order.es.doc.EsOfflineOrderManageDetail;
import com.yxt.order.atom.order.es.doc.EsOfflineRefundOrderManage;
import com.yxt.order.atom.order.es.doc.EsOfflineRefundOrderManageDetail;
import com.yxt.order.atom.order.es.mapper.EsOfflineOrderManageMapper;
import com.yxt.order.atom.order.es.mapper.EsOfflineRefundOrderManageMapper;
import com.yxt.order.atom.order.repository.OfflineOrderRepository;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineOrderDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineOrderOrganizationDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineRefundOrderDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineRefundOrderOrganizationDTO;
import com.yxt.order.atom.sdk.offline_order.req.OfflineOrderDetailReqDto;
import com.yxt.order.atom.sdk.offline_order.req.OfflineRefundOrderDetailReqDto;
import com.yxt.order.atom.sdk.offline_order.req.manage.OfflineOrderListReq;
import com.yxt.order.atom.sdk.offline_order.req.manage.OfflineRefundOrderListReq;
import com.yxt.order.atom.sdk.offline_order.res.OfflineOrderDetailResDto;
import com.yxt.order.atom.sdk.offline_order.res.OfflineRefundOrderDetailResDto;
import com.yxt.order.atom.sdk.offline_order.res.manage.OfflineOrderRes;
import com.yxt.order.atom.sdk.offline_order.res.manage.OfflineRefundOrderRes;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.dromara.easyes.core.biz.EsPageInfo;
import org.dromara.easyes.core.conditions.select.LambdaEsQueryWrapper;
import org.dromara.easyes.core.toolkit.FieldUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * @author: moatkon
 * @time: 2025/4/1 14:12
 */
@Service
@Slf4j
public class OfflineOrderManageServiceImpl implements OfflineOrderManageService {
  private final String offlineOrderManageDetailList = FieldUtils.val(EsOfflineOrderManage::getOfflineOrderManageDetailList);

  private final String offlineRefundOrderManageDetailList = FieldUtils.val(
      EsOfflineRefundOrderManage::getEsOfflineRefundOrderManageDetailList);

  @Resource
  private EsOfflineOrderManageMapper esOfflineOrderManageMapper;

  @Resource
  private EsOfflineRefundOrderManageMapper esOfflineRefundOrderManageMapper;

  @Resource
  private OfflineOrderRepository offlineOrderRepository;


  @Override
  @DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
  public PageDTO<OfflineOrderRes> offlineOrderList(OfflineOrderListReq req) {
    LambdaEsQueryWrapper<EsOfflineOrderManage> queryWrapper = new LambdaEsQueryWrapper<>();
    queryWrapper.eq(StringUtils.isNotEmpty(req.getOrderNo()),EsOfflineOrderManage::getOrderNo,req.getOrderNo());
    queryWrapper.eq(StringUtils.isNotEmpty(req.getThirdPlatformCode()),EsOfflineOrderManage::getThirdPlatformCode,req.getThirdPlatformCode());
    queryWrapper.eq(StringUtils.isNotEmpty(req.getThirdOrderNo()),EsOfflineOrderManage::getThirdOrderNo,req.getThirdOrderNo());
    queryWrapper.in(!CollectionUtils.isEmpty(req.getStoreCodeList()),EsOfflineOrderManage::getStoreCode,req.getStoreCodeList());
    queryWrapper.in(!CollectionUtils.isEmpty(req.getCompanyCodeList()),EsOfflineOrderManage::getCompanyCode,req.getCompanyCodeList());
    queryWrapper.ge(Objects.nonNull(req.getCreatedStart()),EsOfflineOrderManage::getCreated,
        OrderDateUtils.formatYYMMDD(req.getCreatedStart()));
    queryWrapper.le(Objects.nonNull(req.getCreatedEnd()),EsOfflineOrderManage::getCreated,OrderDateUtils.formatYYMMDD(req.getCreatedEnd()));
    String erpCode = req.getErpCode();
    if(StringUtils.isNotEmpty(erpCode)){
      queryWrapper.nested(offlineOrderManageDetailList,
          item -> item.eq(
              String.format("%s.%s.keyword", offlineOrderManageDetailList,
                  FieldUtils.val(EsOfflineOrderManageDetail::getErpCode)), erpCode));
    }

    // 排序
    queryWrapper.orderByDesc(EsOfflineOrderManage::getCreated);
    EsPageInfo<EsOfflineOrderManage> esPage = esOfflineOrderManageMapper.pageQuery(
        queryWrapper, req.getCurrentPage().intValue(), req.getPageSize().intValue());

    List<OfflineOrderRes> resList = Lists.newArrayList();
    if (esPage.getTotal() != 0L) {
      for (EsOfflineOrderManage order : esPage.getList()) {
        OfflineOrderDetailReqDto detailReqDto = new OfflineOrderDetailReqDto();
        detailReqDto.setOrderNo(order.getOrderNo());
        OfflineOrderDetailResDto detail = offlineOrderRepository.detail(detailReqDto);
        if (Objects.isNull(detail)) {
          log.warn("订单【{}】不存在", order.getOrderNo());
          continue;
        }

        OfflineOrderDTO offlineOrderDTO = detail.getOfflineOrderDTO();
        OfflineOrderOrganizationDTO offlineOrderOrganizationDTO = detail.getOfflineOrderOrganizationDTO();
        String storeName = null;
        if (Objects.nonNull(offlineOrderOrganizationDTO)) {
          storeName = offlineOrderOrganizationDTO.getStoreName();
        }

        OfflineOrderRes res = new OfflineOrderRes();
        res.setOrderNo(offlineOrderDTO.getOrderNo());
        res.setThirdPlatformCode(offlineOrderDTO.getThirdPlatformCode());
        res.setThirdOrderNo(offlineOrderDTO.getThirdOrderNo());
        res.setOfflineOrderState(offlineOrderDTO.getOrderState());
        res.setCreated(offlineOrderDTO.getCreated());
        res.setActualPayAmount(offlineOrderDTO.getActualPayAmount());
        res.setActualCollectAmount(offlineOrderDTO.getActualCollectAmount());
        res.setStoreCode(offlineOrderDTO.getStoreCode());
        res.setStoreName(storeName);
        resList.add(res);
      }
    }
    PageDTO<OfflineOrderRes> pageRes = new PageDTO<>();
    pageRes.setTotalCount(esPage.getTotal());
    pageRes.setTotalPage((long) esPage.getPages());
    pageRes.setData(resList);
    pageRes.setCurrentPage(req.getCurrentPage());
    pageRes.setPageSize(req.getPageSize());
    return pageRes;
  }

  @Override
  @DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
  public PageDTO<OfflineRefundOrderRes> offlineRefundOrderList(OfflineRefundOrderListReq req) {
    LambdaEsQueryWrapper<EsOfflineRefundOrderManage> queryWrapper = new LambdaEsQueryWrapper<>();
    queryWrapper.eq(StringUtils.isNotEmpty(req.getRefundNo()),EsOfflineRefundOrderManage::getRefundNo,req.getRefundNo());
    queryWrapper.eq(StringUtils.isNotEmpty(req.getOrderNo()),EsOfflineRefundOrderManage::getOrderNo,req.getOrderNo());
    queryWrapper.eq(StringUtils.isNotEmpty(req.getThirdPlatformCode()),EsOfflineRefundOrderManage::getThirdPlatformCode,req.getThirdPlatformCode());
    queryWrapper.eq(StringUtils.isNotEmpty(req.getThirdRefundNo()),EsOfflineRefundOrderManage::getThirdRefundNo,req.getThirdRefundNo());
    queryWrapper.eq(StringUtils.isNotEmpty(req.getThirdOrderNo()),EsOfflineRefundOrderManage::getThirdOrderNo,req.getThirdOrderNo());
    queryWrapper.eq(StringUtils.isNotEmpty(req.getRefundType()),EsOfflineRefundOrderManage::getRefundType,req.getRefundType());
    queryWrapper.eq(StringUtils.isNotEmpty(req.getAfterSaleType()),EsOfflineRefundOrderManage::getAfterSaleType,req.getAfterSaleType());
    queryWrapper.eq(StringUtils.isNotEmpty(req.getStoreDirectJoinType()),EsOfflineRefundOrderManage::getStoreDirectJoinType,req.getStoreDirectJoinType());
    queryWrapper.ge(Objects.nonNull(req.getCreatedStart()),EsOfflineRefundOrderManage::getCreated,OrderDateUtils.formatYYMMDD(req.getCreatedStart()));
    queryWrapper.le(Objects.nonNull(req.getCreatedEnd()),EsOfflineRefundOrderManage::getCreated,OrderDateUtils.formatYYMMDD(req.getCreatedEnd()));
    queryWrapper.in(!CollectionUtils.isEmpty(req.getStoreCodeList()),EsOfflineRefundOrderManage::getStoreCode,req.getStoreCodeList());
    queryWrapper.in(!CollectionUtils.isEmpty(req.getCompanyCodeList()),EsOfflineRefundOrderManage::getCompanyCode,req.getCompanyCodeList());
    String erpCode = req.getErpCode();
    if(StringUtils.isNotEmpty(erpCode)){
      queryWrapper.nested(offlineRefundOrderManageDetailList,
          item -> item.eq(
              String.format("%s.%s.keyword", offlineRefundOrderManageDetailList,
                  FieldUtils.val(EsOfflineRefundOrderManageDetail::getErpCode)), erpCode));
    }

    // 排序
    queryWrapper.orderByDesc(EsOfflineRefundOrderManage::getCreated);
    EsPageInfo<EsOfflineRefundOrderManage> esPage = esOfflineRefundOrderManageMapper.pageQuery(
        queryWrapper, req.getCurrentPage().intValue(), req.getPageSize().intValue());

    List<OfflineRefundOrderRes> refundResList = Lists.newArrayList();
    if (esPage.getTotal() != 0L) {
      for (EsOfflineRefundOrderManage refund : esPage.getList()) {
        OfflineRefundOrderDetailReqDto detailReqDto = new OfflineRefundOrderDetailReqDto();
        detailReqDto.setRefundNo(refund.getRefundNo());
        OfflineRefundOrderDetailResDto refundDetail = offlineOrderRepository.refundDetail(
            detailReqDto);
        if (Objects.isNull(refundDetail)) {
          log.warn("退单【{}】不存在", refund.getOrderNo());
          continue;
        }

        OfflineRefundOrderDTO offlineOrderDTO = refundDetail.getOfflineRefundOrderDTO();
        OfflineRefundOrderOrganizationDTO offlineOrderOrganizationDTO = refundDetail.getOfflineRefundOrderOrganizationDTO();
        String storeName = null;
        if (Objects.nonNull(offlineOrderOrganizationDTO)) {
          storeName = offlineOrderOrganizationDTO.getStoreName();
        }

        OfflineRefundOrderRes refundRes = new OfflineRefundOrderRes();
        refundRes.setRefundNo(offlineOrderDTO.getRefundNo());
        refundRes.setOrderNo(offlineOrderDTO.getOrderNo());
        refundRes.setThirdPlatformCode(offlineOrderDTO.getThirdPlatformCode());
        refundRes.setThirdRefundNo(offlineOrderDTO.getThirdRefundNo());
        refundRes.setThirdOrderNo(offlineOrderDTO.getThirdOrderNo());
        refundRes.setRefundType(offlineOrderDTO.getRefundType());
        refundRes.setAfterSaleType(offlineOrderDTO.getAfterSaleType());
        refundRes.setRefundState(offlineOrderDTO.getRefundState());
        refundRes.setCreated(offlineOrderDTO.getCreated());
        refundRes.setConsumerRefund(offlineOrderDTO.getConsumerRefund());
        refundRes.setStoreCode(offlineOrderDTO.getStoreCode());
        refundRes.setStoreName(storeName);
        refundResList.add(refundRes);
      }
    }
    PageDTO<OfflineRefundOrderRes> pageRes = new PageDTO<>();
    pageRes.setTotalCount(esPage.getTotal());
    pageRes.setTotalPage((long) esPage.getPages());
    pageRes.setData(refundResList);
    pageRes.setCurrentPage(req.getCurrentPage());
    pageRes.setPageSize(req.getPageSize());
    return pageRes;
  }

  @Override
  public Boolean createOfflineOrderManageIndex() {
    return esOfflineOrderManageMapper.createIndex();
  }

  @Override
  public Boolean createOfflineRefundOrderManageIndex() {
    return esOfflineRefundOrderManageMapper.createIndex();
  }
}
