package com.yxt.order.atom.order.repository.abstracts.order.update;

import cn.hutool.core.bean.BeanUtil;
import com.yxt.order.atom.order.entity.OrderDetailDO;
import com.yxt.order.atom.order.entity.OrderPickInfoDO;
import com.yxt.order.atom.order.repository.abstracts.order.AbstractUpdate;
import com.yxt.order.atom.order.repository.batch.OrderDetailBatchRepository;
import com.yxt.order.atom.order.repository.batch.OrderPickInfoBatchRepository;
import com.yxt.order.atom.sdk.common.data.OrderDetailDTO;
import com.yxt.order.atom.sdk.common.data.OrderPickInfoDTO;
import java.util.List;
import javax.annotation.Resource;
import lombok.Data;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年09月13日 18:08
 * @email: <EMAIL>
 */
@Component
public class OrderPickInfoUpdate extends AbstractUpdate<List<OrderPickInfoDO>> {

  @Resource
  private OrderPickInfoBatchRepository orderPickInfoBatchRepository;

  @Override
  protected Boolean canUpdate() {
    List<OrderPickInfoDTO> orderPickDtoList = req.getOrderPickDtoList();
    if (CollectionUtils.isEmpty(orderPickDtoList)) {
      return false;
    }

    // 强制校验Id
    orderPickDtoList.forEach(dto -> Assert.isTrue(!StringUtils.isEmpty(dto.getId()), "id can not null"));

    return Boolean.TRUE;
  }

  @Override
  protected Integer update(List<OrderPickInfoDO> list) {
    return orderPickInfoBatchRepository.updateBatchById(list) ? list.size() : 0;
  }

  @Override
  protected List<OrderPickInfoDO> convert() {
    return BeanUtil.copyToList(req.getOrderPickDtoList(), OrderPickInfoDO.class);
  }
}