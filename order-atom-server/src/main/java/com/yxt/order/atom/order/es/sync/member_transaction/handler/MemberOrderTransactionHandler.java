package com.yxt.order.atom.order.es.sync.member_transaction.handler;

import com.google.common.collect.Lists;
import com.yxt.common.logic.canal.AbstractCanalHandler;
import com.yxt.order.atom.common.utils.OrderDateUtils;
import com.yxt.order.atom.order.entity.OrderDetailDO;
import com.yxt.order.atom.order.es.components.SyncComponent;
import com.yxt.order.atom.order.es.sync.clean.ExpireDaysConstant;
import com.yxt.order.atom.order.es.sync.data.CanalOrderInfo;
import com.yxt.order.atom.order.es.sync.data.CanalOrderInfo.Order;
import com.yxt.order.atom.order.es.sync.member_transaction.dp.MemberOrderSource;
import com.yxt.order.atom.order.es.sync.member_transaction.dp.MemberOrderStatus;
import com.yxt.order.atom.order.es.sync.member_transaction.model.MemberOrderModel;
import com.yxt.order.atom.order.es.sync.member_transaction.model.MemberOrderModel.MemberOrderDetailModel;
import com.yxt.order.atom.order.es.sync.member_transaction.utils.OnlineOrderStoreTypeUtils;
import com.yxt.order.types.canal.Database;
import com.yxt.order.types.canal.Table;
import com.yxt.order.types.order.enums.PlatformCodeEnum;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * @author: moatkon
 * @time: 2024/12/9 11:49
 */
@Component
public class MemberOrderTransactionHandler extends
    AbstractCanalHandler<CanalOrderInfo, MemberOrderModel> {
  @Resource
  private SyncComponent syncComponent;
  public MemberOrderTransactionHandler() {
    super(CanalOrderInfo.class);
  }

  @Override
  protected Boolean check() {
    String database = getData().getDatabase();
    String table = getData().getTable();
    return Database.DSCLOUD.equals(database) && table.equals(Table.ORDER_INFO);
  }

  @Override
  protected List<MemberOrderModel> assemble() {
    List<Order> orderList = getData().getData();
    if (CollectionUtils.isEmpty(orderList)) {
      return Lists.newArrayList();
    }
    return orderList.stream()
        // 过滤userId
        .filter(this::efficientData)
        .map(order -> {
          String userId = syncComponent.queryUserId(order.getMemberNo());

          MemberOrderModel memberOrderModel = new MemberOrderModel();
          memberOrderModel.setUserId(userId);
          memberOrderModel.setCreated(order.getCreated());
          memberOrderModel.setCreateTime(order.getCreateTime());
          memberOrderModel.setStoreCode(order.getOrganizationCode());
          memberOrderModel.setUserCardNo(order.getMemberNo());
          memberOrderModel.setOrderSource(MemberOrderSource.ONLINE.name());
          memberOrderModel.setPlatformCode(String.valueOf(PlatformCodeEnum.getByCode(order.getThirdPlatformCode())));
          memberOrderModel.setOrderStatus(MemberOrderStatus.getByStatus(order.getOrderState()));
          memberOrderModel.setThirdOrderNo(order.getThirdOrderNo());
          memberOrderModel.setOrderNo(order.getOrderNo());
          // memberOrderModel.setCompanyCode(); // 无
          memberOrderModel.setStoreType(OnlineOrderStoreTypeUtils.storeType(order.getOrganizationCode()).name());
          memberOrderModel.setDeleted(order.getDeleted());

          List<OrderDetailDO> detailList = syncComponent.getOrderDetailListByOrderNo(
              Lists.newArrayList(order.getOrderNo()));
          if(!CollectionUtils.isEmpty(detailList)){
            memberOrderModel.setOrderDetailModelList(detailList.stream()
                .map(item -> {
                  MemberOrderDetailModel detailModel = new MemberOrderDetailModel();
                  detailModel.setErpCode(item.getErpCode());
                  detailModel.setErpName(item.getCommodityName());
                  return detailModel;
                })
                .collect(Collectors.toList())
            );
          }
          return memberOrderModel;
        }).collect(Collectors.toList());
  }

  public Boolean efficientData(Order order){
    return
        !OrderDateUtils.isExpired(order.getCreated(), ExpireDaysConstant.EsMemberOrderEfficientDays)
        && !StringUtils.isEmpty(syncComponent.queryUserId(order.getMemberNo()));
  }
}
