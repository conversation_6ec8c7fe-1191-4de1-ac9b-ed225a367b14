package com.yxt.order.atom.order.repository.abstracts.order.insert;

import com.yxt.order.atom.order.entity.OrderPayInfoDO;
import com.yxt.order.atom.order.mapper.OrderPayInfoMapper;
import com.yxt.order.atom.order.repository.abstracts.order.AbstractInsert;
import java.util.Objects;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年09月13日 14:57
 * @email: <EMAIL>
 */
@Component
public class OrderPayInfoInsert extends AbstractInsert<OrderPayInfoDO> {

  @Resource
  private OrderPayInfoMapper orderPayInfoMapper;

  @Override
  protected Boolean canInsert() {
    return Objects.nonNull(saveDataOptional.getOrderPayInfo());
  }

  @Override
  protected Integer insert(OrderPayInfoDO orderPayInfoDO) {
    return orderPayInfoMapper.insert(orderPayInfoDO);
  }

  @Override
  protected OrderPayInfoDO data() {
    return saveDataOptional.getOrderPayInfo();
  }


}
