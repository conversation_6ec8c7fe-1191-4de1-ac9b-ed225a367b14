package com.yxt.order.atom.order.es.doc;

import com.yxt.order.types.offline.enums.AfterSaleTypeEnum;
import com.yxt.order.types.offline.enums.RefundTypeEnum;
import com.yxt.order.types.offline.enums.StoreDirectJoinTypeEnum;
import com.yxt.order.types.offline.enums.ThirdPlatformCodeEnum;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;
import org.dromara.easyes.annotation.IndexField;
import org.dromara.easyes.annotation.IndexId;
import org.dromara.easyes.annotation.IndexName;
import org.dromara.easyes.annotation.rely.FieldType;
import org.dromara.easyes.annotation.rely.IdType;

/**
 * @author: moatkon
 * @time: 2025/3/27 18:26
 */
@Data
@IndexName(value = "es_offline_refund_order_manage", keepGlobalPrefix = true, aliasName = "alias_es_offline_refund_order_manage")
public class EsOfflineRefundOrderManage {

  @IndexId(type = IdType.CUSTOMIZE)
  private String id;

  /**
   * 系统退款单号
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String refundNo;

  /**
   * 系统订单号
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String orderNo;

  /**
   * POS
   *
   * @see ThirdPlatformCodeEnum
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String thirdPlatformCode;

  /**
   * 平台订单号
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String thirdOrderNo;

  /**
   * 平台退款单号
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String thirdRefundNo;

  /**
   * 退款类型
   *
   * @see RefundTypeEnum
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String refundType;

  /**
   * 售后类型
   *
   * @see AfterSaleTypeEnum
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String afterSaleType;

  /**
   * 门店直营加盟类型
   *
   * @see StoreDirectJoinTypeEnum
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String storeDirectJoinType;

  /**
   * 门店
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String storeCode;

  /**
   * 退款时间
   */
  @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
  private Date created;


  @IndexField(fieldType = FieldType.NESTED, nestedClass = EsOfflineRefundOrderManageDetail.class)
  private List<EsOfflineRefundOrderManageDetail> esOfflineRefundOrderManageDetailList;

  /**
   * 公司编码
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String companyCode;

  @IndexField(fieldType = FieldType.SCALED_FLOAT)
  private BigDecimal consumerRefund;


}
