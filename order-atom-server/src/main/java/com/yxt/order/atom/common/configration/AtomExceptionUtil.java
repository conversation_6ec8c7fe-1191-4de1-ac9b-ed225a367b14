package com.yxt.order.atom.common.configration;


import com.yxt.lang.exception.YxtBizException;
import com.yxt.order.atom.sdk.AtomSdkErrorCode;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2019/12/23 16:25
 */
public class AtomExceptionUtil {

  public static YxtBizException getWarnException(AtomSdkErrorCode dsErrorType) {
    return YxtBizException.builder().code(String.valueOf(dsErrorType.getSdkErrorCode()))
        .message(dsErrorType.getMsg())
        .build();
  }

  public static YxtBizException getWarnException(String code, String msg) {
    return YxtBizException.builder().code(code).message(msg).build();
  }


}
