package com.yxt.order.atom.order.es.sync.data;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.yxt.common.logic.canal.BaseCanalData;
import com.yxt.order.atom.order.es.sync.data.CanalOfflineRefundOrderDetail.OfflineRefundOrderDetail;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年08月20日 14:06
 * @email: <EMAIL>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CanalOfflineRefundOrderDetail extends BaseCanalData<OfflineRefundOrderDetail> {

  @Data
  public static class OfflineRefundOrderDetail {
    @JsonProperty("refund_no")
    private String refundNo;
  }
}

/*


{
    "data": [
        {
            "id": "1",
            "order_no": "",
            "refund_no": "2816194481456662407",
            "parent_refund_no": null,
            "user_id": "",
            "third_platform_code": "KE_CHUAN-O",
            "third_refund_no": "S010718646",
            "parent_third_refund_no": null,
            "third_order_no": "S010718646",
            "refund_type": "UNKOWN",
            "after_sale_type": "AFTER_SALE_AMOUNT_GOODS",
            "refund_state": "REFUNDED",
            "reason": "",
            "created": "2024-07-30 18:22:46",
            "apply_time": "2024-07-30 18:22:46",
            "complete_time": "2024-07-30 18:22:44",
            "bill_time": "2024-07-30 18:22:44",
            "total_amount": "19.900000",
            "shop_refund": "19.900000",
            "consumer_refund": "19.900000",
            "created_by": "",
            "updated_by": "",
            "created_time": "2024-07-30 18:22:44",
            "updated_time": "2024-08-20 13:52:53",
            "version": "1",
            "serial_no": null,
            "store_code": "A831",
            "migration": "true",
            "is_on_promotion": null
        }
    ],
    "database": "dscloud_offline",
    "es": 1724133173000,
    "id": 68,
    "isDdl": false,
    "mysqlType": {
        "id": "bigint unsigned",
        "order_no": "varchar(64)",
        "refund_no": "varchar(255)",
        "parent_refund_no": "varchar(64)",
        "user_id": "varchar(32)",
        "third_platform_code": "varchar(32)",
        "third_refund_no": "varchar(255)",
        "parent_third_refund_no": "varchar(32)",
        "third_order_no": "varchar(32)",
        "refund_type": "varchar(255)",
        "after_sale_type": "varchar(32)",
        "refund_state": "varchar(10)",
        "reason": "varchar(512)",
        "created": "datetime",
        "apply_time": "datetime",
        "complete_time": "datetime",
        "bill_time": "datetime",
        "total_amount": "decimal(16,6)",
        "shop_refund": "decimal(16,6)",
        "consumer_refund": "decimal(16,6)",
        "created_by": "varchar(64)",
        "updated_by": "varchar(64)",
        "created_time": "datetime",
        "updated_time": "datetime",
        "version": "bigint",
        "serial_no": "varchar(64)",
        "store_code": "varchar(32)",
        "migration": "varchar(6)",
        "is_on_promotion": "varchar(6)"
    },
    "old": [
        {
            "third_platform_code": "KE_CHUAN",
            "updated_time": "2024-08-19 10:42:01"
        }
    ],
    "pkNames": [
        "id"
    ],
    "sql": "",
    "sqlType": {
        "id": -5,
        "order_no": 12,
        "refund_no": 12,
        "parent_refund_no": 12,
        "user_id": 12,
        "third_platform_code": 12,
        "third_refund_no": 12,
        "parent_third_refund_no": 12,
        "third_order_no": 12,
        "refund_type": 12,
        "after_sale_type": 12,
        "refund_state": 12,
        "reason": 12,
        "created": 93,
        "apply_time": 93,
        "complete_time": 93,
        "bill_time": 93,
        "total_amount": 3,
        "shop_refund": 3,
        "consumer_refund": 3,
        "created_by": 12,
        "updated_by": 12,
        "created_time": 93,
        "updated_time": 93,
        "version": -5,
        "serial_no": 12,
        "store_code": 12,
        "migration": 12,
        "is_on_promotion": 12
    },
    "table": "offline_refund_order_2407",
    "ts": 1724133173481,
    "type": "UPDATE"
}

 */