package com.yxt.order.atom;

import com.yxt.order.atom.common.utils.OrderDateUtils;
import com.yxt.order.atom.job.AutoCreateOfflineOrderTableHandler;
import com.yxt.order.atom.job.RefundOrderFindOrder404Handler;
import com.yxt.order.atom.job.User404Handler;
import com.yxt.order.atom.migration.MigrationHanaErrorDataHandler;
import com.yxt.order.atom.order.es.sync.consistency_check.EsMemberOrderCheck;
import com.yxt.order.common.CommonDateUtils;
import java.util.Date;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年08月08日 11:09
 * @email: <EMAIL>
 */
@Slf4j
@RestController
public class PreJobController {

  @Resource
  private MigrationHanaErrorDataHandler migrationHanaDataReHandler;

  @Resource
  private User404Handler user404Handler;

  @Resource
  private RefundOrderFindOrder404Handler refundOrderFindOrder404Handler;

  @Resource
  private EsMemberOrderCheck esMemberOrderCheck;

//  @Resource
//  private CompensationOrderNoForRefundOrderHandler compensationOrderNoForRefundOrderHandler;

  @Value("${preCompensationOrderNoTemp:10}")
  private String preCompensationOrderNoTemp;


  @Resource
  private AutoCreateOfflineOrderTableHandler autoCreateOfflineOrderTableHandler;
  @Value("${preCompensationOrderNoTemp:}")
  private String preAutoCreateOfflineTableTemp;





  @GetMapping("/temp/migrationHanaDataReHandler")
  public Boolean migrationHanaDataReHandler() throws Exception {
    migrationHanaDataReHandler.execute();
    return Boolean.TRUE;
  }

//  @GetMapping("/temp/compensationOrderNoForRefundOrderHandler")
//  public Boolean compensationOrderNoForRefundOrderHandler() throws Exception {
//    log.info("preCompensationOrderNoTemp,{}", preCompensationOrderNoTemp);
//    Assert.isTrue(!StringUtils.isEmpty(preCompensationOrderNoTemp), "不能为空");
//    compensationOrderNoForRefundOrderHandler.execute(preCompensationOrderNoTemp);
//    return Boolean.TRUE;
//  }


  @GetMapping("/temp/preCompensationOrderNoTemp")
  public Boolean preCompensationOrderNoTemp() throws Exception {
    log.info("preCompensationOrderNoTemp,{}", preAutoCreateOfflineTableTemp);
    Assert.isTrue(!StringUtils.isEmpty(preAutoCreateOfflineTableTemp), "不能为空");
    autoCreateOfflineOrderTableHandler.execute();
    return Boolean.TRUE;
  }

  @GetMapping("/temp/user404Handler")
  public Boolean user404Handler() throws Exception {
    user404Handler.execute();
    return Boolean.TRUE;
  }

  @GetMapping("/temp/refundOrderFindOrder404Handler")
  public Boolean refundOrderFindOrder404Handler() throws Exception {
    refundOrderFindOrder404Handler.execute();
    return Boolean.TRUE;
  }

  @Value("${consistencyCheckDate:2025-02-11 15:41:25}")
  private String consistencyCheckDate;

  @Value("${consistencyCheckHandlerEndDateSpecify:10}")
  private Integer consistencyCheckEndDateSpecify; // 指定查多少分钟前的订单

  @GetMapping("/temp/consistencyCheck")
  public Boolean consistencyCheck() throws Exception {
//    Date startDate = CommonDateUtils.convert2Date("2025-02-16 16:10:26");
//    Date endDate = CommonDateUtils.convert2Date("2025-02-17 17:00:26");
    Date endDate = CommonDateUtils.convert2Date(consistencyCheckDate);
    Date startDate = OrderDateUtils.previousDateForMinute(endDate, consistencyCheckEndDateSpecify);
    esMemberOrderCheck.checkIfCompensate(startDate,endDate);
    return Boolean.TRUE;
  }


}
