package com.yxt.order.atom.order.es.sync;

import com.yxt.order.atom.order.entity.*;
import com.yxt.order.atom.order.es.sync.data.CanalOfflineOrder.OfflineOrder;
import com.yxt.order.atom.order.es.sync.data.CanalOfflineRefundOrder.OfflineRefundOrder;
import com.yxt.order.atom.order.es.sync.data.CanalOmsOrderInfo.OmsOrderInfo;
import com.yxt.order.atom.order.es.sync.data.CanalOrderInfo.Order;
import com.yxt.order.atom.order.es.sync.data.CanalOrderWorldOrder.OrderInfo;
import com.yxt.order.atom.order.es.sync.data.CanalRefundOrder.RefundOrder;

/**
 * 从DO转Canal Data Dto
 * @author: moatkon
 * @time: 2024/12/24 11:13
 */
public class DoToCanalDtoWrapper {

  public static OfflineOrder getOfflineOrder(OfflineOrderDO data) {
    OfflineOrder offlineOrder = new OfflineOrder();
    offlineOrder.setOrderNo(data.getOrderNo());
    offlineOrder.setStoreCode(data.getStoreCode());
    offlineOrder.setUserId(data.getUserId());
    offlineOrder.setOrderState(data.getOrderState());
    offlineOrder.setThirdOrderNo(data.getThirdOrderNo());
    offlineOrder.setThirdPlatformCode(data.getThirdPlatformCode());
    offlineOrder.setCreated(data.getCreated());
    offlineOrder.setCreatedTime(data.getCreatedTime());
    offlineOrder.setPayTime(data.getPayTime());
    offlineOrder.setNeedRoute(false);
    offlineOrder.setMigration(data.getMigration());
    return offlineOrder;
  }

  public static OfflineRefundOrder getOfflineRefundOrder(OfflineRefundOrderDO data) {
    OfflineRefundOrder refundOrder = new OfflineRefundOrder();
    refundOrder.setRefundNo(data.getRefundNo());
    refundOrder.setOrderNo(data.getOrderNo());
    refundOrder.setStoreCode(data.getStoreCode());
    refundOrder.setUserId(data.getUserId());
    refundOrder.setRefundState(data.getRefundState());
    refundOrder.setThirdRefundNo(data.getThirdRefundNo());
    refundOrder.setThirdOrderNo(data.getThirdOrderNo());
    refundOrder.setRefundType(data.getRefundType());
    refundOrder.setAfterSaleType(data.getAfterSaleType());
    refundOrder.setThirdPlatformCode(data.getThirdPlatformCode());
    refundOrder.setCreated(data.getCreated());
    refundOrder.setCreatedTime(data.getCreatedTime());
    refundOrder.setCompleteTime(data.getCompleteTime());
    refundOrder.setNeedRoute(false);
    refundOrder.setMigration(data.getMigration());
    return refundOrder;
  }


  public static Order getOrder(OrderInfoDO data) {
    Order order = new Order();
    order.setOrderNo(String.valueOf(data.getOrderNo()));
    order.setOnlineStoreCode(data.getOnlineStoreCode());
    order.setOrganizationCode(data.getOrganizationCode());
    order.setMemberNo(data.getMemberNo());
    order.setOrderState(String.valueOf(data.getOrderState()));
    order.setThirdOrderNo(data.getThirdOrderNo());
    order.setThirdPlatformCode(data.getThirdPlatformCode());
    order.setCreated(data.getCreated());
    order.setCreateTime(data.getCreateTime());
    order.setServiceMode(data.getServiceMode());
    order.setPayTime(data.getPayTime());
    order.setCompleteTime(data.getCompleteTime());
    order.setDeleted(data.getDeleted());
    return order;
  }


  public static RefundOrder getRefundOrder(RefundOrderDO data) {
    RefundOrder refundOrder = new RefundOrder();
    refundOrder.setRefundNo(String.valueOf(data.getRefundNo()));
    refundOrder.setOrderNo(String.valueOf(data.getOrderNo()));
    refundOrder.setOnlineStoreCode(data.getOnlineStoreCode());
    refundOrder.setOrganizationCode(data.getOrganizationCode());
    refundOrder.setState(String.valueOf(data.getState()));
    refundOrder.setType(data.getType());
    refundOrder.setRefundType(data.getRefundType());
    refundOrder.setThirdRefundNo(data.getThirdRefundNo());
    refundOrder.setThirdOrderNo(data.getThirdOrderNo());
    refundOrder.setThirdPlatformCode(data.getThirdPlatformCode());
    refundOrder.setCreateTime(data.getCreateTime());
    refundOrder.setCompleteTime(data.getCompleteTime());
    refundOrder.setServiceMode(data.getServiceMode());
    refundOrder.setOmsOrderNo(data.getOmsOrderNo());
    return refundOrder;
  }

  public static OmsOrderInfo getOmsOrderInfo(OmsOrderInfoDO data){
    OmsOrderInfo omsOrderInfo = new OmsOrderInfo();
    omsOrderInfo.setDeleted(data.getDeleted());
    omsOrderInfo.setOmsOrderNo(data.getOmsOrderNo());
    return omsOrderInfo;
  }


  public static OrderInfo getNewOrder(com.yxt.order.atom.order_world.entity.OrderInfoDO orderInfoDO) {
    OrderInfo orderInfo = new OrderInfo();
    orderInfo.setOrderNo(orderInfoDO.getOrderNo());
    orderInfo.setBusinessType(orderInfoDO.getBusinessType());
    orderInfo.setThirdPlatformCode(orderInfoDO.getThirdPlatformCode());
    orderInfo.setTransactionChannel(orderInfoDO.getTransactionChannel());
    orderInfo.setNeedRoute(false);
    return orderInfo;
  }
}
