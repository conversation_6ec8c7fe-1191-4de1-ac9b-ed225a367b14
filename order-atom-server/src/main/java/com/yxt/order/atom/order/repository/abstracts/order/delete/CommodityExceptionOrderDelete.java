package com.yxt.order.atom.order.repository.abstracts.order.delete;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yxt.order.atom.order.entity.CommodityExceptionOrderDO;
import com.yxt.order.atom.order.mapper.CommodityExceptionOrderMapper;
import com.yxt.order.atom.order.repository.abstracts.order.AbstractDelete;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年09月13日 11:52
 * @email: <EMAIL>
 */
@Component
public class CommodityExceptionOrderDelete extends AbstractDelete {

  @Resource
  private CommodityExceptionOrderMapper commodityExceptionOrderMapper;


  @Override
  protected Boolean canDelete() {
    return dto.getCommodityExceptionOrder();
  }

  @Override
  protected Integer delete() {
    LambdaQueryWrapper<CommodityExceptionOrderDO> query = new LambdaQueryWrapper<>();
    query.eq(CommodityExceptionOrderDO::getOrderNo, orderNo);
    return commodityExceptionOrderMapper.delete(query);
  }
}
