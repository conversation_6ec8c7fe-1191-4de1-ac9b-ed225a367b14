package com.yxt.order.atom.order.es.wrapper;

import static org.dromara.easyes.common.constants.BaseEsConstants.DEFAULT_BOOST;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.yxt.order.atom.order.es.doc.*;
import com.yxt.order.atom.order.es.doc.EsB2cAccountInfo;
import com.yxt.order.atom.order.es.doc.EsB2cRefundAccountInfo;
import com.yxt.order.atom.order.es.doc.EsOmsOrderEx;
import com.yxt.order.atom.order.es.doc.EsOmsOrderInfo;
import com.yxt.order.atom.order.es.doc.EsOmsOrderItem;
import com.yxt.order.atom.order.es.doc.EsAccountItem;
import com.yxt.order.atom.order.es.doc.EsOrderWorldOrder;
import com.yxt.order.atom.order.es.doc.EsOrderWorldOrderDetail;
import com.yxt.order.atom.order.es.doc.EsOrderWorldRefundOrder;
import com.yxt.order.atom.order.es.doc.EsOrderWorldRefundOrderDetail;
import com.yxt.order.atom.order.es.doc.EsOrgOrder;
import com.yxt.order.atom.order.es.doc.EsOrgOrderDetail;
import com.yxt.order.atom.order.es.doc.EsOrgRefund;
import com.yxt.order.atom.order.es.doc.EsOrgRefundDetail;
import com.yxt.order.atom.sdk.account_info.req.AccountFialEnum;
import com.yxt.order.atom.sdk.account_info.req.AccountInfoQueryReq;
import com.yxt.order.atom.sdk.account_info.req.RefundAccountInfoQueryReq;
import com.yxt.order.atom.sdk.order_info.req.OrderListQueryDto;
import com.yxt.order.atom.sdk.order_info.req.OrderPageOtherReqDto;
import com.yxt.order.atom.sdk.order_info.req.OrderPageReqDto;
import com.yxt.order.atom.sdk.order_info.req.OrderTag;
import com.yxt.order.atom.sdk.order_world.req.EsOrderWorldOrderCountStatisticReq;
import com.yxt.order.atom.sdk.order_world.req.EsOrderWorldOrderCountWithConditionReq;
import com.yxt.order.atom.sdk.order_world.req.EsOrderWorldOrderPageQueryReq;
import com.yxt.order.atom.sdk.order_world.req.EsOrderWorldOrderSearchCondition;
import com.yxt.order.atom.sdk.order_world.req.EsOrderWorldRefundOrderPageQueryReq;
import com.yxt.order.atom.sdk.order_world.req.EsOrderWorldRefundOrderSearchCondition;
import com.yxt.order.atom.sdk.org_order.req.EsOrderBaseReqDTO;
import com.yxt.order.atom.sdk.org_order.req.EsOrgOrderAmountStaticReqDTO;
import com.yxt.order.atom.sdk.org_order.req.EsOrgOrderCountStatisticReqDTO;
import com.yxt.order.atom.sdk.org_order.req.EsOrgOrderPageQueryReqDTO;
import com.yxt.order.atom.sdk.org_order.req.EsOrgOrderSearchConditionDTO;
import com.yxt.order.atom.sdk.org_order.req.EsOrgRefundAmountStaticReqDTO;
import com.yxt.order.atom.sdk.org_order.req.EsOrgRefundPageQueryReqDTO;
import com.yxt.order.atom.sdk.org_order.req.EsOrgRefundSearchConditionDTO;
import com.yxt.order.atom.sdk.org_order.req.EsRefundBaseReqDTO;
import com.yxt.order.common.CommonDateUtils;
import com.yxt.order.common.es.EsPageRequestDTO.SortEnum;
import com.yxt.order.types.DsConstants;
import com.yxt.order.types.order.enums.OrderSearchTypeEnum;
import com.yxt.order.types.order_world.RefundOrderSearchConditionEnum;
import com.yxt.order.types.order_world.OrderSearchConditionEnum;
import com.yxt.order.types.order_world.RefundOrderSearchConditionEnum;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import javax.validation.constraints.NotEmpty;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.apache.lucene.search.join.ScoreMode;
import org.dromara.easyes.core.biz.OrderByParam;
import org.dromara.easyes.core.conditions.select.LambdaEsQueryWrapper;
import org.dromara.easyes.core.kernel.EsWrappers;
import org.dromara.easyes.core.toolkit.FieldUtils;
import org.springframework.util.CollectionUtils;

/**
 * @author: moatkon
 * @time: 2024/11/7 16:50
 */
public class EsQueryBuilder {
  private static final String ITEM_FIELD = FieldUtils.val(EsOmsOrderInfo::getEsOmsOrderItemList);
  private static final String EX_ITEM_FIELD = FieldUtils.val(EsOmsOrderInfo::getEsOmsOrderExList);
  private static final String ACCOUNT_ITEM_FIELD = FieldUtils.val(EsB2cAccountInfo::getItems);
  private static final String REFUND_ACCOUNT_ITEM_FIELD = FieldUtils.val(EsB2cRefundAccountInfo::getItems);
  private static final String ORG_ORDER_DETAIL_FIELD = FieldUtils.val(EsOrgOrder::getDetailList);
  private static final String ORG_REFUND_DETAIL_FIELD = FieldUtils.val(EsOrgRefund::getDetailList);

  private static final String ORDER_WORLD_ORDER_DETAIL_FIELD = FieldUtils.val(EsOrderWorldOrder::getDetailList);

  private static final String ORDER_WORLD_REFUND_ORDER_DETAIL_FIELD = FieldUtils.val(EsOrderWorldRefundOrder::getDetailList);

  private static final String LOGISTICS_INTERCEPT_FIELD = FieldUtils.val(EsOmsOrderInfo::getTagLogisticsIntercept);

  private static final String LOGISTICS_UP_ADDRESS_FIELD = FieldUtils.val(EsOmsOrderInfo::getTagLogisticsUpAddress);
  public static LambdaEsQueryWrapper<EsOmsOrderInfo> buildEsQueryForOrderPageReqDto(
      OrderPageReqDto reqDto) {
    LambdaEsQueryWrapper<EsOmsOrderInfo> query = new LambdaEsQueryWrapper<>();
    query.eq(EsOmsOrderInfo::getDeleted, 0);
    query.eq(StringUtils.isNotEmpty(reqDto.getMerCode()), EsOmsOrderInfo::getMerCode,reqDto.getMerCode());
    if (StringUtils.isNotEmpty(reqDto.getSupplierCode())) {
      query.eq(EsOmsOrderInfo::getOrderOwnerType, 1);
      query.eq(EsOmsOrderInfo::getSupplierCode, reqDto.getSupplierCode());
    }
    query.eq(Objects.nonNull(reqDto.getOrderOwnerType()), EsOmsOrderInfo::getOrderOwnerType,reqDto.getOrderOwnerType());
    if (Objects.nonNull(reqDto.getOrderState())) {
      if(reqDto.getOrderState()==40){
        query.in(EsOmsOrderInfo::getOrderStatus, Arrays.asList(40,100));
      }
      else{
        query.eq(EsOmsOrderInfo::getOrderStatus, reqDto.getOrderState());
      }
    } else {
      query.in(EsOmsOrderInfo::getOrderStatus, Arrays.asList(10, 30, 40,100));
    }
    query.eq(StringUtils.isNotEmpty(reqDto.getTemplateId()),EsOmsOrderInfo::getLogisticConfigInfoOfStandardTemplateId,reqDto.getTemplateId());
    query.eq(StringUtils.isNotEmpty(reqDto.getPrintPlatformCode()),EsOmsOrderInfo::getLogisticOrderOfPlatformCode,reqDto.getPrintPlatformCode());

    if(Objects.nonNull(reqDto.getTimeType())){
      switch (reqDto.getTimeType()){
        case 1:
          query.ge(StringUtils.isNotEmpty(reqDto.getBeginTime()),EsOmsOrderInfo::getCreated, reqDto.getBeginTime());
          query.le(StringUtils.isNotEmpty(reqDto.getEndTime()),EsOmsOrderInfo::getCreated, reqDto.getEndTime());
          break;
        case 2:
          query.ge(StringUtils.isNotEmpty(reqDto.getBeginTime()),EsOmsOrderInfo::getPayTime, reqDto.getBeginTime());
          query.le(StringUtils.isNotEmpty(reqDto.getEndTime()),EsOmsOrderInfo::getPayTime, reqDto.getEndTime());
          break;
        case 3:
          query.ge(StringUtils.isNotEmpty(reqDto.getBeginTime()),EsOmsOrderInfo::getAuditTime, reqDto.getBeginTime());
          query.le(StringUtils.isNotEmpty(reqDto.getEndTime()),EsOmsOrderInfo::getAuditTime, reqDto.getEndTime());
          break;
        case 4:
          query.ge(StringUtils.isNotEmpty(reqDto.getBeginTime()),EsOmsOrderInfo::getShipTime, reqDto.getBeginTime());
          query.le(StringUtils.isNotEmpty(reqDto.getEndTime()),EsOmsOrderInfo::getShipTime, reqDto.getEndTime());
          break;
        case 5:
          query.ge(StringUtils.isNotEmpty(reqDto.getBeginTime()),EsOmsOrderInfo::getCancelTime, reqDto.getBeginTime());
          query.le(StringUtils.isNotEmpty(reqDto.getEndTime()),EsOmsOrderInfo::getCancelTime, reqDto.getEndTime());
          break;
      }
    }

    query.in(!CollectionUtils.isEmpty(reqDto.getSupplierCodes()), EsOmsOrderInfo::getSupplierCode,reqDto.getSupplierCodes());
    query.in(!CollectionUtils.isEmpty(reqDto.getSpreadStoreCodes()),EsOmsOrderInfo::getSpreadStoreCode, reqDto.getSpreadStoreCodes());
    query.in(!CollectionUtils.isEmpty(reqDto.getMerCodes()), EsOmsOrderInfo::getMerCode,reqDto.getMerCodes());
    query.eq(EsOmsOrderInfo::getIsPostFeeOrder, 0);
    query.eq(Objects.isNull(reqDto.getIsQueryException()),EsOmsOrderInfo::getExStatus, 0);
    query.eq(Objects.nonNull(reqDto.getIsQueryException()),EsOmsOrderInfo::getExStatus, reqDto.getIsQueryException());

    query.lt(Objects.nonNull(reqDto.getOrderState()) && 10 == reqDto.getOrderState() && StringUtils.isNotEmpty(
            reqDto.getWaitAuditCreateTime()),
        EsOmsOrderInfo::getCreateTime, reqDto.getWaitAuditCreateTime());
    query.lt(Objects.nonNull(reqDto.getOrderState()) && 10 == reqDto.getOrderState() && StringUtils.isEmpty(
            reqDto.getWaitAuditCreateTime()),
        EsOmsOrderInfo::getCreateTime, CommonDateUtils.getMinutesAgo(1));
    query.eq(Objects.nonNull(reqDto.getExpressId()), EsOmsOrderInfo::getExpressId, reqDto.getExpressId());

    if(StringUtils.isNotEmpty(reqDto.getCommodityName()) || StringUtils.isNotEmpty(reqDto.getCommodityCode())){
      query.nested(ITEM_FIELD,
          item->item
              // oms_order_no!=1
              .not().eq(String.format("%s.%s", ITEM_FIELD,FieldUtils.val(EsOmsOrderItem::getOmsOrderNo)),1)
              // status != 2 note:如果不行可是使用allEq来实现
              .not().eq(String.format("%s.%s", ITEM_FIELD,FieldUtils.val(EsOmsOrderItem::getStatus)),2)
      );

      if(StringUtils.isNotEmpty(reqDto.getCommodityCode())){
        query.nested(ITEM_FIELD,
            item->item.eq(String.format("%s.%s", ITEM_FIELD,FieldUtils.val(EsOmsOrderItem::getErpCode)),
                reqDto.getCommodityCode())
        );
      }
      if(StringUtils.isNotEmpty(reqDto.getCommodityName())){
        query.nested(ITEM_FIELD,
            // todo notey 这里是like,没有加keyword测试的时候注意下
            item->item.like(String.format("%s.%s", ITEM_FIELD,FieldUtils.val(EsOmsOrderItem::getCommodityName)),
                reqDto.getCommodityName())
        );
      }
    }

    query.eq(StringUtils.isNotEmpty(reqDto.getWarehouseId()),EsOmsOrderInfo::getWarehouseId, reqDto.getWarehouseId());
    query.eq(Objects.nonNull(reqDto.getIsPrescription()),EsOmsOrderInfo::getIsPrescription, reqDto.getIsPrescription());
    if(Objects.nonNull(reqDto.getPrescriptionStatus())){
      switch (reqDto.getPrescriptionStatus()){
        case 1:
          query.eq(EsOmsOrderInfo::getHavecfy,"1");
          query.eq(EsOmsOrderInfo::getRxAuditStatus,"1");
          break;
        case 2:
          query.eq(EsOmsOrderInfo::getHavecfy,"1");
          query.eq(EsOmsOrderInfo::getRxAuditStatus,"0");
          break;
        default:
          Map<String,String> haveCfyNull = new HashMap<>();
          haveCfyNull.put(FieldUtils.val(EsOmsOrderInfo::getHavecfy),null);
          query.and(i->i.eq(EsOmsOrderInfo::getHavecfy,haveCfyNull).or().eq(EsOmsOrderInfo::getHavecfy,"0").or().eq(EsOmsOrderInfo::getRxAuditStatus,"-1"));
      }
    }

    query.eq(StringUtils.isNotEmpty(reqDto.getPlatformCode()),EsOmsOrderInfo::getThirdPlatformCode,
        reqDto.getPlatformCode());

    if(StringUtils.isNotEmpty(reqDto.getOrderNo())){
      query.in(reqDto.getOrderNo().contains(","),EsOmsOrderInfo::getOmsOrderNo,Arrays.asList(
          reqDto.getOrderNo().split(",")));
      query.likeRight(!reqDto.getOrderNo().contains(","),EsOmsOrderInfo::getOmsOrderNo,
          reqDto.getOrderNo(),DEFAULT_BOOST);
    }

    query.eq(StringUtils.isNotEmpty(reqDto.getOmsShipNo()), EsOmsOrderInfo::getOmsShipNo, reqDto.getOmsShipNo());
    query.eq(StringUtils.isNotEmpty(reqDto.getExpressNo()), EsOmsOrderInfo::getExpressNumber,
        reqDto.getExpressNo());
    if(StringUtils.isNotEmpty(reqDto.getThirdOrderNo())){
      query.in(reqDto.getThirdOrderNo().contains(","),EsOmsOrderInfo::getThirdOrderNo,Arrays.asList(
          reqDto.getThirdOrderNo().split(",")));
      query.likeRight(!reqDto.getThirdOrderNo().contains(","),EsOmsOrderInfo::getThirdOrderNo,
          reqDto.getThirdOrderNo(),DEFAULT_BOOST);
    }

    query.in(!CollectionUtils.isEmpty(reqDto.getThirdOrderNoList()),EsOmsOrderInfo::getThirdOrderNo,
        reqDto.getThirdOrderNoList());
    if(!CollectionUtils.isEmpty(reqDto.getCommodityCodeList())){
      query.nested(ITEM_FIELD,
          item->item.in(String.format("%s.%s", ITEM_FIELD,FieldUtils.val(EsOmsOrderItem::getErpCode)),
              reqDto.getCommodityCodeList())
      );
    }
    query.like(StringUtils.isNotEmpty(reqDto.getBuyerName()), EsOmsOrderInfo::getBuyerName, reqDto.getBuyerName());
    if(Objects.nonNull(reqDto.getRemarkType()) && reqDto.getRemarkType() == 2){
      query.and(i -> i.and(j -> j.not().exists(EsOmsOrderInfo::getBuyerMessage)).or().eq(EsOmsOrderInfo::getBuyerMessage,Strings.EMPTY));
      query.and(i -> i.and(j -> j.not().exists(EsOmsOrderInfo::getSellerRemark)).or().eq(EsOmsOrderInfo::getSellerRemark,Strings.EMPTY));
    }

    if(Objects.nonNull(reqDto.getRemarkType()) && reqDto.getRemarkType() == 1){
      if(StringUtils.isNotEmpty(reqDto.getRemarkText())){
        query.and(i->i.like(EsOmsOrderInfo::getBuyerMessage, reqDto.getRemarkText()).or().like(EsOmsOrderInfo::getSellerRemark,
            reqDto.getRemarkText()));
      }else{
        query.and(i -> i
                .and(j -> j.exists(EsOmsOrderInfo::getBuyerMessage).not().eq(EsOmsOrderInfo::getBuyerMessage,Strings.EMPTY))
                .or()
                .and(j -> j.exists(EsOmsOrderInfo::getSellerRemark).not().eq(EsOmsOrderInfo::getSellerRemark,Strings.EMPTY)));
      }
    }
    query.eq(Objects.nonNull(reqDto.getOrderType()) && reqDto.getOrderType() == 1,EsOmsOrderInfo::getIsPrescription,1);
    query.eq(Objects.nonNull(reqDto.getOrderType()) && reqDto.getOrderType() >= 2 && reqDto.getOrderType() <= 5,EsOmsOrderInfo::getOrderType,reqDto.getOrderType());
    if(Objects.nonNull(reqDto.getSplitStatus())){

    }
    query.gt((Objects.nonNull(reqDto.getSplitStatus()) && reqDto.getSplitStatus() == -1),EsOmsOrderInfo::getSplitStatus,0);
    query.eq((Objects.nonNull(reqDto.getSplitStatus()) && reqDto.getSplitStatus() != -1),EsOmsOrderInfo::getSplitStatus,reqDto.getSplitStatus());
    query.eq(StringUtils.isNotEmpty(reqDto.getPayType()) ,EsOmsOrderInfo::getPayType, reqDto.getPayType());
    query.ge(Objects.nonNull(reqDto.getMinAmount()) ,EsOmsOrderInfo::getBuyerActualAmount, reqDto.getMinAmount());
    query.le(Objects.nonNull(reqDto.getMaxAmount()) ,EsOmsOrderInfo::getBuyerActualAmount, reqDto.getMaxAmount());
    query.eq(StringUtils.isNotEmpty(reqDto.getReceiverPhone()) ,EsOmsOrderInfo::getReceiverMobile,
        reqDto.getReceiverPhone());
    query.eq(StringUtils.isNotEmpty(reqDto.getReceiverName()) ,EsOmsOrderInfo::getReceiverName,
        reqDto.getReceiverName());
    query.eq(Objects.nonNull(reqDto.getShipStatus()) ,EsOmsOrderInfo::getShipStatus, reqDto.getShipStatus());
    query.eq(StringUtils.isNotEmpty(reqDto.getSheetStatus()) ,EsOmsOrderInfo::getSheetStatus, reqDto.getSheetStatus());
    if(StringUtils.isNotEmpty(reqDto.getAreas())){
      if(reqDto.getAreas().contains(",")){
        for (String area : reqDto.getAreas().split(",")) {
          query.like(EsOmsOrderInfo::getFullAddress,area);
        }
      }else {
        query.like(EsOmsOrderInfo::getFullAddress, reqDto.getAreas());
      }
    }
    query.like(StringUtils.isNotEmpty(reqDto.getRemark()) ,EsOmsOrderInfo::getRemark, reqDto.getRemark());
    query.eq(StringUtils.isNotEmpty(reqDto.getOnlineStoreCode()) ,EsOmsOrderInfo::getOnlineStoreCode,
        reqDto.getOnlineStoreCode());
    query.in(!CollectionUtils.isEmpty(reqDto.getStoreCodeSet()) ,EsOmsOrderInfo::getOnlineStoreCode,
        reqDto.getStoreCodeSet());
    query.in(!CollectionUtils.isEmpty(reqDto.getWarehouseIdSet()) ,EsOmsOrderInfo::getWarehouseId,
        reqDto.getWarehouseIdSet());
    query.in(!CollectionUtils.isEmpty(reqDto.getOnlineStoreCodeSet()) ,EsOmsOrderInfo::getOnlineStoreCode,
        reqDto.getOnlineStoreCodeSet());
    query.in(!CollectionUtils.isEmpty(reqDto.getPlatformCodeSet()) ,EsOmsOrderInfo::getThirdPlatformCode,
        reqDto.getPlatformCodeSet());
    query.ge(Objects.nonNull(reqDto.getGoodsQtyMin()) ,EsOmsOrderInfo::getGoodsQty, reqDto.getGoodsQtyMin());
    query.le(Objects.nonNull(reqDto.getGoodsQtyMax()) ,EsOmsOrderInfo::getGoodsQty, reqDto.getGoodsQtyMax());
    query.ge(Objects.nonNull(reqDto.getGoodsCategoryQtyMin()) ,EsOmsOrderInfo::getGoodsCategoryQty,
        reqDto.getGoodsCategoryQtyMin());
    query.le(Objects.nonNull(reqDto.getGoodsCategoryQtyMax()) ,EsOmsOrderInfo::getGoodsCategoryQty,
        reqDto.getGoodsCategoryQtyMax());
    OrderTag tag = reqDto.getTag();
    if(Objects.nonNull(tag)){
      query.and(i-> {
          if(Objects.nonNull(tag.getEmptyOrder())){
            i.or().eq(EsOmsOrderInfo::getTagEmptyOrderStatus, tag.getEmptyOrder().getStatus());
          }
          if(Objects.nonNull(tag.getMergeOrder())){
            i.or().eq(EsOmsOrderInfo::getTagMergeOrderStatus, tag.getMergeOrder().getStatus());
          }
          if (Objects.nonNull(tag.getPreSellOrder())){
            i.or().eq(EsOmsOrderInfo::getTagPreSellOrderStatus, tag.getPreSellOrder().getStatus());
          }
          if(Objects.nonNull(tag.getModifyAddressTag())){
            i.or().eq(EsOmsOrderInfo::getTagModifyAddressTagStatus, tag.getModifyAddressTag().getStatus());
          }
          if(Objects.nonNull(tag.getAgentDeliveryOrder())){
            i.or().eq(EsOmsOrderInfo::getTagAgentDeliveryOrder, tag.getAgentDeliveryOrder().getStatus());
          }
          if(!CollectionUtils.isEmpty(tag.getLogisticsInterceptTag())){
            i.or().nested(LOGISTICS_INTERCEPT_FIELD,
                    item->item.in(String.format("%s.%s", LOGISTICS_INTERCEPT_FIELD,FieldUtils.val(LogisticsTagItem::getStatus)),
                            tag.getLogisticsInterceptTag().get(0).getStatus())
            );
          }
          if(!CollectionUtils.isEmpty(tag.getLogisticsUpAddressesTag())){
            i.or().nested(LOGISTICS_UP_ADDRESS_FIELD,
                    item->item.in(String.format("%s.%s", LOGISTICS_UP_ADDRESS_FIELD,FieldUtils.val(LogisticsTagItem::getStatus)),
                            tag.getLogisticsUpAddressesTag().get(0).getStatus())
            );
          }
        });
    }
    if(StringUtils.isNotEmpty(reqDto.getStockState()) && "1".equals(reqDto.getStockState())){
      Map<String,String> stockStateNull = new HashMap<>();
      stockStateNull.put(FieldUtils.val(EsOmsOrderInfo::getStockState),null);
      query.and(i->i.eq(EsOmsOrderInfo::getStockState,1).or().allEq(stockStateNull));
    }
    if(StringUtils.isNotEmpty(reqDto.getStockState()) && "2".equals(reqDto.getStockState())){
      query.eq(StringUtils.isNotEmpty(reqDto.getStockState()) ,EsOmsOrderInfo::getStockState,
          reqDto.getStockState()); // 原sql是直接写的2,这里直接从参数中获取
    }
    query.eq(Objects.nonNull(reqDto.getShipPrintStatus()) && reqDto.getShipPrintStatus() ==0 ,EsOmsOrderInfo::getSendOrderPrintNum,0);
    query.gt(Objects.nonNull(reqDto.getShipPrintStatus()) && reqDto.getShipPrintStatus() ==1 ,EsOmsOrderInfo::getSendOrderPrintNum,0);
    query.gt(StringUtils.isNotEmpty(reqDto.getRefundFlag()) && "1".equals(reqDto.getRefundFlag()),EsOmsOrderInfo::getRefundCount,0);
    query.eq(StringUtils.isNotEmpty(reqDto.getRefundFlag()) && "0".equals(reqDto.getRefundFlag()),EsOmsOrderInfo::getRefundCount,0);
    query.eq(Objects.nonNull(reqDto.getErpAuditStatus()),EsOmsOrderInfo::getErpAuditStatus, reqDto.getErpAuditStatus());

    String orderByConfig = reqDto.getNormalSearchOrderConfig();
    if(StringUtils.isNotEmpty(orderByConfig)){
      switch (orderByConfig){
        case "1":
          query.orderByAsc(EsOmsOrderInfo::getCreated);
          break;
        case "2":
          query.orderByDesc(EsOmsOrderInfo::getCreated);
          break;
        case "3":
          query.orderByDesc(EsOmsOrderInfo::getErpCodeList,EsOmsOrderInfo::getGoodsQty);
          break;
        case "4":
          List<OrderByParam> orderByParams = new ArrayList<>();
          OrderByParam descErpCodeList = new OrderByParam();
          descErpCodeList.setOrder(FieldUtils.val(EsOmsOrderInfo::getErpCodeList));
          descErpCodeList.setSort("DESC");

          OrderByParam goodsCategoryQty = new OrderByParam();
          goodsCategoryQty.setOrder(FieldUtils.val(EsOmsOrderInfo::getGoodsCategoryQty));
          goodsCategoryQty.setSort("ASC");

          orderByParams.add(descErpCodeList);
          orderByParams.add(goodsCategoryQty);
          query.orderBy(orderByParams);
          break;
        case "5":
          query.orderByAsc(EsOmsOrderInfo::getAuditTime);
          break;
        case "6":
          query.orderByDesc(EsOmsOrderInfo::getAuditTime);
          break;
        default:
          query.orderByDesc(EsOmsOrderInfo::getCreated);
      }
    }else {
      query.orderByDesc(EsOmsOrderInfo::getCreated);
    }
    query.eq(Objects.nonNull(reqDto.getClassifyId()),EsOmsOrderInfo::getOnlineStoreType, reqDto.getClassifyId());
    return query;
  }



  public static LambdaEsQueryWrapper<EsOmsOrderInfo> buildEsQueryForOrderPageOtherReqDto(
      OrderPageOtherReqDto reqDto) {
    LambdaEsQueryWrapper<EsOmsOrderInfo> query = new LambdaEsQueryWrapper<>();
    query.eq(StringUtils.isNotEmpty(reqDto.getMerCode()), EsOmsOrderInfo::getMerCode,reqDto.getMerCode());
    if (Objects.nonNull(reqDto.getOrderState())) {
      query.eq(EsOmsOrderInfo::getOrderStatus, reqDto.getOrderState());
    } else {
      query.in(EsOmsOrderInfo::getOrderStatus, Arrays.asList(5,10,15,30,40));
    }
    query.eq(EsOmsOrderInfo::getIsPostFeeOrder, 0);
//    query.eq(EsOmsOrderInfo::getOrderOwnerType, 0);
    query.eq(EsOmsOrderInfo::getDeleted, 0);
    query.eq(EsOmsOrderInfo::getExStatus, 1);
    if(Objects.nonNull(reqDto.getTimeType())){
      switch (reqDto.getTimeType()){
        case 1:
          query.ge(StringUtils.isNotEmpty(reqDto.getBeginTime()),EsOmsOrderInfo::getCreated, reqDto.getBeginTime());
          query.le(StringUtils.isNotEmpty(reqDto.getEndTime()),EsOmsOrderInfo::getCreated, reqDto.getEndTime());
          break;
        case 2:
          query.ge(StringUtils.isNotEmpty(reqDto.getBeginTime()),EsOmsOrderInfo::getPayTime, reqDto.getBeginTime());
          query.le(StringUtils.isNotEmpty(reqDto.getEndTime()),EsOmsOrderInfo::getPayTime, reqDto.getEndTime());
          break;
        case 3:
          query.ge(StringUtils.isNotEmpty(reqDto.getBeginTime()),EsOmsOrderInfo::getAuditTime, reqDto.getBeginTime());
          query.le(StringUtils.isNotEmpty(reqDto.getEndTime()),EsOmsOrderInfo::getAuditTime, reqDto.getEndTime());
          break;
        case 4:
          query.ge(StringUtils.isNotEmpty(reqDto.getBeginTime()),EsOmsOrderInfo::getShipTime, reqDto.getBeginTime());
          query.le(StringUtils.isNotEmpty(reqDto.getEndTime()),EsOmsOrderInfo::getShipTime, reqDto.getEndTime());
          break;
        case 5:
          query.ge(StringUtils.isNotEmpty(reqDto.getBeginTime()),EsOmsOrderInfo::getCancelTime, reqDto.getBeginTime());
          query.le(StringUtils.isNotEmpty(reqDto.getEndTime()),EsOmsOrderInfo::getCancelTime, reqDto.getEndTime());
          break;
      }
    }

    if(Objects.nonNull(reqDto.getExceptionType())){
      query.nested(EX_ITEM_FIELD,item->item
          .eq(String.format("%s.%s", EX_ITEM_FIELD, FieldUtils.val(EsOmsOrderEx::getExType)),reqDto.getExceptionType())
          .eq(String.format("%s.%s", EX_ITEM_FIELD, FieldUtils.val(EsOmsOrderEx::getOperateStatus)),0)
      );
    }

    query.eq(Objects.nonNull(reqDto.getExpressId()), EsOmsOrderInfo::getExpressId, reqDto.getExpressId());
    if(StringUtils.isNotEmpty(reqDto.getCommodityName()) || StringUtils.isNotEmpty(reqDto.getCommodityCode())){
      query.nested(ITEM_FIELD,
          item->item
              // oms_order_no!=1
              .not().eq(String.format("%s.%s", ITEM_FIELD,FieldUtils.val(EsOmsOrderItem::getOmsOrderNo)),1)
              // status != 2 note:如果不行可是使用allEq来实现
              .not().eq(String.format("%s.%s", ITEM_FIELD,FieldUtils.val(EsOmsOrderItem::getStatus)),2)
      );

      if(StringUtils.isNotEmpty(reqDto.getCommodityCode())){
        query.nested(ITEM_FIELD,
            item->item.eq(String.format("%s.%s", ITEM_FIELD,FieldUtils.val(EsOmsOrderItem::getErpCode)),
                reqDto.getCommodityCode())
        );
      }
      if(StringUtils.isNotEmpty(reqDto.getCommodityName())){
        query.nested(ITEM_FIELD,
            // todo notey 这里是like,没有加keyword测试的时候注意下
            item->item.like(String.format("%s.%s", ITEM_FIELD,FieldUtils.val(EsOmsOrderItem::getCommodityName)),
                reqDto.getCommodityName())
        );
      }
    }
    query.eq(StringUtils.isNotEmpty(reqDto.getWarehouseId()),EsOmsOrderInfo::getWarehouseId, reqDto.getWarehouseId());
    query.eq(StringUtils.isNotEmpty(reqDto.getPlatformCode()),EsOmsOrderInfo::getThirdPlatformCode,reqDto.getPlatformCode());
    query.eq(StringUtils.isNotEmpty(reqDto.getOnlineStoreCode()) ,EsOmsOrderInfo::getOnlineStoreCode,reqDto.getOnlineStoreCode());
    if(StringUtils.isNotEmpty(reqDto.getOrderNo())){
      query.in(reqDto.getOrderNo().contains(","),EsOmsOrderInfo::getOmsOrderNo,Arrays.asList(reqDto.getOrderNo().split(",")));
      query.likeRight(!reqDto.getOrderNo().contains(","),EsOmsOrderInfo::getOmsOrderNo,reqDto.getOrderNo(),DEFAULT_BOOST);
    }
    query.eq(StringUtils.isNotEmpty(reqDto.getOmsShipNo()), EsOmsOrderInfo::getOmsShipNo, reqDto.getOmsShipNo());
    query.eq(StringUtils.isNotEmpty(reqDto.getExpressNo()), EsOmsOrderInfo::getExpressNumber,reqDto.getExpressNo());
    if(StringUtils.isNotEmpty(reqDto.getThirdOrderNo())){
      query.in(reqDto.getThirdOrderNo().contains(","),EsOmsOrderInfo::getThirdOrderNo,Arrays.asList(reqDto.getThirdOrderNo().split(",")));
      query.likeRight(!reqDto.getThirdOrderNo().contains(","),EsOmsOrderInfo::getThirdOrderNo,reqDto.getThirdOrderNo(),DEFAULT_BOOST);
    }
    query.in(!CollectionUtils.isEmpty(reqDto.getStoreCodeSet()) ,EsOmsOrderInfo::getOnlineStoreCode,reqDto.getStoreCodeSet());
    query.in(!CollectionUtils.isEmpty(reqDto.getWarehouseIdSet()) ,EsOmsOrderInfo::getWarehouseId,reqDto.getWarehouseIdSet());
    query.in(!CollectionUtils.isEmpty(reqDto.getOnlineStoreCodeSet()) ,EsOmsOrderInfo::getOnlineStoreCode,reqDto.getOnlineStoreCodeSet());
    query.in(!CollectionUtils.isEmpty(reqDto.getPlatformCodeSet()) ,EsOmsOrderInfo::getThirdPlatformCode,reqDto.getPlatformCodeSet());
    query.eq(Objects.nonNull(reqDto.getOrderType()) && reqDto.getOrderType() == 1,EsOmsOrderInfo::getIsPrescription,1);
    query.eq(Objects.nonNull(reqDto.getOrderType()) && reqDto.getOrderType() >= 2 && reqDto.getOrderType() <= 5,EsOmsOrderInfo::getOrderType,reqDto.getOrderType());
    query.not(Objects.nonNull(reqDto.getSplitStatus()) && reqDto.getSplitStatus() == -1).eq(EsOmsOrderInfo::getSplitStatus,0);
    query.eq(Objects.nonNull(reqDto.getSplitStatus()) && reqDto.getSplitStatus() != -1,EsOmsOrderInfo::getSplitStatus,reqDto.getSplitStatus());
    query.eq(Objects.nonNull(reqDto.getPayType()) ,EsOmsOrderInfo::getPayType, reqDto.getPayType());
    query.ge(Objects.nonNull(reqDto.getMinAmount()) ,EsOmsOrderInfo::getBuyerActualAmount, reqDto.getMinAmount());
    query.le(Objects.nonNull(reqDto.getMaxAmount()) ,EsOmsOrderInfo::getBuyerActualAmount, reqDto.getMaxAmount());
    query.eq(StringUtils.isNotEmpty(reqDto.getReceiverPhone()) ,EsOmsOrderInfo::getReceiverMobile,reqDto.getReceiverPhone());
    query.eq(StringUtils.isNotEmpty(reqDto.getReceiverName()) ,EsOmsOrderInfo::getReceiverName,reqDto.getReceiverName());
    if(StringUtils.isNotEmpty(reqDto.getAreas())){
      if(reqDto.getAreas().contains(",")){
        for (String area : reqDto.getAreas().split(",")) {
          query.like(EsOmsOrderInfo::getFullAddress,area);
        }
      }else {
        query.like(EsOmsOrderInfo::getFullAddress, reqDto.getAreas());
      }
    }
    query.like(StringUtils.isNotEmpty(reqDto.getBuyerName()), EsOmsOrderInfo::getBuyerName, reqDto.getBuyerName());
    query.eq(Objects.nonNull(reqDto.getClassifyId()),EsOmsOrderInfo::getOnlineStoreType, reqDto.getClassifyId());
    query.like(StringUtils.isNotEmpty(reqDto.getRemark()) ,EsOmsOrderInfo::getRemark, reqDto.getRemark());
    if(Objects.nonNull(reqDto.getRemarkType()) && reqDto.getRemarkType() == 2){
      query.and(i -> i.and(j -> j.not().exists(EsOmsOrderInfo::getBuyerMessage)).or().eq(EsOmsOrderInfo::getBuyerMessage,Strings.EMPTY));
      query.and(i -> i.and(j -> j.not().exists(EsOmsOrderInfo::getSellerRemark)).or().eq(EsOmsOrderInfo::getSellerRemark,Strings.EMPTY));
    }
    if(Objects.nonNull(reqDto.getRemarkType()) && reqDto.getRemarkType() == 1){
      if(StringUtils.isNotEmpty(reqDto.getRemarkText())){
        query.and(i->i.like(EsOmsOrderInfo::getBuyerMessage, reqDto.getRemarkText()).or().like(EsOmsOrderInfo::getSellerRemark,
            reqDto.getRemarkText()));
      }else{
        query.and(i -> i
                .and(j -> j.exists(EsOmsOrderInfo::getBuyerMessage).not().eq(EsOmsOrderInfo::getBuyerMessage,Strings.EMPTY))
                .or()
                .and(j -> j.exists(EsOmsOrderInfo::getSellerRemark).not().eq(EsOmsOrderInfo::getSellerRemark,Strings.EMPTY)));
      }
    }
    OrderTag tag = reqDto.getTag();
    if(Objects.nonNull(tag)){
      query.and(i-> {
        if(Objects.nonNull(tag.getEmptyOrder())){
          i.or().eq(EsOmsOrderInfo::getTagEmptyOrderStatus, tag.getEmptyOrder().getStatus());
        }
        if(Objects.nonNull(tag.getMergeOrder())){
          i.or().eq(EsOmsOrderInfo::getTagMergeOrderStatus, tag.getMergeOrder().getStatus());
        }
        if (Objects.nonNull(tag.getPreSellOrder())){
          i.or().eq(EsOmsOrderInfo::getTagPreSellOrderStatus, tag.getPreSellOrder().getStatus());
        }
        if(Objects.nonNull(tag.getModifyAddressTag())){
          i.or().eq(EsOmsOrderInfo::getTagModifyAddressTagStatus, tag.getModifyAddressTag().getStatus());
        }
        if(Objects.nonNull(tag.getAgentDeliveryOrder())){
          i.or().eq(EsOmsOrderInfo::getTagAgentDeliveryOrder, tag.getAgentDeliveryOrder().getStatus());
        }
        if(!CollectionUtils.isEmpty(tag.getLogisticsInterceptTag())){
          i.or().nested(LOGISTICS_INTERCEPT_FIELD,
                  item->item.in(String.format("%s.%s", LOGISTICS_INTERCEPT_FIELD,FieldUtils.val(LogisticsTagItem::getStatus)),
                          tag.getLogisticsInterceptTag().get(0).getStatus())
          );
        }
        if(!CollectionUtils.isEmpty(tag.getLogisticsUpAddressesTag())){
          i.or().nested(LOGISTICS_UP_ADDRESS_FIELD,
                  item->item.in(String.format("%s.%s", LOGISTICS_UP_ADDRESS_FIELD,FieldUtils.val(LogisticsTagItem::getStatus)),
                          tag.getLogisticsUpAddressesTag().get(0).getStatus())
          );
        }
      });
    }
    query.orderByDesc(EsOmsOrderInfo::getCreated);
    return query;
  }
  public static LambdaEsQueryWrapper<EsOmsOrderInfo> buildEsQueryForOrderListQueryDto(
      OrderListQueryDto reqDto) {
    LambdaEsQueryWrapper<EsOmsOrderInfo> query = new LambdaEsQueryWrapper<>();
    query.eq(EsOmsOrderInfo::getDeleted, 0);
    if(CollectionUtils.isEmpty(reqDto.getSupplierCodes()) && Objects.isNull(reqDto.getSupplierCode())){
      query.in(EsOmsOrderInfo::getOrderStatus, Arrays.asList(5,10,15,30,40,100,101,102,0,91,92,93));
    }else {
      query.in(EsOmsOrderInfo::getOrderStatus,Arrays.asList(5,15,30,40,100,101,102,0,91,92,93));
    }
    query.eq(StringUtils.isNotEmpty(reqDto.getMerCode()), EsOmsOrderInfo::getMerCode,reqDto.getMerCode());
    query.in(!CollectionUtils.isEmpty(reqDto.getOmsOrderNoList()),EsOmsOrderInfo::getOmsOrderNo,reqDto.getOmsOrderNoList());
    if (StringUtils.isNotEmpty(reqDto.getSupplierCode())) {
      query.eq(EsOmsOrderInfo::getOrderOwnerType, 1);
      query.eq(EsOmsOrderInfo::getSupplierCode, reqDto.getSupplierCode());
    }
    if(StringUtils.isNotEmpty(reqDto.getOrderNo())){
      query.in(reqDto.getOrderNo().contains(","),EsOmsOrderInfo::getOmsOrderNo,Arrays.asList(reqDto.getOrderNo().split(",")));
      query.eq(!reqDto.getOrderNo().contains(","),EsOmsOrderInfo::getOmsOrderNo,reqDto.getOrderNo());
    }
    if(StringUtils.isNotEmpty(reqDto.getThirdOrderNo())){
      query.in(reqDto.getThirdOrderNo().contains(","),EsOmsOrderInfo::getThirdOrderNo,Arrays.asList(reqDto.getThirdOrderNo().split(",")));
      query.eq(!reqDto.getThirdOrderNo().contains(","),EsOmsOrderInfo::getThirdOrderNo,reqDto.getThirdOrderNo());
    }
    query.eq(Objects.nonNull(reqDto.getExpressId()), EsOmsOrderInfo::getExpressId, reqDto.getExpressId());
    query.eq(StringUtils.isNotEmpty(reqDto.getOmsShipNo()), EsOmsOrderInfo::getOmsShipNo, reqDto.getOmsShipNo());
    if(StringUtils.isNotEmpty(reqDto.getExpressNo())){
      query.in(EsOmsOrderInfo::getExpressNumber,Arrays.asList(reqDto.getExpressNo().split(",")));
    }
    query.eq(Objects.nonNull(reqDto.getOrderOwnerType()), EsOmsOrderInfo::getOrderOwnerType,reqDto.getOrderOwnerType());
    query.eq(StringUtils.isNotEmpty(reqDto.getOnlineStoreCode()) ,EsOmsOrderInfo::getOnlineStoreCode,reqDto.getOnlineStoreCode());
    query.eq(StringUtils.isNotEmpty(reqDto.getPlatformCode()),EsOmsOrderInfo::getThirdPlatformCode,reqDto.getPlatformCode());
    query.eq(StringUtils.isNotEmpty(reqDto.getWarehouseId()),EsOmsOrderInfo::getWarehouseId, reqDto.getWarehouseId());
    query.in(!CollectionUtils.isEmpty(reqDto.getOrderStatus()),EsOmsOrderInfo::getOrderStatus, reqDto.getOrderStatus());
    query.eq(Objects.nonNull(reqDto.getIsPrescription()),EsOmsOrderInfo::getIsPrescription, reqDto.getIsPrescription());
    query.eq(Objects.nonNull(reqDto.getErpState()),EsOmsOrderInfo::getErpStatus, reqDto.getErpState());
    query.eq(Objects.nonNull(reqDto.getOrderType()) && reqDto.getOrderType() == 1,EsOmsOrderInfo::getIsPrescription,1);
    query.eq(Objects.nonNull(reqDto.getOrderType()) && reqDto.getOrderType() == 6,EsOmsOrderInfo::getIsPostFeeOrder,1);
    query.eq(Objects.nonNull(reqDto.getOrderType()) && reqDto.getOrderType() != 1 && reqDto.getOrderType() != 6,EsOmsOrderInfo::getOrderType,reqDto.getOrderType());
    query.not(Objects.nonNull(reqDto.getSplitStatus()) && reqDto.getSplitStatus() == -1).eq(EsOmsOrderInfo::getSplitStatus,0);
    query.eq(Objects.nonNull(reqDto.getSplitStatus()) && reqDto.getSplitStatus() != -1,EsOmsOrderInfo::getSplitStatus,reqDto.getSplitStatus());

    if(Objects.nonNull(reqDto.getTimeType())){
      switch (reqDto.getTimeType()){
        case 1:
          query.ge(StringUtils.isNotEmpty(reqDto.getBeginTime()),EsOmsOrderInfo::getCreated, reqDto.getBeginTime());
          query.le(StringUtils.isNotEmpty(reqDto.getEndTime()),EsOmsOrderInfo::getCreated, reqDto.getEndTime());
          break;
        case 2:
          query.ge(StringUtils.isNotEmpty(reqDto.getBeginTime()),EsOmsOrderInfo::getPayTime, reqDto.getBeginTime());
          query.le(StringUtils.isNotEmpty(reqDto.getEndTime()),EsOmsOrderInfo::getPayTime, reqDto.getEndTime());
          break;
        case 3:
          query.ge(StringUtils.isNotEmpty(reqDto.getBeginTime()),EsOmsOrderInfo::getAuditTime, reqDto.getBeginTime());
          query.le(StringUtils.isNotEmpty(reqDto.getEndTime()),EsOmsOrderInfo::getAuditTime, reqDto.getEndTime());
          break;
        case 4:
          query.ge(StringUtils.isNotEmpty(reqDto.getBeginTime()),EsOmsOrderInfo::getShipTime, reqDto.getBeginTime());
          query.le(StringUtils.isNotEmpty(reqDto.getEndTime()),EsOmsOrderInfo::getShipTime, reqDto.getEndTime());
          break;
        case 5:
          query.ge(StringUtils.isNotEmpty(reqDto.getBeginTime()),EsOmsOrderInfo::getCompleteTime, reqDto.getBeginTime());
          query.le(StringUtils.isNotEmpty(reqDto.getEndTime()),EsOmsOrderInfo::getCompleteTime, reqDto.getEndTime());
          break;
        case 6:
          query.ge(StringUtils.isNotEmpty(reqDto.getBeginTime()),EsOmsOrderInfo::getBillTime, reqDto.getBeginTime());
          query.le(StringUtils.isNotEmpty(reqDto.getEndTime()),EsOmsOrderInfo::getBillTime, reqDto.getEndTime());
          break;
      }
    }
    query.like(StringUtils.isNotEmpty(reqDto.getRemark()) ,EsOmsOrderInfo::getRemark, reqDto.getRemark());
    if(Objects.nonNull(reqDto.getRemarkType()) && reqDto.getRemarkType() == 2){
      query.and(i -> i.and(j -> j.not().exists(EsOmsOrderInfo::getBuyerMessage)).or().eq(EsOmsOrderInfo::getBuyerMessage,Strings.EMPTY));
      query.and(i -> i.and(j -> j.not().exists(EsOmsOrderInfo::getSellerRemark)).or().eq(EsOmsOrderInfo::getSellerRemark,Strings.EMPTY));
    }
    if(Objects.nonNull(reqDto.getRemarkType()) && reqDto.getRemarkType() == 1){
      if(StringUtils.isNotEmpty(reqDto.getRemarkText())){
        query.and(i->i.like(EsOmsOrderInfo::getBuyerMessage, reqDto.getRemarkText()).or().like(EsOmsOrderInfo::getSellerRemark,reqDto.getRemarkText()));
      }else{
        query.and(i -> i
                .and(j -> j.exists(EsOmsOrderInfo::getBuyerMessage).not().eq(EsOmsOrderInfo::getBuyerMessage,Strings.EMPTY))
                .or()
                .and(j -> j.exists(EsOmsOrderInfo::getSellerRemark).not().eq(EsOmsOrderInfo::getSellerRemark,Strings.EMPTY)));
      }
    }
    query.like(StringUtils.isNotEmpty(reqDto.getBuyerName()), EsOmsOrderInfo::getBuyerName, reqDto.getBuyerName());
    query.in(!CollectionUtils.isEmpty(reqDto.getStoreCodeSet()) ,EsOmsOrderInfo::getOnlineStoreCode,reqDto.getStoreCodeSet());
    query.in(!CollectionUtils.isEmpty(reqDto.getWarehouseIdSet()) ,EsOmsOrderInfo::getWarehouseId,reqDto.getWarehouseIdSet());
    query.in(!CollectionUtils.isEmpty(reqDto.getOnlineStoreCodeSet()) ,EsOmsOrderInfo::getOnlineStoreCode,reqDto.getOnlineStoreCodeSet());
    query.in(!CollectionUtils.isEmpty(reqDto.getPlatformCodeSet()) ,EsOmsOrderInfo::getThirdPlatformCode,reqDto.getPlatformCodeSet());
    query.eq(Objects.nonNull(reqDto.getExpressId()), EsOmsOrderInfo::getExpressId, reqDto.getExpressId());
    if(StringUtils.isNotEmpty(reqDto.getCommodityCode())){
      query.nested(ITEM_FIELD,item->item.eq(String.format("%s.%s", ITEM_FIELD,FieldUtils.val(EsOmsOrderItem::getStatus)),0));
      if(StringUtils.isNotEmpty(reqDto.getCommodityCode())){
        query.nested(ITEM_FIELD,
            item->item.in(String.format("%s.%s", ITEM_FIELD,FieldUtils.val(EsOmsOrderItem::getErpCode)),Arrays.asList(reqDto.getCommodityCode().split(",")))
        );
      }
    }
    if (StringUtils.isNotEmpty(reqDto.getCommodityName())) {
      query.nested(ITEM_FIELD,
          // todo notey 这里是like,没有加keyword测试的时候注意下
          item -> item.like(
              String.format("%s.%s", ITEM_FIELD, FieldUtils.val(EsOmsOrderItem::getCommodityName)),
              reqDto.getCommodityName())
      );
    }
    if(!CollectionUtils.isEmpty(reqDto.getPaths())){
      for (String path : reqDto.getPaths()) {
        query.likeRight(StringUtils.isNotEmpty(path),EsOmsOrderInfo::getFullAddress,path,DEFAULT_BOOST);
      }
    }
    query.and(StringUtils.isNotEmpty(reqDto.getReceiverPhone()),i->i.eq(EsOmsOrderInfo::getReceiverMobile,
        reqDto.getReceiverPhone()).or().eq(EsOmsOrderInfo::getReceiverTelephone,reqDto.getReceiverPhone()));
    query.like(StringUtils.isNotEmpty(reqDto.getReceiverName()) ,EsOmsOrderInfo::getReceiverName,reqDto.getReceiverName());
    query.eq(Objects.nonNull(reqDto.getPayType()) ,EsOmsOrderInfo::getPayType, reqDto.getPayType());
    OrderTag tag = reqDto.getTag();
    if(Objects.nonNull(tag)){
      query.and(i-> {
        if(Objects.nonNull(tag.getEmptyOrder())){
          i.or().eq(EsOmsOrderInfo::getTagEmptyOrderStatus, tag.getEmptyOrder().getStatus());
        }
        if(Objects.nonNull(tag.getMergeOrder())){
          i.or().eq(EsOmsOrderInfo::getTagMergeOrderStatus, tag.getMergeOrder().getStatus());
        }
        if (Objects.nonNull(tag.getPreSellOrder())){
          i.or().eq(EsOmsOrderInfo::getTagPreSellOrderStatus, tag.getPreSellOrder().getStatus());
        }
        if(Objects.nonNull(tag.getModifyAddressTag())){
          i.or().eq(EsOmsOrderInfo::getTagModifyAddressTagStatus, tag.getModifyAddressTag().getStatus());
        }
        if(Objects.nonNull(tag.getAgentDeliveryOrder())){
          i.or().eq(EsOmsOrderInfo::getTagAgentDeliveryOrder, tag.getAgentDeliveryOrder().getStatus());
        }
        if(!CollectionUtils.isEmpty(tag.getLogisticsInterceptTag())){
          i.or().nested(LOGISTICS_INTERCEPT_FIELD,
                  item->item.in(String.format("%s.%s", LOGISTICS_INTERCEPT_FIELD,FieldUtils.val(LogisticsTagItem::getStatus)),
                          tag.getLogisticsInterceptTag().get(0).getStatus())
          );
        }
        if(!CollectionUtils.isEmpty(tag.getLogisticsUpAddressesTag())){
          i.or().nested(LOGISTICS_UP_ADDRESS_FIELD,
                  item->item.in(String.format("%s.%s", LOGISTICS_UP_ADDRESS_FIELD,FieldUtils.val(LogisticsTagItem::getStatus)),
                          tag.getLogisticsUpAddressesTag().get(0).getStatus())
          );
        }
      });
    }
    query.eq(Objects.nonNull(reqDto.getReturnType()) && 1==reqDto.getReturnType(),EsOmsOrderInfo::getIsPostFeeOrder,0);
    if(!CollectionUtils.isEmpty(reqDto.getSupplierCodes())){
      query.in(EsOmsOrderInfo::getSupplierCode,reqDto.getSupplierCodes());
      query.eq(EsOmsOrderInfo::getOrderOwnerType,1);
    }
    query.in(!CollectionUtils.isEmpty(reqDto.getSpreadStoreCodes()),EsOmsOrderInfo::getSpreadStoreCode, reqDto.getSpreadStoreCodes());
    query.in(!CollectionUtils.isEmpty(reqDto.getSpreadPermissionStoreCodes()),EsOmsOrderInfo::getSpreadStoreCode, reqDto.getSpreadPermissionStoreCodes());
    query.in(!CollectionUtils.isEmpty(reqDto.getMerCodes()),EsOmsOrderInfo::getMerCode, reqDto.getMerCodes());
    query.eq(Objects.nonNull(reqDto.getIsFilterPostFee()) &&reqDto.getIsFilterPostFee(),EsOmsOrderInfo::getIsPostFeeOrder,1);
    query.eq(Objects.nonNull(reqDto.getIsFilterPostFee()) &&!reqDto.getIsFilterPostFee(),EsOmsOrderInfo::getIsPostFeeOrder,0);

    query.eq(Objects.nonNull(reqDto.getExStatus()),EsOmsOrderInfo::getExStatus, reqDto.getExStatus());
    if(Objects.nonNull(reqDto.getExceptionType())){
      query.nested(EX_ITEM_FIELD,item->item
          .eq(String.format("%s.%s", EX_ITEM_FIELD, FieldUtils.val(EsOmsOrderEx::getExType)),reqDto.getExceptionType())
      );
    }
    query.orderByDesc(EsOmsOrderInfo::getCreated);
    return query;
  }

  /**
   * 构建B2C正向单下账列表查询条件
   * @param queryReq 查询参数
   * @return 下账单数据
   * @throws ParseException
   */
  public static LambdaEsQueryWrapper<EsB2cAccountInfo> buildEsQueryForAccountInfoQueryDto(
      AccountInfoQueryReq queryReq) throws ParseException {
    LambdaEsQueryWrapper<EsB2cAccountInfo> query = new LambdaEsQueryWrapper<>();
    // 主要字段过滤

    query.eq(StringUtils.isNotEmpty(queryReq.getThirdOrderNo()),EsB2cAccountInfo::getThirdOrderNo, queryReq.getThirdOrderNo());
    query.eq(StringUtils.isNotEmpty(queryReq.getOrderNo()),EsB2cAccountInfo::getOrderNo, queryReq.getOrderNo());
    // 模糊查询机构信息
    query.likeRight(StringUtils.isNotEmpty(queryReq.getAccOrgParentPath()),EsB2cAccountInfo::getAccOrgParentPath, queryReq.getAccOrgParentPath(),
        DEFAULT_BOOST);
    query.likeRight(StringUtils.isNotEmpty(queryReq.getOrgParentPath()),EsB2cAccountInfo::getOrgParentPath, queryReq.getOrgParentPath(),DEFAULT_BOOST);


    query.eq(StringUtils.isNotEmpty(queryReq.getSubCompanyCode()),EsB2cAccountInfo::getSubCompanyCode, queryReq.getSubCompanyCode()) ;
    query.in(ObjectUtil.isNotNull(queryReq.getSubCompanyList()),EsB2cAccountInfo::getSubCompanyCode, queryReq.getSubCompanyList());
    SimpleDateFormat dateFormat= new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    query.eq(StringUtils.isNotEmpty(queryReq.getOrganizationCode()),EsB2cAccountInfo::getOrganizationCode, queryReq.getOrganizationCode());
    query.eq(StringUtils.isNotEmpty(queryReq.getAccOrganizationCode()),EsB2cAccountInfo::getAccOrganizationCode, queryReq.getAccOrganizationCode());
    query.eq(StringUtils.isNotEmpty(queryReq.getState()),EsB2cAccountInfo::getState, queryReq.getState());
    query.eq(StringUtils.isNotEmpty(queryReq.getPickType()),EsB2cAccountInfo::getPickType, queryReq.getPickType());
    query.eq(StringUtils.isNotEmpty(queryReq.getPosMode()),EsB2cAccountInfo::getPosMode, queryReq.getPosMode());
    query.eq(StringUtils.isNotEmpty(queryReq.getSaleNo()),EsB2cAccountInfo::getSaleNo, queryReq.getSaleNo());
    if(StringUtils.isNotEmpty(queryReq.getStartAcceptTime())){
      query.ge(EsB2cAccountInfo::getOrderAcceptTime,  dateFormat.parse(queryReq.getStartAcceptTime()).getTime());
    }
    if(StringUtils.isNotEmpty(queryReq.getEndAcceptTime())){
      query.le(EsB2cAccountInfo::getOrderAcceptTime,  dateFormat.parse(queryReq.getEndAcceptTime()).getTime());
    }
    if(StringUtils.isNotEmpty(queryReq.getStartAccountTime())){
      query.ge(EsB2cAccountInfo::getAccountTime, dateFormat.parse(queryReq.getStartAccountTime()).getTime() );
    }
    if(StringUtils.isNotEmpty(queryReq.getEndAccountTime())){
      query.le(EsB2cAccountInfo::getAccountTime, dateFormat.parse(queryReq.getEndAccountTime()).getTime()  );
    }
    if (Objects.nonNull(queryReq.getAccountFailStatus()) && "FAIL".equals(queryReq.getState())) {
      query.like(EsB2cAccountInfo::getAccountErrMsg,   AccountFialEnum.getMsgByCode(queryReq.getAccountFailStatus()));
    }
    if(StringUtils.isNotEmpty(queryReq.getErpCode())){
      query.nested(ACCOUNT_ITEM_FIELD,
          item->item.eq(String.format("%s.%s.keyword", ACCOUNT_ITEM_FIELD,FieldUtils.val(EsAccountItem::getErpCode)),
              queryReq.getErpCode())
      );
    }
    query.eq(EsB2cAccountInfo::getDeleted, 0L);
    return query;
  }

  /**
   * 构建B2C逆向单下账列表查询条件
   * @param queryReq 查询参数
   * @return 下账单数据
   * @throws ParseException
   */
  public static LambdaEsQueryWrapper<EsB2cRefundAccountInfo> buildEsQueryForRefundAccountInfoQueryDto(
      RefundAccountInfoQueryReq queryReq) throws ParseException {
    LambdaEsQueryWrapper<EsB2cRefundAccountInfo> query = new LambdaEsQueryWrapper<>();
    // 主要字段过滤

    query.eq(StringUtils.isNotEmpty(queryReq.getThirdOrderNo()),EsB2cRefundAccountInfo::getThirdOrderNo, queryReq.getThirdOrderNo());
    query.eq(StringUtils.isNotEmpty(queryReq.getOrderNo()),EsB2cRefundAccountInfo::getOrderNo, queryReq.getOrderNo());
    // 模糊查询机构信息
    query.likeRight(StringUtils.isNotEmpty(queryReq.getAccOrgParentPath()),EsB2cRefundAccountInfo::getAccOrgParentPath, queryReq.getAccOrgParentPath(),
        DEFAULT_BOOST);
    query.likeRight(StringUtils.isNotEmpty(queryReq.getOrgParentPath()),EsB2cRefundAccountInfo::getOrgParentPath, queryReq.getOrgParentPath(),DEFAULT_BOOST);

    query.eq(StringUtils.isNotEmpty(queryReq.getOrganizationCode()),EsB2cRefundAccountInfo::getOrganizationCode, queryReq.getOrganizationCode());
    query.eq(StringUtils.isNotEmpty(queryReq.getAccOrganizationCode()),EsB2cRefundAccountInfo::getAccOrganizationCode, queryReq.getAccOrganizationCode());

    query.eq(StringUtils.isNotEmpty(queryReq.getSubCompanyCode()),EsB2cRefundAccountInfo::getSubCompanyCode, queryReq.getSubCompanyCode()) ;
    query.in(ObjectUtil.isNotNull(queryReq.getSubCompanyList()),EsB2cRefundAccountInfo::getSubCompanyCode, queryReq.getSubCompanyList());
    SimpleDateFormat dateFormat= new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    query.eq(StringUtils.isNotEmpty(queryReq.getState()),EsB2cRefundAccountInfo::getState, queryReq.getState());
    query.eq(StringUtils.isNotEmpty(queryReq.getPickType()),EsB2cRefundAccountInfo::getPickType, queryReq.getPickType());
    query.eq(StringUtils.isNotEmpty(queryReq.getPosMode()),EsB2cRefundAccountInfo::getPosMode, queryReq.getPosMode());
    query.eq(StringUtils.isNotEmpty(queryReq.getSaleNo()),EsB2cRefundAccountInfo::getSaleNo, queryReq.getSaleNo());
    if(StringUtils.isNotEmpty(queryReq.getStartAcceptTime())){
      query.ge(EsB2cRefundAccountInfo::getRefundAcceptTime,  dateFormat.parse(queryReq.getStartAcceptTime()).getTime());
    }
    if(StringUtils.isNotEmpty(queryReq.getEndAcceptTime())){
      query.le(EsB2cRefundAccountInfo::getRefundAcceptTime,  dateFormat.parse(queryReq.getEndAcceptTime()).getTime());
    }
    if(StringUtils.isNotEmpty(queryReq.getStartAccountTime())){
      query.ge(EsB2cRefundAccountInfo::getAccountTime, dateFormat.parse(queryReq.getStartAccountTime()).getTime() );
    }
    if(StringUtils.isNotEmpty(queryReq.getEndAcceptTime())){
      query.le(EsB2cRefundAccountInfo::getAccountTime,
          dateFormat.parse(queryReq.getEndAcceptTime()).getTime()  );
    }

    if(StringUtils.isNotEmpty(queryReq.getErpCode())){
      query.nested(REFUND_ACCOUNT_ITEM_FIELD,
          item->item.eq(String.format("%s.%s.keyword", REFUND_ACCOUNT_ITEM_FIELD,FieldUtils.val(EsAccountItem::getErpCode)),
              queryReq.getErpCode())
      );
    }
    query.eq(EsB2cRefundAccountInfo::getDeleted, 0L);
    return query;
  }


  public static LambdaEsQueryWrapper<EsOrgOrder> buildEsQueryForOrgOrderAmountStatic(EsOrgOrderAmountStaticReqDTO reqDto) {
    LambdaEsQueryWrapper<EsOrgOrder> wrapper = buildEsQueryForOrgOrderQueryCondition(reqDto.getSearchConditionList());
    wrapper.sum(EsOrgOrder::getBillAmount);
    return wrapper;
  }


  public static LambdaEsQueryWrapper<EsOrgOrder> buildEsQueryForOrgOrderPageQuery(EsOrgOrderPageQueryReqDTO reqDto) {
    List<EsOrgOrderSearchConditionDTO> searchConditionList = reqDto.getSearchConditionList();
    LambdaEsQueryWrapper<EsOrgOrder> wrapper = buildEsQueryForOrgOrderQueryCondition(searchConditionList);
    List<OrderByParam> orderByParams = new ArrayList<>();
    long count = searchConditionList.stream().filter(condition -> condition.getSearchType() == OrderSearchTypeEnum.ERP_INFO).count();
    if(count > 0){
      //如果根据商品信息查询，需要根据分数进行排序，先暂时写死
      OrderByParam score = new OrderByParam();
      score.setSort("DESC");
      score.setOrder("_score");
      orderByParams.add(score);
    }
    if (CollUtil.isNotEmpty(reqDto.getSortParams())) {
      orderByParams.addAll(reqDto.getSortParams().stream().map(sort -> {
        OrderByParam orderByParam = new OrderByParam();
        orderByParam.setSort(sort.getSort().name());
        orderByParam.setOrder(sort.getSortBy());
        return orderByParam;
      }).collect(Collectors.toList()));
    } else {
      OrderByParam orderByParam = new OrderByParam();
      orderByParam.setSort(SortEnum.DESC.name());
      orderByParam.setOrder("payTime");
      orderByParams.add(orderByParam);
    }
    wrapper.orderBy(orderByParams);
    return wrapper;
  }

  private static LambdaEsQueryWrapper<EsOrgOrder> buildEsQueryForOrgOrderQueryCondition(List<EsOrgOrderSearchConditionDTO> searchConditionList) {
    LambdaEsQueryWrapper<EsOrgOrder> wrapper = EsWrappers.lambdaQuery(EsOrgOrder.class);
    if(CollUtil.isEmpty(searchConditionList)){
      return wrapper;
    }
    for (EsOrgOrderSearchConditionDTO condition : searchConditionList) {
      switch (condition.getSearchType()) {
        case ORDER_SOURCE:
          wrapper.eq(StrUtil.isNotBlank(condition.getSearchData()), EsOrgOrder::getOrderSource, condition.getSearchData());
          break;
        case STORE_CODE:
          wrapper.in(StrUtil.isNotBlank(condition.getSearchData()), EsOrgOrder::getStoreCode, StrUtil.split(condition.getSearchData(), StrUtil.COMMA));
          break;
        case ORG_CODE:
          if(StrUtil.isNotBlank(condition.getSearchData())) {
            List<String> orgList = StrUtil.split(condition.getSearchData(), StrUtil.COMMA);
            wrapper.in(EsOrgOrder::getOrgCode, orgList);
            //设置路由，目前会按照org_code进行分片
            if(orgList.size() == 1){
              wrapper.routing(condition.getSearchData());
            }
          }
          break;
        case SOURCE_STORE_CODE:
          wrapper.in(StrUtil.isNotBlank(condition.getSearchData()), EsOrgOrder::getSourceStoreCode, StrUtil.split(condition.getSearchData(), StrUtil.COMMA));
          break;
        case SOURCE_ORG_CODE:
          wrapper.in(StrUtil.isNotBlank(condition.getSearchData()), EsOrgOrder::getSourceOrgCode, StrUtil.split(condition.getSearchData(), StrUtil.COMMA));
          break;
        case START_DATE:
          if (StrUtil.isNotBlank(condition.getSearchData())) {
            wrapper.ge(EsOrgOrder::getPayTime, DateUtil.formatDateTime(new Date(Long.parseLong(condition.getSearchData()) * 1000L)));
          }
          break;
        case END_DATE:
          if (StrUtil.isNotBlank(condition.getSearchData())) {
            wrapper.le(EsOrgOrder::getPayTime, DateUtil.formatDateTime(new Date(Long.parseLong(condition.getSearchData()) * 1000L)));
          }
          break;
        case ORDER_NO:
          wrapper.in(StrUtil.isNotBlank(condition.getSearchData()), EsOrgOrder::getOrderNo, StrUtil.split(condition.getSearchData(), StrUtil.COMMA));
          break;
        case THIRD_ORDER_NO:
          wrapper.in(StrUtil.isNotBlank(condition.getSearchData()), EsOrgOrder::getThirdOrderNo, StrUtil.split(condition.getSearchData(), StrUtil.COMMA));
          break;
        case PLATFORM:
          wrapper.in(StrUtil.isNotBlank(condition.getSearchData()), EsOrgOrder::getPlatformCode, StrUtil.split(condition.getSearchData(), StrUtil.COMMA));
          break;
        case ERP_INFO:
          wrapper.and(StrUtil.isNotBlank(condition.getSearchData()), w->
              w.nested(ORG_ORDER_DETAIL_FIELD, item ->
                          item.eq(String.format("%s.%s", ORG_ORDER_DETAIL_FIELD, FieldUtils.val(EsOrgOrderDetail::getErpCode)), condition.getSearchData())
                          .or()
                          .match(String.format("%s.%s", ORG_ORDER_DETAIL_FIELD, FieldUtils.val(EsOrgOrderDetail::getItemName)), condition.getSearchData())
                  , ScoreMode.Avg)
          );
          break;
        case ORG_REL:
          wrapper.and(StrUtil.isNotBlank(condition.getSearchData()), w->
              w.in(EsOrgOrder::getSourceOrgCode, StrUtil.split(condition.getSearchData(), StrUtil.COMMA))
                  .or()
                  .in(EsOrgOrder::getOrgCode, StrUtil.split(condition.getSearchData(), StrUtil.COMMA))
          );
          break;
        case ORDER_FLAG:
          wrapper.match(StrUtil.isNotBlank(condition.getSearchData()), EsOrgOrder::getOrderFlags, condition.getSearchData());
          break;
        case ORDER_FLAG_AND:
          if(StrUtil.isNotBlank(condition.getSearchData())){
            List<String> orderFlags = StrUtil.split(condition.getSearchData(), StrUtil.COMMA);
            for (String orderFlag : orderFlags) {
              wrapper.match(EsOrgOrder::getOrderFlags, orderFlag);
            }
          }
          break;
        case ORDER_FLAG_OR:
          if(StrUtil.isNotBlank(condition.getSearchData())){
            List<String> orderFlags = StrUtil.split(condition.getSearchData(), StrUtil.COMMA);
            wrapper.and(w -> {
              IntStream.range(0, orderFlags.size()).forEach(i -> {
                String orderFlag = orderFlags.get(i);
                w.match(EsOrgOrder::getOrderFlags, orderFlag);
                if (i < orderFlags.size() - 1) {
                  w.or();
                }
              });
            });
          }
          break;
        case ORDER_STATUS:
          if(StrUtil.isNotBlank(condition.getSearchData())){
            List<Integer> orderSatusList = StrUtil.split(condition.getSearchData(), StrUtil.COMMA).stream().map(Integer::parseInt).collect(Collectors.toList());
            wrapper.in(StrUtil.isNotBlank(condition.getSearchData()), EsOrgOrder::getOrderStatus, orderSatusList);
          }
          break;
        default:
          break;
      }
    }
    return wrapper;

  }

  public static LambdaEsQueryWrapper<EsOrgRefund> buildEsQueryForOrgRefundAmountStatic(EsOrgRefundAmountStaticReqDTO reqDto) {
    LambdaEsQueryWrapper<EsOrgRefund> wrapper = buildEsQueryForOrgRefundQueryCondition(reqDto.getSearchConditionList());
    wrapper.sum(EsOrgRefund::getRefundBillAmount);
    return wrapper;
  }

  public static LambdaEsQueryWrapper<EsOrgRefund> buildEsQueryForOrgRefundPageQuery(EsOrgRefundPageQueryReqDTO reqDto) {
    LambdaEsQueryWrapper<EsOrgRefund> wrapper = buildEsQueryForOrgRefundQueryCondition(reqDto.getSearchConditionList());
    List<OrderByParam> orderByParams = new ArrayList<>();
    if (CollUtil.isNotEmpty(reqDto.getSortParams())) {
      orderByParams = reqDto.getSortParams().stream().map(sort -> {
        OrderByParam orderByParam = new OrderByParam();
        orderByParam.setSort(sort.getSort().name());
        orderByParam.setOrder(sort.getSortBy());
        return orderByParam;
      }).collect(Collectors.toList());
      wrapper.orderBy(orderByParams);
    } else {
      wrapper.orderBy(true, false, EsOrgRefund::getCreated, EsOrgRefund::getId);
    }
    return wrapper;
  }

  private static LambdaEsQueryWrapper<EsOrgRefund> buildEsQueryForOrgRefundQueryCondition(List<EsOrgRefundSearchConditionDTO> searchConditionList) {
    LambdaEsQueryWrapper<EsOrgRefund> wrapper = EsWrappers.lambdaQuery(EsOrgRefund.class);
    if(CollUtil.isEmpty(searchConditionList)){
      return wrapper;
    }
    for (EsOrgRefundSearchConditionDTO condition : searchConditionList) {
      switch (condition.getSearchType()) {
        case ORDER_SOURCE:
          wrapper.eq(StrUtil.isNotBlank(condition.getSearchData()), EsOrgRefund::getOrderSource, condition.getSearchData());
          break;
        case STORE_CODE:
          wrapper.in(StrUtil.isNotBlank(condition.getSearchData()), EsOrgRefund::getStoreCode, StrUtil.split(condition.getSearchData(), StrUtil.COMMA));
          break;
        case ORG_CODE:
          if(StrUtil.isNotBlank(condition.getSearchData())) {
            List<String> orgList = StrUtil.split(condition.getSearchData(), StrUtil.COMMA);
            wrapper.in(EsOrgRefund::getOrgCode, orgList);
            //设置路由，目前会按照org_code进行分片
            if(orgList.size() == 1){
              wrapper.routing(condition.getSearchData());
            }
          }
          break;
        case SOURCE_STORE_CODE:
          wrapper.in(StrUtil.isNotBlank(condition.getSearchData()), EsOrgRefund::getSourceStoreCode, StrUtil.split(condition.getSearchData(), StrUtil.COMMA));
          break;
        case SOURCE_ORG_CODE:
          wrapper.in(StrUtil.isNotBlank(condition.getSearchData()), EsOrgRefund::getOrgCode, StrUtil.split(condition.getSearchData(), StrUtil.COMMA));
          break;
        case START_DATE:
          if (StrUtil.isNotBlank(condition.getSearchData())) {
            wrapper.ge(EsOrgRefund::getOrderCreated, DateUtil.formatDateTime(new Date(Long.parseLong(condition.getSearchData()) * 1000L)));
          }
          break;
        case END_DATE:
          if (StrUtil.isNotBlank(condition.getSearchData())) {
            wrapper.le(EsOrgRefund::getOrderCreated, DateUtil.formatDateTime(new Date(Long.parseLong(condition.getSearchData()) * 1000L)));
          }
          break;
        case REFUND_NO:
          wrapper.in(StrUtil.isNotBlank(condition.getSearchData()), EsOrgRefund::getRefundNo, StrUtil.split(condition.getSearchData(), StrUtil.COMMA));
          break;
        case THIRD_REFUND_NO:
          wrapper.in(StrUtil.isNotBlank(condition.getSearchData()), EsOrgRefund::getThirdRefundNo, StrUtil.split(condition.getSearchData(), StrUtil.COMMA));
          break;
        case PLATFORM:
          wrapper.in(StrUtil.isNotBlank(condition.getSearchData()), EsOrgRefund::getPlatformCode, StrUtil.split(condition.getSearchData(), StrUtil.COMMA));
          break;
        case ERP_INFO:
          wrapper.and(StrUtil.isNotBlank(condition.getSearchData()), w->
              w.nested(ORG_ORDER_DETAIL_FIELD, item ->
                  item.eq(String.format("%s.%s", ORG_REFUND_DETAIL_FIELD, FieldUtils.val(EsOrgRefundDetail::getErpCode)), condition.getSearchData())
                  .or().match(String.format("%s.%s", ORG_REFUND_DETAIL_FIELD, FieldUtils.val(EsOrgRefundDetail::getItemName)), condition.getSearchData()))
          );
          break;
        case ORG_REL:
          wrapper.and(StrUtil.isNotBlank(condition.getSearchData()), w->
              w.in(EsOrgRefund::getSourceOrgCode, StrUtil.split(condition.getSearchData(), StrUtil.COMMA))
                  .or()
                  .in(EsOrgRefund::getOrgCode, StrUtil.split(condition.getSearchData(), StrUtil.COMMA))
          );
          break;
        case REFUND_FLAG:
          wrapper.match(StrUtil.isNotBlank(condition.getSearchData()), EsOrgRefund::getRefundFlags, condition.getSearchData());
          break;
        case ORDER_NO:
          wrapper.in(StrUtil.isNotBlank(condition.getSearchData()), EsOrgRefund::getOrderNo, StrUtil.split(condition.getSearchData(), StrUtil.COMMA));
          break;
        case REFUND_STATUS:
          if(StrUtil.isNotBlank(condition.getSearchData())){
            List<Integer> refundSatusList = StrUtil.split(condition.getSearchData(), StrUtil.COMMA).stream().map(Integer::parseInt).collect(Collectors.toList());
            wrapper.in(StrUtil.isNotBlank(condition.getSearchData()), EsOrgRefund::getRefundStatus, refundSatusList);
          }
          break;
        default:
          break;
      }
    }
    return wrapper;

  }

  public static LambdaEsQueryWrapper<EsOrgOrder> buildEsQueryForOrgOrderDetailQuery(EsOrderBaseReqDTO reqDto) {
    LambdaEsQueryWrapper<EsOrgOrder> wrapper = EsWrappers.lambdaQuery(EsOrgOrder.class);
    wrapper.eq(EsOrgOrder::getOrderNo, reqDto.getOrderNo());
    if (StrUtil.isNotBlank(reqDto.getOrgCode())) {
      wrapper.routing(reqDto.getOrgCode());
    }
    return wrapper;
  }

  public static LambdaEsQueryWrapper<EsOrgRefund> buildEsQueryForOrgRefundDetailQuery(EsRefundBaseReqDTO reqDto) {
    LambdaEsQueryWrapper<EsOrgRefund> wrapper = EsWrappers.lambdaQuery(EsOrgRefund.class);
    wrapper.eq(EsOrgRefund::getRefundNo, reqDto.getRefundNo());
    if (StrUtil.isNotBlank(reqDto.getOrgCode())) {
      wrapper.routing(reqDto.getOrgCode());
    }
    return wrapper;
  }

  public static LambdaEsQueryWrapper<EsOrgOrder> buildEsQueryForOrgOrderAmountStatic(EsOrgOrderCountStatisticReqDTO reqDto) {
    LambdaEsQueryWrapper<EsOrgOrder> wrapper = buildEsQueryForOrgOrderQueryCondition(reqDto.getSearchConditionList());
    switch (reqDto.getStatisticType()){
      case ORDER_SOURCE:
        wrapper.groupBy(EsOrgOrder::getOrderSource);
        break;
      case ORDER_STATUS:
        wrapper.groupBy(EsOrgOrder::getOrderStatus);
        break;
      default:
        break;
    }
    return wrapper;
  }

  public static LambdaEsQueryWrapper<EsOrderWorldOrder> buildEsQueryForOrderPageQuery(EsOrderWorldOrderPageQueryReq request) {
    LambdaEsQueryWrapper<EsOrderWorldOrder> wrapper = buildEsQueryForOrderQueryConditionWithFilter(request.getSearchConditionList());
    List<OrderByParam> orderByParams = new ArrayList<>();
    List<EsOrderWorldOrderSearchCondition> searchConditionList = request.getSearchConditionList();
    if(CollUtil.isNotEmpty(searchConditionList)) {
      long count = searchConditionList.stream()
          .filter(condition -> condition.getSearchType() == OrderSearchConditionEnum.ERP_INFO)
          .count();
      if (count > 0) {
        //如果根据商品信息查询，需要根据分数进行排序，先暂时写死
        OrderByParam score = new OrderByParam();
        score.setSort("DESC");
        score.setOrder("_score");
        orderByParams.add(score);
      }
    }
    if (CollUtil.isNotEmpty(request.getSortParams())) {
      orderByParams.addAll(request.getSortParams().stream().map(sort -> {
        OrderByParam orderByParam = new OrderByParam();
        orderByParam.setSort(sort.getSort().name());
        orderByParam.setOrder(sort.getSortBy());
        return orderByParam;
      }).collect(Collectors.toList()));
    } else {
      OrderByParam orderByParam = new OrderByParam();
      orderByParam.setSort(SortEnum.DESC.name());
      orderByParam.setOrder("created");
      orderByParams.add(orderByParam);
    }
    wrapper.orderBy(orderByParams);
    return wrapper;
  }


  private static LambdaEsQueryWrapper<EsOrderWorldOrder> buildEsQueryForOrderQueryCondition(List<EsOrderWorldOrderSearchCondition> searchConditionList) {
    LambdaEsQueryWrapper<EsOrderWorldOrder> wrapper = EsWrappers.lambdaQuery(EsOrderWorldOrder.class);
    if(CollUtil.isEmpty(searchConditionList)){
      return wrapper;
    }
    //是否需要包含无效订单
    long count = searchConditionList.stream().filter(condition -> condition.getSearchType() == OrderSearchConditionEnum.INCLUDE_INVALID && DsConstants.INTEGER_ONE.toString().equals(condition.getSearchData())).count();
    if(count <= 0){
      wrapper.eq(EsOrderWorldOrder::getValid, DsConstants.INTEGER_ONE);
    }
    for (EsOrderWorldOrderSearchCondition condition : searchConditionList) {
      switch (condition.getSearchType()) {
        case STORE_CODE:
          wrapper.in(StrUtil.isNotBlank(condition.getSearchData()), EsOrderWorldOrder::getOnlineStoreCode, StrUtil.split(condition.getSearchData(), StrUtil.COMMA));
          break;
        case ORG_CODE:
          if(StrUtil.isNotBlank(condition.getSearchData())) {
            List<String> orgList = StrUtil.split(condition.getSearchData(), StrUtil.COMMA);
            wrapper.in(EsOrderWorldOrder::getOrganizationCode, orgList);
            //设置路由，目前会按照org_code进行分片
            if(orgList.size() == 1){
              wrapper.routing(condition.getSearchData());
            }
          }
          break;
        case ORDER_START_CREATED:
          if (StrUtil.isNotBlank(condition.getSearchData())) {
            wrapper.ge(EsOrderWorldOrder::getCreated, DateUtil.formatDateTime(new Date(Long.parseLong(condition.getSearchData()) * 1000L)));
          }
          break;
        case ORDER_END_CREATED:
          if (StrUtil.isNotBlank(condition.getSearchData())) {
            wrapper.le(EsOrderWorldOrder::getCreated, DateUtil.formatDateTime(new Date(Long.parseLong(condition.getSearchData()) * 1000L)));
          }
          break;
        case ORDER_NO:
          wrapper.in(StrUtil.isNotBlank(condition.getSearchData()), EsOrderWorldOrder::getOrderNo, StrUtil.split(condition.getSearchData(), StrUtil.COMMA));
          break;
        case THIRD_ORDER_NO:
          wrapper.in(StrUtil.isNotBlank(condition.getSearchData()), EsOrderWorldOrder::getThirdOrderNo, StrUtil.split(condition.getSearchData(), StrUtil.COMMA));
          break;
        case ERP_INFO:
          wrapper.and(StrUtil.isNotBlank(condition.getSearchData()), w->
              w.nested(ORDER_WORLD_ORDER_DETAIL_FIELD, item ->
                      item.eq(String.format("%s.%s", ORDER_WORLD_ORDER_DETAIL_FIELD, FieldUtils.val(EsOrderWorldOrderDetail::getErpCode)), condition.getSearchData())
                          .or()
                          .match(String.format("%s.%s", ORDER_WORLD_ORDER_DETAIL_FIELD, FieldUtils.val(EsOrderWorldOrderDetail::getErpName)), condition.getSearchData())
                  , ScoreMode.Avg)
          );
          break;
        case PLATFORM:
          wrapper.in(StrUtil.isNotBlank(condition.getSearchData()), EsOrderWorldOrder::getThirdPlatformCode, StrUtil.split(condition.getSearchData(), StrUtil.COMMA));
          break;
        case ORDER_FLAG:
          wrapper.match(StrUtil.isNotBlank(condition.getSearchData()), EsOrderWorldOrder::getOrderFlags, condition.getSearchData());
          break;
        case ORDER_FLAG_AND:
          if(StrUtil.isNotBlank(condition.getSearchData())){
            List<String> orderFlags = StrUtil.split(condition.getSearchData(), StrUtil.COMMA);
            for (String orderFlag : orderFlags) {
              wrapper.match(EsOrderWorldOrder::getOrderFlags, orderFlag);
            }
          }
          break;
        case ORDER_FLAG_OR:
          if(StrUtil.isNotBlank(condition.getSearchData())){
            List<String> orderFlags = StrUtil.split(condition.getSearchData(), StrUtil.COMMA);
            wrapper.and(w -> {
              IntStream.range(0, orderFlags.size()).forEach(i -> {
                String orderFlag = orderFlags.get(i);
                w.match(EsOrderWorldOrder::getOrderFlags, orderFlag);
                if (i < orderFlags.size() - 1) {
                  w.or();
                }
              });
            });
          }
          break;
        case ORDER_TYPE:
          wrapper.match(StrUtil.isNotBlank(condition.getSearchData()), EsOrderWorldOrder::getOrderTypes, condition.getSearchData());
          break;
        case ORDER_STATUS:
          if(StrUtil.isNotBlank(condition.getSearchData())){
            List<String> orderSatusList = StrUtil.split(condition.getSearchData(), StrUtil.COMMA);
            wrapper.in(StrUtil.isNotBlank(condition.getSearchData()), EsOrderWorldOrder::getOrderMainStatus, orderSatusList);
          }
          break;
        case ORDER_PAYMENT_STATUS:
          if(StrUtil.isNotBlank(condition.getSearchData())){
            List<String> orderSatusList = StrUtil.split(condition.getSearchData(), StrUtil.COMMA);
            wrapper.in(StrUtil.isNotBlank(condition.getSearchData()), EsOrderWorldOrder::getPaymentStatus, orderSatusList);
          }
          break;
        case BUSINESS_TYPE:
          wrapper.in(StrUtil.isNotBlank(condition.getSearchData()), EsOrderWorldOrder::getBusinessType, StrUtil.split(condition.getSearchData(), StrUtil.COMMA));
          break;
        case TRANSACTION_CHANNEL:
          wrapper.in(StrUtil.isNotBlank(condition.getSearchData()), EsOrderWorldOrder::getTransChannel, StrUtil.split(condition.getSearchData(), StrUtil.COMMA));
          break;
        case USER_ID:
          wrapper.in(StrUtil.isNotBlank(condition.getSearchData()), EsOrderWorldOrder::getUserId, StrUtil.split(condition.getSearchData(), StrUtil.COMMA));
          break;
        case LAUNCH_USER_ID:
          wrapper.in(StrUtil.isNotBlank(condition.getSearchData()), EsOrderWorldOrder::getLaunchUserId, StrUtil.split(condition.getSearchData(), StrUtil.COMMA));
          break;
        case LAUNCHED_ORG_CODE:
          wrapper.in(StrUtil.isNotBlank(condition.getSearchData()), EsOrderWorldOrder::getLaunchOrganizationCode, StrUtil.split(condition.getSearchData(), StrUtil.COMMA));
          break;
        case COMPANY_CODE:
          wrapper.in(StrUtil.isNotBlank(condition.getSearchData()), EsOrderWorldOrder::getCompanyCode, StrUtil.split(condition.getSearchData(), StrUtil.COMMA));
          break;
        case PAY_TYPE:
          wrapper.match(StrUtil.isNotBlank(condition.getSearchData()), EsOrderWorldOrder::getPayTypes, condition.getSearchData());
          break;
        case ABNORMAL_TYPE:
          wrapper.eq(StrUtil.isNotBlank(condition.getSearchData()), EsOrderWorldOrder::getAbnormalType, condition.getSearchData());
          break;
        default:
          break;
      }
    }
    return wrapper;
  }

  private static LambdaEsQueryWrapper<EsOrderWorldOrder> buildEsQueryForOrderQueryConditionWithFilter(List<EsOrderWorldOrderSearchCondition> searchConditionList) {
    LambdaEsQueryWrapper<EsOrderWorldOrder> wrapper = EsWrappers.lambdaQuery(EsOrderWorldOrder.class);
    if(CollUtil.isEmpty(searchConditionList)){
      return wrapper;
    }
    Consumer<LambdaEsQueryWrapper<EsOrderWorldOrder>> filterConsumer = w->{};
    Consumer<LambdaEsQueryWrapper<EsOrderWorldOrder>> mustConsumer = w->{};
    //是否需要包含无效订单
    long count = searchConditionList.stream().filter(condition -> condition.getSearchType() == OrderSearchConditionEnum.INCLUDE_INVALID && DsConstants.INTEGER_ONE.toString().equals(condition.getSearchData())).count();
    if(count <= 0){
      filterConsumer = filterConsumer.andThen(w -> w.eq(EsOrderWorldOrder::getValid, DsConstants.INTEGER_ONE));
    }

    for (EsOrderWorldOrderSearchCondition condition : searchConditionList) {
      switch (condition.getSearchType()) {
        case STORE_CODE:
          filterConsumer = filterConsumer.andThen(w -> w.in(StrUtil.isNotBlank(condition.getSearchData()), EsOrderWorldOrder::getOnlineStoreCode, StrUtil.split(condition.getSearchData(), StrUtil.COMMA)));
          break;
        case ORG_CODE:
          if(StrUtil.isNotBlank(condition.getSearchData())) {
            List<String> orgList = StrUtil.split(condition.getSearchData(), StrUtil.COMMA);
            filterConsumer = filterConsumer.andThen(w->w.in(EsOrderWorldOrder::getOrganizationCode, orgList));
            //设置路由，目前会按照org_code进行分片
            if(orgList.size() == 1){
              wrapper.routing(condition.getSearchData());
            }
          }
          break;
        case ORDER_START_CREATED:
          if (StrUtil.isNotBlank(condition.getSearchData())) {
            filterConsumer = filterConsumer.andThen(w -> w.ge(EsOrderWorldOrder::getCreated, DateUtil.formatDateTime(new Date(Long.parseLong(condition.getSearchData()) * 1000L))));
          }
          break;
        case ORDER_END_CREATED:
          if (StrUtil.isNotBlank(condition.getSearchData())) {
            filterConsumer = filterConsumer.andThen(w -> w.le(EsOrderWorldOrder::getCreated, DateUtil.formatDateTime(new Date(Long.parseLong(condition.getSearchData()) * 1000L))));
          }
          break;
        case ORDER_NO:
          filterConsumer = filterConsumer.andThen(w -> w.in(StrUtil.isNotBlank(condition.getSearchData()), EsOrderWorldOrder::getOrderNo, StrUtil.split(condition.getSearchData(), StrUtil.COMMA)));
          break;
        case THIRD_ORDER_NO:
          filterConsumer = filterConsumer.andThen(w -> w.in(StrUtil.isNotBlank(condition.getSearchData()), EsOrderWorldOrder::getThirdOrderNo, StrUtil.split(condition.getSearchData(), StrUtil.COMMA)));
          break;
        case ERP_INFO:
          mustConsumer = mustConsumer.andThen(w->
                  w.nested(StrUtil.isNotBlank(condition.getSearchData()),ORDER_WORLD_ORDER_DETAIL_FIELD, item ->
                          item.eq(String.format("%s.%s", ORDER_WORLD_ORDER_DETAIL_FIELD, FieldUtils.val(EsOrderWorldOrderDetail::getErpCode)), condition.getSearchData())
                              .or()
                              .match(String.format("%s.%s", ORDER_WORLD_ORDER_DETAIL_FIELD, FieldUtils.val(EsOrderWorldOrderDetail::getErpName)), condition.getSearchData())
                  , ScoreMode.Avg)
          );
          break;
        case PLATFORM:
          filterConsumer = filterConsumer.andThen(w -> w.in(StrUtil.isNotBlank(condition.getSearchData()), EsOrderWorldOrder::getThirdPlatformCode, StrUtil.split(condition.getSearchData(), StrUtil.COMMA)));
          break;
        case ORDER_FLAG:
          filterConsumer = filterConsumer.andThen(w -> w.match(StrUtil.isNotBlank(condition.getSearchData()), EsOrderWorldOrder::getOrderFlags, condition.getSearchData()));
          break;
        case ORDER_FLAG_AND:
          if(StrUtil.isNotBlank(condition.getSearchData())){
            List<String> orderFlags = StrUtil.split(condition.getSearchData(), StrUtil.COMMA);
            for (String orderFlag : orderFlags) {
              filterConsumer = filterConsumer.andThen(w -> w.match(EsOrderWorldOrder::getOrderFlags, orderFlag));
            }
          }
          break;
        case ORDER_FLAG_OR:
          if(StrUtil.isNotBlank(condition.getSearchData())){
            List<String> orderFlags = StrUtil.split(condition.getSearchData(), StrUtil.COMMA);
            filterConsumer = filterConsumer.andThen(flag->{
              flag.and(w -> {
                IntStream.range(0, orderFlags.size()).forEach(i -> {
                  String orderFlag = orderFlags.get(i);
                  w.match(EsOrderWorldOrder::getOrderFlags, orderFlag);
                  if (i < orderFlags.size() - 1) {
                    w.or();
                  }
                });
              });
            });
          }
          break;
        case ORDER_TYPE:
          filterConsumer = filterConsumer.andThen(w -> w.match(StrUtil.isNotBlank(condition.getSearchData()), EsOrderWorldOrder::getOrderTypes, condition.getSearchData()));
          break;
        case ORDER_STATUS:
          filterConsumer = filterConsumer.andThen(w -> w.in(StrUtil.isNotBlank(condition.getSearchData()), EsOrderWorldOrder::getOrderMainStatus, StrUtil.split(condition.getSearchData(), StrUtil.COMMA)));
          break;
        case ORDER_PAYMENT_STATUS:
          filterConsumer = filterConsumer.andThen(w -> w.in(StrUtil.isNotBlank(condition.getSearchData()), EsOrderWorldOrder::getPaymentStatus, StrUtil.split(condition.getSearchData(), StrUtil.COMMA)));
          break;
        case BUSINESS_TYPE:
          filterConsumer = filterConsumer.andThen(w -> w.in(StrUtil.isNotBlank(condition.getSearchData()), EsOrderWorldOrder::getBusinessType, StrUtil.split(condition.getSearchData(), StrUtil.COMMA)));
          break;
        case TRANSACTION_CHANNEL:
          filterConsumer = filterConsumer.andThen(w -> w.in(StrUtil.isNotBlank(condition.getSearchData()), EsOrderWorldOrder::getTransChannel, StrUtil.split(condition.getSearchData(), StrUtil.COMMA)));
          break;
        case USER_ID:
          filterConsumer = filterConsumer.andThen(w -> w.in(StrUtil.isNotBlank(condition.getSearchData()), EsOrderWorldOrder::getUserCardNo, StrUtil.split(condition.getSearchData(), StrUtil.COMMA)));
          break;
        case LAUNCH_USER_ID:
          filterConsumer = filterConsumer.andThen(w -> w.in(StrUtil.isNotBlank(condition.getSearchData()), EsOrderWorldOrder::getLaunchUserId, StrUtil.split(condition.getSearchData(), StrUtil.COMMA)));
          break;
        case LAUNCHED_ORG_CODE:
          filterConsumer = filterConsumer.andThen(w -> w.in(StrUtil.isNotBlank(condition.getSearchData()), EsOrderWorldOrder::getLaunchOrganizationCode, StrUtil.split(condition.getSearchData(), StrUtil.COMMA)));
          break;
        case COMPANY_CODE:
          filterConsumer = filterConsumer.andThen(w -> w.in(StrUtil.isNotBlank(condition.getSearchData()), EsOrderWorldOrder::getCompanyCode, StrUtil.split(condition.getSearchData(), StrUtil.COMMA)));
          break;
        case PAY_TYPE:
          filterConsumer = filterConsumer.andThen(w -> w.match(StrUtil.isNotBlank(condition.getSearchData()), EsOrderWorldOrder::getPayTypes, condition.getSearchData()));
          break;
        case ABNORMAL_TYPE:
          filterConsumer = filterConsumer.andThen(w -> w.eq(StrUtil.isNotBlank(condition.getSearchData()), EsOrderWorldOrder::getAbnormalType, condition.getSearchData()));
          break;
        default:
          break;
      }
    }
    wrapper.filter(filterConsumer);
    wrapper.must(mustConsumer);
    return wrapper;

  }

  public static LambdaEsQueryWrapper<EsOrderWorldOrder> buildEsQueryForOrderCountStatic(EsOrderWorldOrderCountStatisticReq request) {
    LambdaEsQueryWrapper<EsOrderWorldOrder> wrapper = buildEsQueryForOrderQueryConditionWithFilter(request.getSearchConditionList());
    switch (request.getStatisticType()){
      case ORDER_STATUS:
        wrapper.groupBy(EsOrderWorldOrder::getOrderMainStatus);
        break;
      default:
        break;
    }
    return wrapper;
  }

  public static LambdaEsQueryWrapper<EsOrderWorldOrder> buildEsQueryForOrderCountWithCondition(EsOrderWorldOrderCountWithConditionReq request) {
    return buildEsQueryForOrderQueryConditionWithFilter(request.getSearchConditionList());
  }

  public static LambdaEsQueryWrapper<EsOrderWorldRefundOrder> buildEsQueryForRefundOrderPageQuery(EsOrderWorldRefundOrderPageQueryReq request) {
    LambdaEsQueryWrapper<EsOrderWorldRefundOrder> wrapper = buildEsQueryForRefundOrderQueryCondition(request.getSearchConditionList());
    List<OrderByParam> orderByParams = new ArrayList<>();
    List<EsOrderWorldRefundOrderSearchCondition> searchConditionList = request.getSearchConditionList();
    if (CollUtil.isNotEmpty(searchConditionList)) {
      long count = searchConditionList.stream()
          .filter(condition -> condition.getSearchType() == RefundOrderSearchConditionEnum.ERP_INFO)
          .count();
      if (count > 0) {
        //如果根据商品信息查询，需要根据分数进行排序，先暂时写死
        OrderByParam score = new OrderByParam();
        score.setSort("DESC");
        score.setOrder("_score");
        orderByParams.add(score);
      }
    }

    if (CollUtil.isNotEmpty(request.getSortParams())) {
      orderByParams.addAll(request.getSortParams().stream().map(sort -> {
        OrderByParam orderByParam = new OrderByParam();
        orderByParam.setSort(sort.getSort().name());
        orderByParam.setOrder(sort.getSortBy());
        return orderByParam;
      }).collect(Collectors.toList()));
    } else {
      OrderByParam orderByParam = new OrderByParam();
      orderByParam.setSort(SortEnum.DESC.name());
      orderByParam.setOrder("created");
      orderByParams.add(orderByParam);
    }
    wrapper.orderBy(orderByParams);
    return wrapper;
  }

  private static LambdaEsQueryWrapper<EsOrderWorldRefundOrder> buildEsQueryForRefundOrderQueryCondition(List<EsOrderWorldRefundOrderSearchCondition> searchConditionList) {
    LambdaEsQueryWrapper<EsOrderWorldRefundOrder> wrapper = EsWrappers.lambdaQuery(EsOrderWorldRefundOrder.class);
    if (CollUtil.isEmpty(searchConditionList)) {
      return wrapper;
    }
    //是否需要包含无效订单
    long count = searchConditionList.stream()
        .filter(condition -> condition.getSearchType() == RefundOrderSearchConditionEnum.INCLUDE_INVALID && DsConstants.INTEGER_ONE.toString()
            .equals(condition.getSearchData())).count();
    if (count <= 0) {
      wrapper.eq(EsOrderWorldRefundOrder::getValid, DsConstants.INTEGER_ONE);
    }
    for (EsOrderWorldRefundOrderSearchCondition condition : searchConditionList) {
      switch (condition.getSearchType()) {
        case ORG_CODE:
          if (StrUtil.isNotBlank(condition.getSearchData())) {
            List<String> orgList = StrUtil.split(condition.getSearchData(), StrUtil.COMMA);
            wrapper.in(EsOrderWorldRefundOrder::getOrganizationCode, orgList);
            //设置路由，目前会按照org_code进行分片
            if (orgList.size() == 1) {
              wrapper.routing(condition.getSearchData());
            }
          }
          break;
        case START_DATE:
          if (StrUtil.isNotBlank(condition.getSearchData())) {
            wrapper.ge(EsOrderWorldRefundOrder::getCreated, DateUtil.formatDateTime(new Date(Long.parseLong(condition.getSearchData()) * 1000L)));
          }
          break;
        case END_DATE:
          if (StrUtil.isNotBlank(condition.getSearchData())) {
            wrapper.le(EsOrderWorldRefundOrder::getCreated, DateUtil.formatDateTime(new Date(Long.parseLong(condition.getSearchData()) * 1000L)));
          }
          break;
        case THIRD_REFUND_NO:
          wrapper.in(StrUtil.isNotBlank(condition.getSearchData()), EsOrderWorldRefundOrder::getThirdRefundNo, StrUtil.split(condition.getSearchData(), StrUtil.COMMA));
          break;
        case AFTER_SALE_NO:
          wrapper.in(StrUtil.isNotBlank(condition.getSearchData()), EsOrderWorldRefundOrder::getAfterSaleNo, StrUtil.split(condition.getSearchData(), StrUtil.COMMA));
          break;
        case ERP_INFO:
          wrapper.and(StrUtil.isNotBlank(condition.getSearchData()), w -> w.nested(ORDER_WORLD_REFUND_ORDER_DETAIL_FIELD, item -> item.eq(String.format("%s.%s", ORDER_WORLD_REFUND_ORDER_DETAIL_FIELD, FieldUtils.val(EsOrderWorldRefundOrderDetail::getErpCode)), condition.getSearchData())
              .or()
              .match(String.format("%s.%s", ORDER_WORLD_REFUND_ORDER_DETAIL_FIELD, FieldUtils.val(EsOrderWorldRefundOrderDetail::getErpName)), condition.getSearchData()), ScoreMode.Avg));
          break;
        case PLATFORM:
          wrapper.in(StrUtil.isNotBlank(condition.getSearchData()), EsOrderWorldRefundOrder::getThirdPlatformCode, StrUtil.split(condition.getSearchData(), StrUtil.COMMA));
          break;
        case ORDER_NO:
          wrapper.in(StrUtil.isNotBlank(condition.getSearchData()), EsOrderWorldRefundOrder::getOrderNo, StrUtil.split(condition.getSearchData(), StrUtil.COMMA));
          break;
        case THIRD_ORDER_NO:
          wrapper.in(StrUtil.isNotBlank(condition.getSearchData()), EsOrderWorldRefundOrder::getThirdOrderNo, StrUtil.split(condition.getSearchData(), StrUtil.COMMA));
          break;
        case REFUND_FLAG:
          wrapper.match(StrUtil.isNotBlank(condition.getSearchData()), EsOrderWorldRefundOrder::getRefundFlags, condition.getSearchData());
          break;
        case REFUND_FLAG_AND:
          if (StrUtil.isNotBlank(condition.getSearchData())) {
            List<String> orderFlags = StrUtil.split(condition.getSearchData(), StrUtil.COMMA);
            for (String orderFlag : orderFlags) {
              wrapper.match(EsOrderWorldRefundOrder::getRefundFlags, orderFlag);
            }
          }
          break;
        case REFUND_FLAG_OR:
          if (StrUtil.isNotBlank(condition.getSearchData())) {
            List<String> orderFlags = StrUtil.split(condition.getSearchData(), StrUtil.COMMA);
            wrapper.and(w -> {
              IntStream.range(0, orderFlags.size()).forEach(i -> {
                String orderFlag = orderFlags.get(i);
                w.match(EsOrderWorldRefundOrder::getRefundFlags, orderFlag);
                if (i < orderFlags.size() - 1) {
                  w.or();
                }
              });
            });
          }
          break;
        case REFUND_STATE:
          if (StrUtil.isNotBlank(condition.getSearchData())) {
            List<String> orderSatusList = StrUtil.split(condition.getSearchData(), StrUtil.COMMA);
            wrapper.in(StrUtil.isNotBlank(condition.getSearchData()), EsOrderWorldRefundOrder::getRefundStatus, orderSatusList);
          }
          break;
        case BUSINESS_TYPE:
          wrapper.in(StrUtil.isNotBlank(condition.getSearchData()), EsOrderWorldRefundOrder::getBusinessType, StrUtil.split(condition.getSearchData(), StrUtil.COMMA));
          break;
        case TRANSACTION_CHANNEL:
          wrapper.in(StrUtil.isNotBlank(condition.getSearchData()), EsOrderWorldRefundOrder::getTransactionChannel, StrUtil.split(condition.getSearchData(), StrUtil.COMMA));
          break;
        case USER_ID:
          wrapper.in(StrUtil.isNotBlank(condition.getSearchData()), EsOrderWorldRefundOrder::getUserId, StrUtil.split(condition.getSearchData(), StrUtil.COMMA));
          break;
        case LAUNCH_USER_ID:
          wrapper.in(StrUtil.isNotBlank(condition.getSearchData()), EsOrderWorldRefundOrder::getLaunchUserId, StrUtil.split(condition.getSearchData(), StrUtil.COMMA));
          break;
        case LAUNCHED_ORG_CODE:
          wrapper.in(StrUtil.isNotBlank(condition.getSearchData()), EsOrderWorldRefundOrder::getLaunchOrganizationCode, StrUtil.split(condition.getSearchData(), StrUtil.COMMA));
          break;
        case COMPANY_CODE:
          wrapper.in(StrUtil.isNotBlank(condition.getSearchData()), EsOrderWorldRefundOrder::getCompanyCode, StrUtil.split(condition.getSearchData(), StrUtil.COMMA));
          break;
        case AFTER_SALE_SCOPE:
          wrapper.match(StrUtil.isNotBlank(condition.getSearchData()), EsOrderWorldRefundOrder::getAfterSaleScope, condition.getSearchData());
          break;
        case AFTER_SALE_TYPE:
          wrapper.match(StrUtil.isNotBlank(condition.getSearchData()), EsOrderWorldRefundOrder::getAfterSaleType, condition.getSearchData());
          break;
        default:
          break;
      }
    }
    return wrapper;
  }
}
