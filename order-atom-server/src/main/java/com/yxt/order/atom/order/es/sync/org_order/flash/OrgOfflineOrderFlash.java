package com.yxt.order.atom.order.es.sync.org_order.flash;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yxt.order.atom.common.LocalConst.DATA_SOURCE;
import com.yxt.order.atom.order.entity.OfflineOrderDO;
import com.yxt.order.atom.order.es.sync.AbstractFlashEnhance;
import com.yxt.order.atom.order.es.sync.DoToCanalDtoWrapper;
import com.yxt.order.atom.order.es.sync.FlashQueryWrapper;
import com.yxt.order.atom.order.es.sync.OrgOrderScene;
import com.yxt.order.atom.order.es.sync.data.CanalOfflineOrder;
import com.yxt.order.atom.order.es.sync.data.CanalOfflineOrder.OfflineOrder;
import com.yxt.order.atom.order.es.sync.org_order.handler.OrgOfflineOrderHandler;
import com.yxt.order.atom.order.mapper.OfflineOrderMapper;
import com.yxt.order.types.offline.NumberType;
import com.yxt.order.types.order.enums.OrderSource;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

@Component
@DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
public class OrgOfflineOrderFlash extends AbstractFlashEnhance<OfflineOrderDO, OfflineOrder, OrgOrderScene> {

  @Resource
  private OrgOfflineOrderHandler orgOfflineOrderHandler;

  @Resource
  private OfflineOrderMapper offlineOrderMapper;

  @Override
  protected Long queryCursorStartId() {
    return offlineOrderMapper.selectMinId(getFlashParam());
  }

  @Override
  protected Long queryCursorEndId() {
    return offlineOrderMapper.selectMaxId(getFlashParam());
  }

  @Override
  protected List<OfflineOrderDO> getSourceList() {
    LambdaQueryWrapper<OfflineOrderDO> query = FlashQueryWrapper.offlineOrderFlashQuery(getFlashParam(),defaultLimit());
    return offlineOrderMapper.selectList(query);
  }


  @Override
  protected List<OfflineOrder> assembleTargetData(List<OfflineOrderDO> sourceList) {
    return sourceList.stream().map(DoToCanalDtoWrapper::getOfflineOrder).collect(Collectors.toList());
  }



  @Override
  protected void flash(List<OfflineOrder> offlineOrderList) {
    CanalOfflineOrder canalOfflineOrder = new CanalOfflineOrder();
    canalOfflineOrder.setData(offlineOrderList);
    orgOfflineOrderHandler.manualFlash(canalOfflineOrder);
  }

  @Override
  protected Boolean isSharding() {
    return Boolean.TRUE;
  }

  @Override
  protected Boolean isHandleNonVipData() {
    return Boolean.TRUE;
  }

  @Override
  public OrderSource getOrderSource() {
    return OrderSource.OFFLINE;
  }

  @Override
  public NumberType getOrderType() {
    return NumberType.ORDER;
  }
}
