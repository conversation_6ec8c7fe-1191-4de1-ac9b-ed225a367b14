package com.yxt.order.atom.order.repository.batch;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yxt.order.atom.order.entity.RefundCheckDO;
import com.yxt.order.atom.order.entity.RefundLogDO;
import com.yxt.order.atom.order.mapper.RefundCheckMapper;
import com.yxt.order.atom.order.mapper.RefundLogMapper;
import org.springframework.stereotype.Repository;


@Repository
public class RefundCheckBatchRepository extends
    ServiceImpl<RefundCheckMapper, RefundCheckDO> {

}

