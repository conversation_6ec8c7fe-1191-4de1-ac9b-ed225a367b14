package com.yxt.order.atom.order_world.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 线下正单医保信息
 */
@Data
@TableName("offline_order_med_ins_settle")
public class OrderMedInsSettleDO {

  /**
   * 主键
   */
  @TableId(value = "id", type = IdType.AUTO)
  private Long id;

  /**
   * 内部订单号
   */
  private String orderNo;

  /**
   * 流水号
   */
  private String serialNo;

  /**
   * 三方销售单号
   */
  private String thirdOrderNo;

  /**
   * 发票号码(结算ID setl_id)
   */
  private String invoiceNo;

  /**
   * 定点医疗机构名称
   */
  private String hospitalName;

  /**
   * 人员姓名(psn_name)
   */
  private String name;

  /**
   * 人员类别(psn_type)
   */
  private String personType;

  /**
   * 人员类别Name(psn_type)
   */
  private String personTypeName;

  /**
   * 个人编号(人员编号 psn_no)
   */
  private String personNo;

  /**
   * 处方号(就诊ID mdtrt_id)
   */
  private String prescriptionNo;

  /**
   * IC卡号
   */
  private String icCardNo;

  /**
   * 个人账户支付(个人账户支出 acct_pay)
   */
  private BigDecimal acctPay;

  /**
   * 统筹支出(基金支付总额 fund_pay_sumamt)
   */
  private BigDecimal fundPay;

  /**
   * 自付现金
   */
  private BigDecimal cashPay;

  /**
   * 医疗类别
   */
  private String medType;

  /**
   * 医疗类别_name
   */
  private String medTypeName;

  /**
   * PAY-付款 REFUND-退款
   */
  private String transactionType;

  /**
   * 交易编码
   */
  private String transactionCode;

  /**
   * 定点医疗机构编码
   */
  private String hospitalCode;

  /**
   * 结算时间
   */
  private Long setlTime;

  /**
   * 清算类别
   */
  private String clearingType;

  /**
   * 清算类别_name
   */
  private String clearingTypeName;

  /**
   * 是否已退款 UN_REFUND-未退款 REFUNDED-已退款
   */
  private String isRefunded;

  /**
   * 退款时间
   */
  private Long refundTime;

  /**
   * 原流水号
   */
  private String origSerialNo;

  /**
   * 下账时间
   */
  private Long billTime;

  /**
   * 创建人
   */
  private String createdBy;

  /**
   * 更新人
   */
  private String updatedBy;

  /**
   * 创建时间
   */
  private Date createdTime;

  /**
   * 更新时间
   */
  private Date updatedTime;

  /**
   * 数据版本，每次update+1
   */
  private Long version;

  /**
   * 平台创建时间
   */
  private Date created;

  /**
   * 平台更新时间
   */
  private Date updated;

  /**
   * 系统创建时间
   */
  private Date sysCreateTime;

  /**
   * 系统更新时间
   */
  private Date sysUpdateTime;
}
