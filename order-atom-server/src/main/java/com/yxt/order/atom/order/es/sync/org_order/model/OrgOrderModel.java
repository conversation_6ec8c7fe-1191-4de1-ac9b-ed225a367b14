package com.yxt.order.atom.order.es.sync.org_order.model;

import cn.hutool.core.collection.CollUtil;
import com.yxt.common.logic.es.BaseEsIndexModel;
import com.yxt.order.atom.order.es.doc.EsOrgOrder;
import com.yxt.order.atom.order.es.doc.EsOrgOrderDetail;
import com.yxt.order.types.order.enums.OrderFlagEnum;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class OrgOrderModel extends BaseEsIndexModel {

  /**
   * 系统单号
   */
  private String orderNo;

  /**
   * 三方订单号
   */
  private String thirdOrderNo;

  /**
   * 下单时间
   */
  private Date created;

  /**
   * 创建时间
   */
  private Date createTime;

  /**
   * 支付时间
   */
  private Date payTime;

  /**
   * 支付日期（冗余）
   */
  private String payDate;

  /**
   * 线上门店编码
   */
  private String storeCode;

  /**
   * 机构编码（线下实际发货门店）
   */
  private String orgCode;

  /**
   * 下单线上门店编码
   */
  private String sourceStoreCode;

  /**
   * 下单线下机构编码
   */
  private String sourceOrgCode;

  /**
   * 订单状态 5待处理,10待接单,20待拣货,30待配送, 40待收货,100已完成,102已取消,101已关闭
   */
  private Integer orderStatus;

  /**
   * 下账状态 20, "待锁定" 30, "待下账" 99, "下账失败" 100, "已下账" 110, "已取消" 120,"已退款"
   */
  private Integer erpStatus;

  /**
   * 下账时间
   */
  private Date erpTime;

  /**
   * 零售流水
   */
  private String erpSaleNo;

  /**
   * 配送方式
   */
  private String deliveryType;

  /**
   * 订单来源 ONLINE-线上订单 OFFLINE-线下订单
   */
  private String orderSource;

  /**
   * HAIDIAN-海典 、 KE_CHUAN-科传 、 JD_DAOJIA-京东到家 、 MEITUAN-美团 、 E_BAI-饿百 、 YD_JIA-微商城 、
   * PING_AN_CENTRAL-平安中心仓 、 PA_COMMON_O2O-平安O2O 、 PA_CITY-平安城市仓 、 ALI_HEALTH-阿里健康 、 JD_HEALTH-京东健康
   * 、 TY_O2O-电商第三方标准平台 、 INHERIT_TM_TCG-淘宝 、 DOUDIAN-抖店 、 ZHIFUBAO-支付宝小程序 、 PDD_B2C-拼多多B2C 、
   * JD_B2C-京东B2C 、 KUAI_SHOU-快手 、 OTHER-其他渠道 、 POS_HD_H1-海典H1 、 POS_HD_H2-海典H2 、 POS_KC-科传
   */
  private String platformCode;

  /**
   * 订单标记
   */
  private List<OrderFlagEnum> orderFlags;

  /**
   * 异常标记
   */
  private String lockFlag;

  /**
   * 下账金额
   */
  private BigDecimal billAmount;

  /**
   * 订单明细
   */
  private List<OrgOrderDetailModel> detailList;

  /**
   * 会员编码(唯一值)
   */
  private String userCardNo;

  /**
   * 会员ID (心云)
   */
  private String userId;

  /**
   * 门店类型 DIRECT_SALES-直营 JOIN-加盟
   */
  private String storeType;

  /**
   * 服务模式 O2O B2C B2B
   */
  private String serviceMode;

  // 模型自由字段,其他表适配该字段
  // 默认0-未删除
  private Long deleted;

  public void addOrderFlags(OrderFlagEnum orderFlag) {
    if (CollUtil.isEmpty(this.orderFlags)) {
      this.orderFlags = new ArrayList<>();
    }
    this.orderFlags.add(orderFlag);
  }

  public String routeKey(){
    return this.orgCode;
  }

  public String defineId(){
    return this.getPlatformCode() + "-" + this.getOrderNo();
  }

  public EsOrgOrder create() {
    EsOrgOrder esOrgOrder = new EsOrgOrder();

    esOrgOrder.setId(this.defineId());
    esOrgOrder.setOrderNo(this.orderNo);
    esOrgOrder.setThirdOrderNo(this.thirdOrderNo);
    esOrgOrder.setCreated(this.created);
    esOrgOrder.setCreateTime(this.createTime);
    esOrgOrder.setPayTime(this.payTime);
    esOrgOrder.setPayDate(this.payDate);
    esOrgOrder.setStoreCode(this.storeCode);
    esOrgOrder.setOrgCode(this.orgCode);
    esOrgOrder.setSourceStoreCode(this.sourceStoreCode);
    esOrgOrder.setSourceOrgCode(this.sourceOrgCode);
    esOrgOrder.setOrderStatus(this.orderStatus);
    esOrgOrder.setErpStatus(this.erpStatus);
    esOrgOrder.setErpTime(this.erpTime);
    esOrgOrder.setErpSaleNo(this.erpSaleNo);
    esOrgOrder.setDeliveryType(this.deliveryType);
    esOrgOrder.setOrderSource(this.orderSource);
    esOrgOrder.setPlatformCode(this.platformCode);
    esOrgOrder.setBillAmount(this.billAmount);
    if(CollUtil.isNotEmpty(this.getDetailList())){
      List<EsOrgOrderDetail> esOrgOrderDetails = this.getDetailList().stream().map(detail -> {
        EsOrgOrderDetail orderDetail = new EsOrgOrderDetail();
        orderDetail.setOrderDetailId(detail.getOrderDetailId());
        orderDetail.setErpCode(detail.getErpCode());
        orderDetail.setItemName(detail.getItemName());
        return orderDetail;
      }).collect(Collectors.toList());
      esOrgOrder.setDetailList(esOrgOrderDetails);
    }
    esOrgOrder.setUserCardNo(this.userCardNo);
    esOrgOrder.setUserId(this.userId);
    esOrgOrder.setStoreType(this.storeType);
    esOrgOrder.setServiceMode(this.serviceMode);
    esOrgOrder.setDeleted(this.deleted);
    if(CollUtil.isNotEmpty(this.orderFlags)){
      List<String> orderFlagList = this.orderFlags.stream().map(OrderFlagEnum::name).collect(Collectors.toList());
      esOrgOrder.setOrderFlags(orderFlagList);
    }
    esOrgOrder.setLockFlag(this.lockFlag);
    return esOrgOrder;
  }
}
