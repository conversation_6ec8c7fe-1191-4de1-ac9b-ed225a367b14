package com.yxt.order.atom.order.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * <p>
 * 退款单
 * </p>
 *
 * <AUTHOR>
 * @since 2020-01-08
 */
@Data
@TableName("refund_order")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class RefundOrderDO implements Serializable {

  private static final long serialVersionUID = 1L;

  /**
   * 记录ID
   */
  @TableId(value = "id", type = IdType.AUTO)
  @ApiModelProperty(value = "记录id")
  private Long id;

  /**
   * 退款单号，雪花算法
   */
  @JsonFormat(shape = JsonFormat.Shape.STRING)
  private Long refundNo;

  /**
   * 三方平台退款ID
   */
  @ApiModelProperty(value = "第三方平台退款ID")
  private String thirdRefundNo;

  /**
   * 订单号
   */
  @JsonFormat(shape = JsonFormat.Shape.STRING)
  @ApiModelProperty(value = "订单号")
  private Long orderNo;

  /**
   * 第三方平台订单号
   */
  @ApiModelProperty(value = "第三方平台订单号")
  private String thirdOrderNo;

  /**
   * 退款类型,0部分退款，1全额退款
   */
  @ApiModelProperty(value = " 退款类型,0部分退款，1全额退款")
  private String type;

  /**
   * 第三方退款单状态
   */
  @ApiModelProperty(value = "平台退款单状态")
  private String thirdStatus;

  /**
   * 退款单状态
   */
  @ApiModelProperty(value = "退款单状态 10 待退款 ;20 待退货; 100 已完成; 102 已拒绝; 103 已取消")
  private Integer state;

  /**
   * 第三方平台编码
   */
  @ApiModelProperty(value = "第三方平台编码")
  private String thirdPlatformCode;

  /**
   * 商户编码
   */
  @ApiModelProperty(value = "商户编码")
  private String merCode;

  /**
   * 网店编码
   */
  @ApiModelProperty(value = "网店编码")
  private String clientCode;

  /**
   * 线下门店编码
   */
  @ApiModelProperty(value = "线下门店编码")
  private String organizationCode;

  /**
   * 线上门店编码
   */
  @ApiModelProperty(value = "线上门店编码")
  private String onlineStoreCode;

  /**
   * 来源线下门店编码
   */
  @ApiModelProperty(value = "来源线下门店编码")
  private String sourceOrganizationCode;

  /**
   * 来源线上门店编码
   */
  @ApiModelProperty(value = "来源线上门店编码")
  private String sourceOnlineStoreCode;

  /**
   * 服务模式 O2O B2C
   */
  @ApiModelProperty(value = "服务模式 O2O B2C")
  private String serviceMode;

  /**
   * 退款商品总金额
   */
  private BigDecimal totalFoodAmount;

  /**
   * 退款商品退用户金额
   */
  @ApiModelProperty(value = "退款商品退用户金额")
  private BigDecimal totalAmount;

  /**
   * 退买家总金额
   */
  @ApiModelProperty(value = "退买家总金额")
  private BigDecimal consumerRefund;

  /**
   * 商家退款总金额
   */
  @ApiModelProperty(value = "商家退款总金额")
  private BigDecimal shopRefund;

  /**
   * 退还佣金
   */
  @ApiModelProperty(value = "返还佣金")
  private BigDecimal feeRefund;

  /**
   * 退平台优惠
   */
  @ApiModelProperty(value = "退平台优惠")
  private BigDecimal platformDiscountRefund;

  /**
   * 退还商家优惠
   */
  @ApiModelProperty(value = "退还商家优惠")
  private BigDecimal shopDiscountRefund;

  /**
   * 审核员id
   */
  @ApiModelProperty(value = "审核员id")
  private String checkerId;

  /**
   * 退款原因
   */
  @ApiModelProperty(value = "退款原因")
  private String reason;

  /**
   * 退款描述
   */
  @ApiModelProperty(value = "退款描述")
  @TableField("`desc`")
  private String desc;

  /**
   * 创建时间
   */
  @ApiModelProperty(value = "创建时间")
  private Date createTime;

  /**
   * 修改时间
   */
  @ApiModelProperty(value = "修改时间")
  private Date modifyTime;

  /**
   * 邮费退款金额
   */
  @ApiModelProperty(value = "邮费退款金额")
  private BigDecimal postageAmount;

  /**
   * 退用户配送费金额(单位元)
   */
  @ApiModelProperty(value = "退用户配送费金额(单位元)")
  private BigDecimal userPostage;

  /**
   * 商家退还给平台补贴的金额(单位元)
   */
  @ApiModelProperty(value = "商家退还给平台补贴的金额(单位元)")
  private BigDecimal platformPostage;

  /**
   * 退平台配送费
   */
  private BigDecimal platformRefundDeliveryFee;

  /**
   * 退商家配送费
   */
  private BigDecimal merchantRefundPostFee;

  /**
   * 退平台包装费
   */
  private BigDecimal platformRefundPackFee;

  /**
   * 退商家包装费
   */
  private BigDecimal merchantRefundPackFee;

  /**
   * 退商品明细优惠
   */
  private BigDecimal detailDiscountAmount;

  /**
   * 退款单零售流水
   */
  @ApiModelProperty(value = "退款单零售流水")
  private String erpRefundNo;

  /**
   * 退款单erp状态
   */
  @ApiModelProperty(value = "下账状态: 20  待下帐  100 已下账  120 已取消")
  private Integer erpState;

  @ApiModelProperty("下账时间")
  private Date billTime;

  @ApiModelProperty(value = "是否调用ERP接口标识（0不调用 1调用）")
  private Integer callErpFlag;

  @ApiModelProperty(value = "退款完成时间")
  private Date completeTime;

  @ApiModelProperty(value = "下账类型(O2O)：1仅退款 2退货退款")
  private Integer billType;
  /**
   * 退健康贝数量
   */
  private Integer healthNum;

  @ApiModelProperty(value = "未下账的部分退款单，是否已重新计算原单财务数据等金额。0-未计算 1-已计算")
  private Integer reCalculateOriginOrderFlag;

  @ApiModelProperty(value = "售后单类型 0:退款售后 1:退货售后")
  private Integer afterSaleType;
  @ApiModelProperty(value = "退货售后单上门取件，预计送达时间开始")
  private Date afterSaleGoodsTimeStart;
  @ApiModelProperty(value = "退货售后单上门取件，预计送达时间结束")
  private Date afterSaleGoodsTimeEnd;

  @ApiModelProperty(value = "退回的物流单号")
  private String expressNo;

  @ApiModelProperty(value = "退单类型")
  private String refundType;
  /**
   * 存储京东健康 额外信息，json串
   */
  private String extraInfo;


  /**
   * 退健康贝换算金额
   */
  private BigDecimal healthValue;

  /**
   * 1表示整单退完,是否是最后一单,1代表最后一次申请,可以根据这个字段判断是部分退款还是整单退款，1表示整单退完;
   * 判断是否整单退款，还需要结合afsServiceState(olstatus)中的状态，只有afsServiceState(olstatus)=32或114，且isLastApply=1才表示原订单整单退完。
   */
  private Integer lastApplyFlag;

  /**
   * 饿百获取佣金标识  1-已获取，0-未获取
   */
  private Integer ebaiCommissionFlag;

  /**
   * 平台迁移退款单号-旧系统迁移数据的退款单号
   */
  private String migrationRefundNo;

  @ApiModelProperty(value = "扩展信息Json See RefundOrderExtendInfo")
  private String extendInfo;

  @ApiModelProperty(value = "是否新订单，1-新 2-老")
  private String orderIsNew;

  /**
   * 退款申请时间
   */
  @ApiModelProperty(value = "退款申请时间")
  private Date refundApplicationTime;

  @ApiModelProperty(value = "系统订单号B2C")
  private Long omsOrderNo;

  @ApiModelProperty(value = "数据版本号")
  private Long dataVersion;

  /**
   * 京东健康额外信息
   */
  @Data
  @ToString
  public static class ExtraInfo {

    @ApiModelProperty(value = "京东健康-系统版本号")
    private Integer sysVersion;
    @ApiModelProperty(value = "京东健康-省份code")
    private String provinceCode;
    @ApiModelProperty(value = "京东健康-城市code")
    private String cityCode;
    @ApiModelProperty(value = "京东健康-区县code")
    private String countyCode;
    @ApiModelProperty(value = "京东健康-乡镇code")
    private String villageCode;
    @ApiModelProperty(value = "京东健康-申请详情id")
    private List<String> applyDetailIdList;
    @ApiModelProperty(value = "京东健康-退款单状态")
    private String olStatus;
  }

  /**
   * 京东到家额外信息
   */
  @Data
  @ToString
  public static class ExtraInfoJddj {

    @ApiModelProperty(value = "京东到家-配送员姓名，商家自送时没有")
    private String deliveryMan;
    @ApiModelProperty(value = "京东到家-配送员电话，商家自送时没有")
    private String deliveryMobile;
    @ApiModelProperty(value = "京东到家-运单状态，指众包配送的退货单的运单状态(0 待抢单,-1 取消,10 已抢单,20 配送中,30 已妥投);")
    private String deliveryState;
    @ApiModelProperty(value = "京东到家-配送员编号，商家自送时没有")
    private String deliveryManNo;
    @ApiModelProperty(value = "京东到家-退款单状态")
    private String olStatus;
  }

}
