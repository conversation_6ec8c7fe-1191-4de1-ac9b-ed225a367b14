package com.yxt.order.atom.order.converter;


import com.yxt.order.atom.order.entity.AccountOrderDO;
import com.yxt.order.atom.order.entity.OrderPickInfoDO;
import com.yxt.order.atom.sdk.common.data.AccountOrderDTO;
import com.yxt.order.atom.sdk.common.data.OrderPickInfoDTO;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;


/**
 * 线下单转换
 *
 * <AUTHOR> (moatkon)
 * @date 2024年04月07日 16:15
 * @email: <EMAIL>
 */
@Mapper
public interface AccountOrder2DtoConverter {

  AccountOrder2DtoConverter INSTANCE = Mappers.getMapper(AccountOrder2DtoConverter.class);

  AccountOrderDTO toDto( AccountOrderDO obj);

}
