package com.yxt.order.atom.order.es.sync.org_order.flash;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yxt.order.atom.common.LocalConst.DATA_SOURCE;
import com.yxt.order.atom.order.entity.OfflineRefundOrderDO;
import com.yxt.order.atom.order.es.sync.AbstractFlashEnhance;
import com.yxt.order.atom.order.es.sync.DoToCanalDtoWrapper;
import com.yxt.order.atom.order.es.sync.FlashQueryWrapper;
import com.yxt.order.atom.order.es.sync.OrgOrderScene;
import com.yxt.order.atom.order.es.sync.data.CanalOfflineRefundOrder;
import com.yxt.order.atom.order.es.sync.data.CanalOfflineRefundOrder.OfflineRefundOrder;
import com.yxt.order.atom.order.es.sync.org_order.handler.OrgOfflineRefundHandler;
import com.yxt.order.atom.order.mapper.OfflineRefundOrderMapper;
import com.yxt.order.types.offline.NumberType;
import com.yxt.order.types.order.enums.OrderSource;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

@Component
@DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
public class OrgOfflineRefundFlash extends AbstractFlashEnhance<OfflineRefundOrderDO, OfflineRefundOrder, OrgOrderScene> {

  @Resource
  private OfflineRefundOrderMapper offlineRefundOrderMapper;


  @Resource
  private OrgOfflineRefundHandler orgOfflineRefundHandler;


  @Override
  protected Long queryCursorStartId() {
    return offlineRefundOrderMapper.selectMinId(getFlashParam());
  }

  @Override
  protected Long queryCursorEndId() {
    return offlineRefundOrderMapper.selectMaxId(getFlashParam());
  }

  @Override
  protected List<OfflineRefundOrderDO> getSourceList() {
    LambdaQueryWrapper<OfflineRefundOrderDO> query = FlashQueryWrapper.offlineRefundOrderFlashQuery(getFlashParam(),defaultLimit());
    return offlineRefundOrderMapper.selectList(query);
  }

  @Override
  protected List<OfflineRefundOrder> assembleTargetData(List<OfflineRefundOrderDO> sourceList) {
    return sourceList.stream().map(DoToCanalDtoWrapper::getOfflineRefundOrder).collect(Collectors.toList());
  }

  @Override
  protected void flash(List<OfflineRefundOrder> offlineRefundOrders) {
    CanalOfflineRefundOrder canalOfflineRefundOrder = new CanalOfflineRefundOrder();
    canalOfflineRefundOrder.setData(offlineRefundOrders);
    orgOfflineRefundHandler.manualFlash(canalOfflineRefundOrder);
  }

  @Override
  protected Boolean isSharding() {
    return Boolean.TRUE;
  }

  /**
   * 是否处理非会员数据
   *
   * @return
   */
  @Override
  protected Boolean isHandleNonVipData() {
    return Boolean.TRUE;
  }

  @Override
  public OrderSource getOrderSource() {
    return OrderSource.OFFLINE;
  }

  @Override
  public NumberType getOrderType() {
    return NumberType.REFUND;
  }
}
