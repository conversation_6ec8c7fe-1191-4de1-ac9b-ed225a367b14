package com.yxt.order.atom.order.es.sync.consistency_check;

import com.yxt.common.logic.flash.FlashParam;
import com.yxt.order.atom.common.utils.OrderDateUtils;
import com.yxt.order.atom.order.es.doc.EsOmsOrderInfo;
import com.yxt.order.atom.order.es.mapper.EsOmsOrderInfoMapper;
import com.yxt.order.atom.order.es.sync.AbstractOrderConsistencyCheck;
import com.yxt.order.atom.order.es.sync.consistency_check.consistency_check_efficient_count.EsOmsOrderEfficientCount;
import com.yxt.order.atom.order.es.sync.oms_order_info.flash.OmsOrderInfoFlash;
import javax.annotation.Resource;
import org.dromara.easyes.core.conditions.select.LambdaEsQueryWrapper;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/1/21
 * @since 1.0
 * omsOrderInfo表ES一致性检查
 */
@Component
public class EsOmsOrderInfoCheck extends AbstractOrderConsistencyCheck {

    @Resource
    private EsOmsOrderEfficientCount esOmsOrderEfficientCount;

    @Resource
    private EsOmsOrderInfoMapper esOmsOrderInfoMapper;

    @Resource
    private OmsOrderInfoFlash omsOrderInfoFlash;

    @Override
    protected Long dbDscloudCount() {
        return esOmsOrderEfficientCount.fetchEfficientCount(getStartDate(),getEndDate());
    }

    @Override
    protected void compensate() {
        FlashParam flashParam = getFlashParam();
        omsOrderInfoFlash.startFlush(flashParam);
    }

    @Override
    protected ConsistencyNotify consistencyNotify() {
        return ConsistencyNotify.OMS_ORDER_INFO;
    }

    @Override
    protected Long esCount() {
        LambdaEsQueryWrapper<EsOmsOrderInfo> queryWrapper = new LambdaEsQueryWrapper<>();
        queryWrapper.ge(EsOmsOrderInfo::getCreated, OrderDateUtils.formatYYMMDD(getStartDate()));
        queryWrapper.le(EsOmsOrderInfo::getCreated,OrderDateUtils.formatYYMMDD(getEndDate()));
        return esOmsOrderInfoMapper.selectCount(queryWrapper);
    }
}
