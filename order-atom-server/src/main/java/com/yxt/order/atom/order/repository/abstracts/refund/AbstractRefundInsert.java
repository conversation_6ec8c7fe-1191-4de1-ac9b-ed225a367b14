package com.yxt.order.atom.order.repository.abstracts.refund;


import com.yxt.order.atom.order.repository.abstracts.order.SaveOrderOptionalDO;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年09月13日 11:46
 * @email: <EMAIL>
 */
public abstract class AbstractRefundInsert<T> {

  protected SaveRefundOptionalDO saveData;

  protected abstract Boolean canInsert();

  protected abstract Integer insert(T t);

  /**
   * 转换成目标对象T
   *
   * @return
   */
  protected abstract T data();


  public Integer exec(SaveRefundOptionalDO req) {

    init(req);

    if (!canInsert()) {
      return 0;
    }

    return insert(data());
  }

  private void init(SaveRefundOptionalDO saveData) {
    this.saveData = saveData;
  }


}
