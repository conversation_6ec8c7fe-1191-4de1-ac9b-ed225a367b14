package com.yxt.order.atom.order.repository.abstracts.order.insert;

import com.yxt.lang.util.JsonUtils;
import com.yxt.order.atom.order.entity.OriThirdOrderDetailDO;
import com.yxt.order.atom.order.repository.abstracts.order.AbstractInsert;
import com.yxt.order.atom.order.repository.batch.OriThirdOrderDetailBatchRepository;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年09月13日 14:57
 * @email: <EMAIL>
 */
@Component
public class OriThirdOrderDetailInsert extends AbstractInsert<List<OriThirdOrderDetailDO>> {

  @Resource
  private OriThirdOrderDetailBatchRepository oriThirdOrderDetailBatchRepository;

  @Override
  protected Boolean canInsert() {
    List<OriThirdOrderDetailDO> oriThirdOrderDetailDtoList = saveDataOptional.getOriThirdOrderDetailList();
    if (CollectionUtils.isEmpty(oriThirdOrderDetailDtoList)) {
      return Boolean.FALSE;
    }

    for (OriThirdOrderDetailDO oriThirdOrderDetailDTO : oriThirdOrderDetailDtoList) {
      if (StringUtils.isEmpty(oriThirdOrderDetailDTO.getOrderNo())) {
        throw new RuntimeException(String.format("原始三方订单明细订单号为空,%s",
            JsonUtils.toJson(oriThirdOrderDetailDTO)));
      }
    }

    return Boolean.TRUE;
  }

  @Override
  protected Integer insert(List<OriThirdOrderDetailDO> list) {
    return oriThirdOrderDetailBatchRepository.saveBatch(list) ? list.size() : 0;
  }

  @Override
  protected List<OriThirdOrderDetailDO> data() {
    return saveDataOptional.getOriThirdOrderDetailList();
  }
}
