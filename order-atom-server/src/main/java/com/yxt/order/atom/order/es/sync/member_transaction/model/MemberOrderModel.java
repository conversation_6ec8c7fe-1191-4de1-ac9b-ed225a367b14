package com.yxt.order.atom.order.es.sync.member_transaction.model;

import com.yxt.common.logic.es.BaseEsIndexModel;
import com.yxt.order.atom.order.es.doc.EsMemberOrder;
import com.yxt.order.atom.order.es.doc.EsMemberOrderDetail;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.Data;
import org.springframework.util.CollectionUtils;

/**
 * 会员订单
 *
 * @author: moatkon
 * @time: 2024/12/9 10:29
 */
@Data
public class MemberOrderModel  extends BaseEsIndexModel {

  /**
   * 会员ID (心云)
   */
  private String userId;

  /**
   * 下单时间
   */
  private Date created;

  /**
   * 创建时间
   */
  private Date createTime;

  /**
   * 门店
   */
  private String storeCode;

  /**
   * 会员编码(唯一值)
   */
  private String userCardNo;

  /**
   * 订单来源 ONLINE-线上订单 POS-线下订单
   */
  private String orderSource;

  /**
   * HAIDIAN-海典 、 KE_CHUAN-科传 、 JD_DAOJIA-京东到家 、 MEITUAN-美团 、 E_BAI-饿百 、 YD_JIA-微商城 、 PING_AN_CENTRAL-平安中心仓 、 PA_COMMON_O2O-平安O2O 、 PA_CITY-平安城市仓 、 ALI_HEALTH-阿里健康 、 JD_HEALTH-京东健康 、 TY_O2O-电商第三方标准平台 、 INHERIT_TM_TCG-淘宝 、 DOUDIAN-抖店 、 ZHIFUBAO-支付宝小程序 、 PDD_B2C-拼多多B2C 、 JD_B2C-京东B2C 、 KUAI_SHOU-快手 、 OTHER-其他渠道 、 POS_HD_H1-海典H1 、 POS_HD_H2-海典H2 、 POS_KC-科传
   */
  private String platformCode;

  /**
   * 订单状态 5待处理,10待接单,20待拣货,30待配送,
   *
   * 40待收货,100已完成,102已取消,101已关闭
   */
  private Integer orderStatus;

  /**
   * 三方订单号
   */
  private String thirdOrderNo;

  /**
   * 系统单号
   */
  private String orderNo;

  /**
   * 分公司Code
   */
  private String companyCode;

  /**
   * 门店类型 DIRECT_SALES-直营 JOIN-加盟
   */
  private String storeType;


  // 模型自由字段,其他表适配该字段
  // 默认0-未删除
  private Long deleted;


  private List<MemberOrderDetailModel> orderDetailModelList;


  public EsMemberOrder create() {
    EsMemberOrder esMemberOrder = new EsMemberOrder();
    esMemberOrder.setUserId(this.getUserId());
    esMemberOrder.setCreated(this.getCreated());
    esMemberOrder.setCreateTime(this.getCreateTime());
    esMemberOrder.setStoreCode(this.getStoreCode());
    esMemberOrder.setUserCardNo(this.getUserCardNo());
    esMemberOrder.setOrderSource(this.getOrderSource());
    esMemberOrder.setPlatformCode(this.getPlatformCode());
    esMemberOrder.setOrderStatus(this.getOrderStatus());
    esMemberOrder.setThirdOrderNo(this.getThirdOrderNo());
    esMemberOrder.setOrderNo(this.getOrderNo());
    esMemberOrder.setCompanyCode(this.getCompanyCode());
    esMemberOrder.setStoreType(this.getStoreType());
    esMemberOrder.setDeleted(this.getDeleted());
    if(Objects.isNull(this.getDeleted())){
      esMemberOrder.setDeleted(0L); // 表中无逻辑删除字段的,默认为0
    }
    esMemberOrder.setId(defineId()); // 自定义Id,避免重复

    List<MemberOrderDetailModel> detailModelList = this.getOrderDetailModelList();
    if(!CollectionUtils.isEmpty(detailModelList)){
      esMemberOrder.setEsMemberOrderDetailList(detailModelList.stream()
          .map(item->{
            EsMemberOrderDetail detail = new EsMemberOrderDetail();
            detail.setErpCode(item.getErpCode());
            detail.setErpName(item.getErpName());
            return detail;
          })
          .collect(Collectors.toList())
      );
    }

    return esMemberOrder;
  }

  public String routeKey(){
    return this.userId;
  }

  public String defineId(){
    return this.getPlatformCode()+"-"+this.getOrderNo();
  }

  @Data
  public static class MemberOrderDetailModel {

    private String erpCode;

    private String erpName;

  }

}
