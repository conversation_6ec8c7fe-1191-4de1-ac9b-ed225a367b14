package com.yxt.order.atom.order.repository.abstracts.order.update;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.yxt.order.atom.order.entity.OrderCommodityDetailCostPriceDO;
import com.yxt.order.atom.order.repository.abstracts.order.AbstractUpdate;
import com.yxt.order.atom.order.repository.batch.OrderCommodityDetailCostPriceBatchRepository;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

@Component
public class OrderCommodityDetailCostPriceUpdate extends AbstractUpdate<List<OrderCommodityDetailCostPriceDO>> {

  @Resource
  private OrderCommodityDetailCostPriceBatchRepository orderCommodityDetailCostPriceBatchRepository;
  
  @Override
  protected Boolean canUpdate() {
    return CollUtil.isNotEmpty(req.getOrderCommodityDetailCostPriceList());
  }

  @Override
  protected Integer update(List<OrderCommodityDetailCostPriceDO> orderCommodityDetailCostPriceDOList) {
    Set<Long> orderNos = orderCommodityDetailCostPriceDOList.stream().map(OrderCommodityDetailCostPriceDO::getOrderNo).collect(Collectors.toSet());
    List<OrderCommodityDetailCostPriceDO> existCostPrices = orderCommodityDetailCostPriceBatchRepository.list(new LambdaUpdateWrapper<OrderCommodityDetailCostPriceDO>().in(OrderCommodityDetailCostPriceDO::getOrderNo, orderNos));
    Map<String, OrderCommodityDetailCostPriceDO> existCostPriceMap = new HashMap<>();
    if(CollUtil.isNotEmpty(existCostPrices)){
      existCostPriceMap = existCostPrices.stream().collect(Collectors.toMap(OrderCommodityDetailCostPriceDO::uniqueKey, e->e,(k1,k2)->k1));
    }
    List<OrderCommodityDetailCostPriceDO> updateCostPrices = new ArrayList<>();
    List<OrderCommodityDetailCostPriceDO> insertCostPrices = new ArrayList<>();
    for (OrderCommodityDetailCostPriceDO orderCommodityDetailCostPrice : orderCommodityDetailCostPriceDOList) {
      if(existCostPriceMap.containsKey(orderCommodityDetailCostPrice.uniqueKey())){
        updateCostPrices.add(orderCommodityDetailCostPrice);
      }else {
        insertCostPrices.add(orderCommodityDetailCostPrice);
      }
    }
    if(CollUtil.isNotEmpty(updateCostPrices)){
      for (OrderCommodityDetailCostPriceDO updateCostPrice : updateCostPrices) {
        OrderCommodityDetailCostPriceDO existCostPrice = existCostPriceMap.get(updateCostPrice.uniqueKey());
        updateCostPrice.setId(existCostPrice.getId());
        if(req.getNeedUpdateCostFlag()){
          updateCostPrice.setAveragePrice(existCostPrice.getAveragePrice());
          updateCostPrice.setTaxPrice(existCostPrice.getTaxPrice());
          updateCostPrice.setTaxRate(existCostPrice.getTaxRate());
        }else {
          updateCostPrice.setBatchNo(existCostPrice.getBatchNo());
          updateCostPrice.setMakeNo(existCostPrice.getMakeNo());
          updateCostPrice.setCostPrice(existCostPrice.getCostPrice());
        }
      }
      orderCommodityDetailCostPriceBatchRepository.updateBatchById(updateCostPrices);
    }
    if(CollUtil.isNotEmpty(insertCostPrices)){
      orderCommodityDetailCostPriceBatchRepository.saveBatch(insertCostPrices);
    }
    return updateCostPrices.size() + insertCostPrices.size();
  }

  /**
   * 转换成目标对象T
   *
   * @return
   */
  @Override
  protected List<OrderCommodityDetailCostPriceDO> convert() {
    return BeanUtil.copyToList(req.getOrderCommodityDetailCostPriceList(), OrderCommodityDetailCostPriceDO.class);
  }
}
