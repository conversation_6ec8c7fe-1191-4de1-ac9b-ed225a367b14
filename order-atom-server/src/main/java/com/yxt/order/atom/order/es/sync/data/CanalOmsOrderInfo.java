package com.yxt.order.atom.order.es.sync.data;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.yxt.common.logic.canal.BaseCanalData;
import com.yxt.order.atom.order.es.sync.data.CanalOmsOrderInfo.OmsOrderInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年08月20日 14:06
 * @email: <EMAIL>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CanalOmsOrderInfo extends BaseCanalData<OmsOrderInfo> {

  @Data
  public static class OmsOrderInfo {

    @JsonProperty("oms_order_no")
    private Long omsOrderNo;

    @JsonProperty("deleted")
    private Long deleted;

  }
}
