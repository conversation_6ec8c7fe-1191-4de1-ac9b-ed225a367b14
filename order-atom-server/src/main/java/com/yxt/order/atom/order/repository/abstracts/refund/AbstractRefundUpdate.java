package com.yxt.order.atom.order.repository.abstracts.refund;


import com.yxt.order.atom.sdk.online_order.order_info.dto.req.UpdateOrderOptionalReq;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年09月13日 11:46
 * @email: <EMAIL>
 */
public abstract class AbstractRefundUpdate<T> {

  protected SaveRefundOptionalDO saveData;

  protected abstract Boolean canUpdate(); // notey 暂时不校验dataVersion

  protected abstract Integer update(T t);

  /**
   * 转换成目标对象T
   *
   * @return
   */
  protected abstract T data();


  public Integer exec(SaveRefundOptionalDO req) {

    init(req);

    if (!canUpdate()) {
      return 0;
    }

    return update(data());
  }

  private void init(SaveRefundOptionalDO saveData) {
    this.saveData = saveData;
  }


}
