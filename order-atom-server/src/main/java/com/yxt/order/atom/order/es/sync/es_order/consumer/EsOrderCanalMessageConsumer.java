package com.yxt.order.atom.order.es.sync.es_order.consumer;

import static com.yxt.order.atom.common.configration.KafkaConfig.CANAL_MESSAGE_GROUP_ID;

import com.fasterxml.jackson.core.type.TypeReference;
import com.yxt.common.logic.canal.AbstractCanalHandler;
import com.yxt.common.logic.canal.BaseCanalData;
import com.yxt.common.logic.es.BaseEsIndexModel;
import com.yxt.lang.util.JsonUtils;
import com.yxt.order.atom.order.es.sync.es_order.model.EsOrderIndexModel;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * 需要区分线上,线下
 *
 * <AUTHOR> (moatkon)
 * @date 2024年08月15日 14:29
 * @email: <EMAIL>
 */
@Slf4j
@Component
public class EsOrderCanalMessageConsumer {

  @Resource
  private List<AbstractCanalHandler<? extends BaseCanalData<?>, ? extends EsOrderIndexModel>> abstractCanalHandlerList;

  @KafkaListener(topics = {"${canal.support-chronic-diseases}"},
      groupId = CANAL_MESSAGE_GROUP_ID, containerFactory = "canalKafkaListenerFactory",concurrency = "6")
  public void supportChronicDiseases(List<ConsumerRecord<String, String>> consumerRecordList,
      Acknowledgment ack) {
    try {

      if (CollectionUtils.isEmpty(consumerRecordList)) {
        return;
      }

      List<String> messageList = consumerRecordList.stream()
          .filter(s -> !StringUtils.isEmpty(s.value()))
          .filter(s -> !JsonUtils.toObject(s.value(), new TypeReference<BaseCanalData<?>>() {})
              .getIsDdl())
          .map(ConsumerRecord::value)
          .collect(Collectors.toList());

      if (CollectionUtils.isEmpty(messageList)) {
        return;
      }

      for (String message : messageList) {
        try {
          for (AbstractCanalHandler<? extends BaseCanalData<?>, ? extends BaseEsIndexModel> abstractCanalHandler : abstractCanalHandlerList) {
            if (abstractCanalHandler.execBusiness(message)) {
              break;
            }
          }
        } catch (Exception e) {
          log.warn("支持慢病业务,同步到ES异常,入延迟处理线程池再次处理,当前异常原因(忽略):{},message: {},",
              e.getMessage(), message);
//          scheduledThreadPool.schedule(() -> delayHandle(message), 10, TimeUnit.SECONDS);
        }
      }

      ack.acknowledge();
    } catch (Exception e) {
      log.error("supportChronicDiseases consumer error,cause:{},data:{}", e.getMessage(),
          consumerRecordList, e);
    }
  }

  /**
   * 延迟处理
   *
   * @param message
   */
  private void delayHandle(String message) {
    try {
      for (AbstractCanalHandler<? extends BaseCanalData<?>, ? extends BaseEsIndexModel> abstractCanalHandler : abstractCanalHandlerList) {
        if (abstractCanalHandler.execBusiness(message)) {
          break;
        }
      }
    } catch (Exception e) {
      log.error("支持慢病业务,同步到ES异常后延迟处理依然异常,原因:{},message: {}", e.getMessage(),
          message);
    }
  }

}
