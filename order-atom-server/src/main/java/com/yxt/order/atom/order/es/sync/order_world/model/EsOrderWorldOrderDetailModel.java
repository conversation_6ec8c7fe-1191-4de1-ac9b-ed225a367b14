package com.yxt.order.atom.order.es.sync.order_world.model;

import lombok.Data;
import org.dromara.easyes.annotation.IndexField;
import org.dromara.easyes.annotation.rely.Analyzer;
import org.dromara.easyes.annotation.rely.FieldType;


@Data
public class EsOrderWorldOrderDetailModel {

  /**
   * 详情id
   */
  private String orderDetailNo;

  /**
   * 商品编码
   */
  private String erpCode;

  /**
   * 商品名称
   */
  private String erpName;
}
