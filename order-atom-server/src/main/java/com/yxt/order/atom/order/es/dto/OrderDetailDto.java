package com.yxt.order.atom.order.es.dto;

import java.math.BigDecimal;
import lombok.Data;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年08月15日 14:32
 * @email: <EMAIL>
 */
@Data
public class OrderDetailDto {

  private String commodityCode;//	商品编码
  private String commodityName;//商品名称
  private BigDecimal commodityCount;  //商品数量
  private Integer status; // 明细状态,B2C和O2O的明细有差异
  private String fiveClass; // 五级分类
}
