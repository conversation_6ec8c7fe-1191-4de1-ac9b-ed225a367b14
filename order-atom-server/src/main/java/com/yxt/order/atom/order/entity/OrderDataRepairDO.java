package com.yxt.order.atom.order.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yxt.order.types.offline.enums.PreCheckEnum;
import java.util.Date;
import lombok.Data;

@Data
@TableName("order_data_repair_2")
public class OrderDataRepairDO {

  @TableId(value = "id", type = IdType.AUTO)
  private Long id;

  private String scene;

  private String input;

  private String preCheck;
  private String preCheckFailed;

  private String repairResult;
  private String repairFailedReason;

  private String businessNo;

  private String createdBy;

  private String updatedBy;

  private Date createdTime;

  private Date updatedTime;

  private Long version;


  private String beforeImage;

  private String afterImage;


  public static OrderDataRepairDO create() {
    OrderDataRepairDO orderDataRepairDO = new OrderDataRepairDO();
//    orderDataRepairDO.setId();
//    orderDataRepairDO.setScene();
    orderDataRepairDO.setPreCheck(PreCheckEnum.WAIT.name());
//    orderDataRepairDO.setResult();
//    orderDataRepairDO.setReason();
//    orderDataRepairDO.setNumber();
    orderDataRepairDO.setCreatedBy("");
    orderDataRepairDO.setUpdatedBy("");
    orderDataRepairDO.setCreatedTime(new Date());
    orderDataRepairDO.setUpdatedTime(new Date());
    orderDataRepairDO.setVersion(1L);
//    orderDataRepairDO.setInput();
//    orderDataRepairDO.setCheckNote();
//    orderDataRepairDO.setBeforeImage();
//    orderDataRepairDO.setAfterImage();
    return orderDataRepairDO;
  }
}