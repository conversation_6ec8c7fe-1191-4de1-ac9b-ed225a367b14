package com.yxt.order.atom.common;

import java.nio.charset.StandardCharsets;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.apache.rocketmq.spring.core.RocketMQPushConsumerLifecycleListener;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年03月26日 18:31
 * @email: <EMAIL>
 */
@Slf4j
public abstract class AbstractRocketMQListenerEnhance implements RocketMQListener<MessageExt>,
    RocketMQPushConsumerLifecycleListener {

  abstract public void handleMsg(String message);


  @Override
  public void onMessage(MessageExt messageExt) {

    String msg = new String(messageExt.getBody(), StandardCharsets.UTF_8);

    try {
      handleMsg(msg);
    } catch (Exception e) {
      log.error("第{}次消息处理异常,{},{}", messageExt.getReconsumeTimes(), msg, e.getMessage());
      throw new RuntimeException(e);
    }

  }


  @Override
  public void prepareStart(DefaultMQPushConsumer defaultMQPushConsumer) {
    // 设置消费者消息重试次数
    defaultMQPushConsumer.setMaxReconsumeTimes(maxRetryTimes());
    defaultMQPushConsumer.setConsumeThreadMin(minConsumeThreadNum());
    defaultMQPushConsumer.setConsumeThreadMax(maxConsumeThreadNum());
    defaultMQPushConsumer.setPullBatchSize(32);
    defaultMQPushConsumer.setConsumeMessageBatchMaxSize(32);
  }

  public Integer maxRetryTimes() {
    return 1;
  }

  public Integer maxConsumeThreadNum() {
    return 32;
  }

  public Integer minConsumeThreadNum() {
    return 32;
  }
}
