package com.yxt.order.atom.order.repository.abstracts.order.delete;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yxt.order.atom.order.entity.OrderDeliveryAddressDO;
import com.yxt.order.atom.order.mapper.OrderDeliveryAddressMapper;
import com.yxt.order.atom.order.repository.abstracts.order.AbstractDelete;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年09月13日 11:52
 * @email: <EMAIL>
 */
@Component
public class OrderDeliveryAddressDelete extends AbstractDelete {

  @Resource
  private OrderDeliveryAddressMapper orderDeliveryAddressMapper;


  @Override
  protected Boolean canDelete() {
    return dto.getOrderDeliveryAddress();
  }

  @Override
  protected Integer delete() {
    LambdaQueryWrapper<OrderDeliveryAddressDO> query = new LambdaQueryWrapper<>();
    query.eq(OrderDeliveryAddressDO::getOrderNo, orderNo);
    return orderDeliveryAddressMapper.delete(query);
  }
}
