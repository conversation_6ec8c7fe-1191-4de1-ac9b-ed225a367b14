package com.yxt.order.atom.migration.dao;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yxt.order.atom.migration.dao.enums.MigrationOrderType;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * @author: moatkon
 * @time: 2025/3/28 15:33
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ToString
@TableName("offline_order_platform_error")
public class OfflineOrderPlatformErrorDO {

  @TableId(value = "id", type = IdType.AUTO)
  private Long id;

  /**
   * 迁移订单类型
   *
   * @see MigrationOrderType
   */
  private String orderType;

  /**
   * 业务单号
   */
  private String businessNo;


  /**
   * 店铺编码
   */
  private String storeCode;

  /**
   * 三方平台编码
   *
   * @see com.yxt.order.types.offline.enums.ThirdPlatformCodeEnum
   */
  private String thirdPlatformCode;

  /**
   * 三方平台单号
   */
  private String thirdBusinessNo;

  /**
   * 平台订单创建时间
   */
  private Date created;

  /**
   * 分表位置
   */
  private String shardingNo;


  /**
   * 备注
   */
  private String note;

  /**
   * 默认false
   */
  private String needDeleted;

  /**
   * 是否删除成功
   */
  private String deleteSuccess;

  private String targetSchema;

  private Long handId;

}
