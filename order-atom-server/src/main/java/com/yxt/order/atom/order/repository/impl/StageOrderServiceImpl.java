package com.yxt.order.atom.order.repository.impl;

import com.yxt.lang.util.JsonUtils;
import com.yxt.order.atom.order.mongo.StagingOrder;
import com.yxt.order.atom.order.repository.StageOrderService;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineOrderStagingReqDto;
import java.util.Date;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年08月13日 16:24
 * @email: <EMAIL>
 */
@Slf4j
@Service
public class StageOrderServiceImpl implements StageOrderService {

  @Resource
  private MongoTemplate mongoTemplate;

  @Override
  public Boolean exist(OfflineOrderStagingReqDto dto) {
    Query query = new Query(Criteria.where("numberType").is(dto.getNumberType())
        .and("stagingType").is(dto.getStagingType())
        .and("storeCode").is(dto.getStoreCode())
        .and("thirdPlatformCode").is(dto.getThirdPlatformCode())
        .and("data").is(dto.getData()));
    return mongoTemplate.exists(query, StagingOrder.class, StagingOrder.COLLECTION_NAME);
  }

  @Override
  public Boolean create(OfflineOrderStagingReqDto dto) {
    if (exist(dto)) {
      log.info("数据已存在,不入库,{}", JsonUtils.toJson(dto));
      return Boolean.TRUE;
    }

    StagingOrder stagingOrder = new StagingOrder();
    stagingOrder.setMigration(dto.getMigration());
    stagingOrder.setNumberType(dto.getNumberType());
    stagingOrder.setStagingType(dto.getStagingType());
    stagingOrder.setData(dto.getData());
    stagingOrder.setCreateTime(new Date());
    stagingOrder.setHandled(Boolean.FALSE);
    stagingOrder.setStoreCode(dto.getStoreCode());
    stagingOrder.setThirdPlatformCode(dto.getThirdPlatformCode());
    stagingOrder.setThirdOrderNo(dto.getThirdOrderNo());
    stagingOrder.setThirdRefundNo(dto.getThirdRefundNo());
    stagingOrder.setDefineNo(dto.getDefineNo());
    stagingOrder.setUserCardNo(dto.getUserCardNo());
    stagingOrder.setCreateDateTime(new Date());

    mongoTemplate.insert(stagingOrder, StagingOrder.COLLECTION_NAME);
    return Boolean.TRUE;
  }

  @Override
  public List<StagingOrder> queryRefundOrderFind404Order(Integer pageNo, Integer pageSize) {
    Query query = queryRefundOrderCanNotMatchOrder();
    PageRequest pageRequest = PageRequest.of(pageNo, pageSize);
    query.with(pageRequest);
    return mongoTemplate.find(query, StagingOrder.class,
        StagingOrder.COLLECTION_NAME);

  }

  @Override
  public Long countRefundOrderFind404Order() {
    return mongoTemplate.count(queryRefundOrderCanNotMatchOrder(), StagingOrder.class,
        StagingOrder.COLLECTION_NAME);
  }

  private Query queryRefundOrderCanNotMatchOrder() {
    return new Query(Criteria
        .where("numberType").is("REFUND")
        .and("stagingType").is("QUERY_ORDER_404_FROM_REFUND_NO")
    );
  }

  private Query queryUser404SQL() {
    return new Query(Criteria
        .where("stagingType").is("QUERY_MEMBER_404_FROM_API")
    );
  }

  @Override
  public void delete(StagingOrder stagingRefundOrder) {
    Assert.isTrue(!StringUtils.isEmpty(stagingRefundOrder.getId()), "id can not null");
    Query query = new Query(Criteria.where("id").is(stagingRefundOrder.getId()));
    mongoTemplate.remove(query, StagingOrder.class,
        StagingOrder.COLLECTION_NAME);
  }

  @Override
  public Long countUser404() {
    return mongoTemplate.count(queryUser404SQL(), StagingOrder.class,
        StagingOrder.COLLECTION_NAME);
  }

  @Override
  public List<StagingOrder> queryUser404(Integer pageNo, Integer pageSize) {
    Query query = queryUser404SQL();
    PageRequest pageRequest = PageRequest.of(pageNo, pageSize);
    query.with(pageRequest);
    return mongoTemplate.find(query, StagingOrder.class,
        StagingOrder.COLLECTION_NAME);
  }
}
