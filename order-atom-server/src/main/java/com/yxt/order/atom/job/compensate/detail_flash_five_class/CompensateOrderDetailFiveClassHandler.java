package com.yxt.order.atom.job.compensate.detail_flash_five_class;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yxt.order.atom.migration.service.MigrationCommonComponent;
import com.yxt.order.atom.migration.service.dto.GoodsInfo;
import com.yxt.order.atom.order.entity.OrderDetailDO;
import com.yxt.order.atom.order.es.sync.AbstractFlash;
import com.yxt.order.atom.order.mapper.OrderDetailMapper;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * 补偿订单明细五级分类-订单明细
 *
 * @author: moatkon
 * @time: 2025/2/10 11:20
 */
@Component
public class CompensateOrderDetailFiveClassHandler extends
    AbstractFlash<OrderDetailDO, OrderDetailDO, FlashDetailFiveClass> {

  @Resource
  private OrderDetailMapper orderDetailMapper;

  @Resource
  private MigrationCommonComponent migrationCommonComponent;

  @Value("${flashFiveClassLimit:500}")
  private Integer flashFiveClassLimit;

  @Override
  protected Long queryCursorStartId() {
    return orderDetailMapper.selectMinId(getFlashParam());
  }

  @Override
  protected Long queryCursorEndId() {
    return orderDetailMapper.selectMaxId(getFlashParam());
  }

  @Override
  protected List<OrderDetailDO> getSourceList() {
    LambdaQueryWrapper<OrderDetailDO> query = new LambdaQueryWrapper<>();
    query.ge(OrderDetailDO::getId, getFlashParam().getCursorStartId());
    query.lt(OrderDetailDO::getId, getFlashParam().getCursorStartId() + defaultLimit());
    return orderDetailMapper.selectList(query);
  }

  @Override
  protected List<OrderDetailDO> assembleTargetData(List<OrderDetailDO> offlineOrderDetailDOS) {
    return offlineOrderDetailDOS;
  }

  @Override
  protected void flash(List<OrderDetailDO> offlineOrderDetailDOS) {
    if (CollectionUtils.isEmpty(offlineOrderDetailDOS)) {
      return;
    }

    Set<String> erpCodeSet = offlineOrderDetailDOS.stream()
        .filter(CompensateOrderDetailFiveClassHandler::missFiveClass)
        .map(OrderDetailDO::getErpCode).collect(Collectors.toSet());
    if(CollectionUtils.isEmpty(erpCodeSet)){
      return;
    }

    Map<String, GoodsInfo> goodsInfoMap = migrationCommonComponent.getGoodsInfo(erpCodeSet);

    for (OrderDetailDO orderDetail : offlineOrderDetailDOS) {
      if (!missFiveClass(orderDetail) || Objects.isNull(orderDetail.getId())) {
        continue;
      }

      GoodsInfo goodsInfo = goodsInfoMap.get(orderDetail.getErpCode());
      if(Objects.isNull(goodsInfo)){
        continue;
      }


      orderDetail.setFiveClass(goodsInfo.getFiveClass());
      orderDetail.setFiveClassName(goodsInfo.getFiveClassName());
      orderDetailMapper.updateById(orderDetail);
    }

  }

  private static boolean missFiveClass(OrderDetailDO detailDO) {
    return StringUtils.isEmpty(detailDO.getFiveClass()) || StringUtils.isEmpty(
        detailDO.getFiveClassName());
  }

  @Override
  protected Integer defaultLimit() {
    return flashFiveClassLimit;
  }
}
