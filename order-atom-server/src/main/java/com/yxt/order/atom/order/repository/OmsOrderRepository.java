package com.yxt.order.atom.order.repository;

import com.yxt.order.atom.sdk.online_order.oms_order_info.dto.OmsOrderInfoResDto;
import com.yxt.order.atom.sdk.online_order.oms_order_info.dto.SimpleOmsOrderInfoDTO;
import com.yxt.order.atom.sdk.online_order.oms_order_info.dto.req.OmsOrderInfoQryReqDto;
import com.yxt.order.types.order.OmsOrderNo;
import com.yxt.order.types.order.OrderNo;
import java.util.List;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年04月07日 16:28
 * @email: <EMAIL>
 * <p>
 */
public interface OmsOrderRepository {


  List<SimpleOmsOrderInfoDTO> querySimpleOmsOrderList(OrderNo orderNo);
  OmsOrderInfoResDto getOmsOrderInfo(OmsOrderInfoQryReqDto omsOrderNo);
}
