package com.yxt.order.atom.order.converter;


import com.yxt.order.atom.order.entity.OrderInfoDO;
import com.yxt.order.atom.sdk.online_order.order_info.dto.OrderInfoResDto;
import com.yxt.order.atom.sdk.online_order.order_info.dto.OrderInfoResDto.Acceptor;
import com.yxt.order.atom.sdk.online_order.order_info.dto.OrderInfoResDto.AccountInfo;
import com.yxt.order.atom.sdk.online_order.order_info.dto.OrderInfoResDto.Canceler;
import com.yxt.order.atom.sdk.online_order.order_info.dto.OrderInfoResDto.ExOperator;
import com.yxt.order.atom.sdk.online_order.order_info.dto.OrderInfoResDto.InvoiceInfo;
import com.yxt.order.atom.sdk.online_order.order_info.dto.OrderInfoResDto.OrderLock;
import com.yxt.order.atom.sdk.online_order.order_info.dto.OrderInfoResDto.OrderMainInfo;
import com.yxt.order.atom.sdk.online_order.order_info.dto.OrderInfoResDto.OrderOrganization;
import com.yxt.order.atom.sdk.online_order.order_info.dto.OrderInfoResDto.OrderSimpleDelivery;
import com.yxt.order.atom.sdk.online_order.order_info.dto.OrderInfoResDto.OrderTypeFlags;
import com.yxt.order.atom.sdk.online_order.order_info.dto.OrderInfoResDto.Picker;
import com.yxt.order.types.order.DataVersion;
import com.yxt.order.types.order.OrderNo;
import com.yxt.order.types.order.ThirdOrderNo;
import com.yxt.order.types.order.enums.DeliveryTimeTypeEnum;
import com.yxt.order.types.order.enums.OrderErpStatusEnum;
import com.yxt.order.types.order.enums.OrderLockFlagEnum;
import com.yxt.order.types.order.enums.OrderRemindFlag;
import com.yxt.order.types.order.enums.OrderServiceModeEnum;
import com.yxt.order.types.order.enums.OrderStatusEnum;

/**
 * <AUTHOR> Runkang (moatkon)
 * @date 2024年03月01日 16:24
 * @email: <EMAIL>
 */

public class OrderInfoConverter {


  private OrderInfoConverter() {
    throw new UnsupportedOperationException(
        "OrderInfoConverter is a utility class and cannot be instantiated.");
  }


  public static OrderInfoResDto toOrderInfo(OrderInfoDO orderInfoDO) {
    if (orderInfoDO == null) {
      return null;
    }
    OrderInfoResDto resDto = new OrderInfoResDto();

    OrderMainInfo orderMainInfo = new OrderMainInfo();
    orderMainInfo.setId(orderInfoDO.getId());
    orderMainInfo.setOrderNo(OrderNo.orderNo(orderInfoDO.getOrderNo()));
    orderMainInfo.setThirdOrderNo(ThirdOrderNo.thirdOrderNo(orderInfoDO.getThirdOrderNo()));
    orderMainInfo.setDayNum(orderInfoDO.getDayNum());
    orderMainInfo.setOrderStatus(OrderStatusEnum.getOrderStatus(orderInfoDO.getOrderState()));
    orderMainInfo.setCreated(orderInfoDO.getCreated());
    orderMainInfo.setCompleteTime(orderInfoDO.getCompleteTime());
    orderMainInfo.setServiceMode(OrderServiceModeEnum.getByCode(orderInfoDO.getServiceMode()));
    orderMainInfo.setDataVersion(DataVersion.dataVersion(orderInfoDO.getDataVersion()));
    orderMainInfo.setFreightOrderNo(OrderNo.orderNo(orderInfoDO.getFreightOrderNo()));
    orderMainInfo.setOrderExtendInfoStr(orderInfoDO.getExtendInfo());
    orderMainInfo.setOrderOrganization(toOrderOrganization(orderInfoDO));
    orderMainInfo.setAccountInfo(toAccountInfo(orderInfoDO));
    orderMainInfo.setOrderLock(toOrderLock(orderInfoDO));
    orderMainInfo.setOrderSimpleDelivery(toOrderSimpleDelivery(orderInfoDO));
    orderMainInfo.setAcceptor(toAcceptor(orderInfoDO));
    orderMainInfo.setPicker(toPicker(orderInfoDO));
    orderMainInfo.setCanceler(toCanceler(orderInfoDO));
    orderMainInfo.setExpOperator(toExpOperator(orderInfoDO));
    orderMainInfo.setOrderTypeFlags(toOrderTypeFlags(orderInfoDO));
    orderMainInfo.setInvoiceInfo(toInvoiceInfo(orderInfoDO));

    resDto.setOrderMainInfo(orderMainInfo);
    return resDto;
  }

  private static InvoiceInfo toInvoiceInfo(OrderInfoDO orderInfoDO) {
    InvoiceInfo invoiceInfo = new InvoiceInfo();
    invoiceInfo.setNeedInvoiceFlag(orderInfoDO.getNeedInvoice());
    invoiceInfo.setInvoiceTitle(orderInfoDO.getInvoiceTitle());
    invoiceInfo.setInvoiceType(orderInfoDO.getInvoiceType());
    invoiceInfo.setInvoiceContent(orderInfoDO.getInvoiceContent());
    invoiceInfo.setTaxerId(orderInfoDO.getTaxerId());

    return invoiceInfo;

  }

  private static OrderTypeFlags toOrderTypeFlags(OrderInfoDO orderInfoDO) {

    OrderTypeFlags orderTypeFlags = new OrderTypeFlags();
    orderTypeFlags.setTransferDeliveryFlag(orderInfoDO.getTransferDelivery());
    orderTypeFlags.setAppointmentFlag(orderInfoDO.getAppointment());
    orderTypeFlags.setAppointmentBusinessFlag(orderInfoDO.getAppointmentBusinessFlag());
    orderTypeFlags.setAppointmentBusinessTypeFlag(orderInfoDO.getAppointmentBusinessType());
    orderTypeFlags.setRequestDeliverGoodsResultFlag(orderInfoDO.getRequestDeliverGoodsResult());
    orderTypeFlags.setDeliverGoodsRefuseReason(orderInfoDO.getDeliverGoodsRefuseReason());
    orderTypeFlags.setPrescriptionFlag(orderInfoDO.getPrescriptionFlag());
    orderTypeFlags.setIsPrescriptionFlag(orderInfoDO.getIsPrescription());
    orderTypeFlags.setPrescriptionStatus(orderInfoDO.getPrescriptionStatus());
    orderTypeFlags.setIsPushCheckFlag(orderInfoDO.getIsPushCheck());
    orderTypeFlags.setNewCustomerFlag(orderInfoDO.getNewCustomerFlag());
    orderTypeFlags.setIntegralFlag(orderInfoDO.getIntegralFlag());
    orderTypeFlags.setComplexModifyFlag(orderInfoDO.getComplexModifyFlag());
    orderTypeFlags.setMedicalInsuranceFlag(orderInfoDO.getMedicalInsurance());

    return orderTypeFlags;

  }

  private static ExOperator toExpOperator(OrderInfoDO orderInfoDO) {
    ExOperator exOperator = new ExOperator();
    exOperator.setExOperatorId(orderInfoDO.getExOperatorId());
    exOperator.setExOperatorName(orderInfoDO.getExOperatorName());
    exOperator.setExOperatorTime(orderInfoDO.getExOperatorTime());

    return exOperator;
  }

  private static Canceler toCanceler(OrderInfoDO orderInfoDO) {
    Canceler canceler = new Canceler();
    canceler.setCancellerId(orderInfoDO.getCancellerId());
    canceler.setCancellerName(orderInfoDO.getCancellerName());
    canceler.setCancelReason(orderInfoDO.getCancelReason());
    canceler.setCancelTime(orderInfoDO.getCancelTime());
    return canceler;

  }

  private static Picker toPicker(OrderInfoDO orderInfoDO) {
    Picker picker = new Picker();
    picker.setPickerId(orderInfoDO.getPickerId());
    picker.setPickerName(orderInfoDO.getPickerName());
    picker.setPickOperatorId(orderInfoDO.getPickOperatorId());
    picker.setPickOperatorName(orderInfoDO.getPickOperatorName());
    picker.setPickTime(orderInfoDO.getPickTime());
    return picker;

  }

  private static Acceptor toAcceptor(OrderInfoDO orderInfoDO) {
    Acceptor acceptor = new Acceptor();
    acceptor.setAcceptorId(orderInfoDO.getAcceptorId());
    acceptor.setAcceptorName(orderInfoDO.getAcceptorName());
    acceptor.setAcceptTime(orderInfoDO.getAcceptTime());
    return acceptor;
  }

  private static OrderSimpleDelivery toOrderSimpleDelivery(OrderInfoDO orderInfoDO) {
    OrderSimpleDelivery orderSimpleDelivery = new OrderSimpleDelivery();
    orderSimpleDelivery.setDeliveryTimeType(
        DeliveryTimeTypeEnum.getByCode(orderInfoDO.getDeliveryTimeType()));
    orderSimpleDelivery.setDeliveryTimeDesc(orderInfoDO.getDeliveryTimeDesc());
    orderSimpleDelivery.setBuyerName(orderInfoDO.getBuyerName());
    orderSimpleDelivery.setBuyerRemark(orderInfoDO.getBuyerRemark());
    orderSimpleDelivery.setBuyerMessage(orderInfoDO.getBuyerMessage());
    orderSimpleDelivery.setSellerRemark(orderInfoDO.getSellerRemark());
    orderSimpleDelivery.setRemindFlag(
        OrderRemindFlag.getByCode(orderInfoDO.getRemindFlag()));
    orderSimpleDelivery.setReceiverLat(orderInfoDO.getReceiverLat());
    orderSimpleDelivery.setReceiverLng(orderInfoDO.getReceiverLng());
    orderSimpleDelivery.setSelfVerifyCode(orderInfoDO.getSelfVerifyCode());

    return orderSimpleDelivery;


  }

  private static OrderLock toOrderLock(OrderInfoDO orderInfoDO) {
    OrderLock orderLock = new OrderLock();
    orderLock.setOrderLockFlag(OrderLockFlagEnum.getLockFlag(orderInfoDO.getLockFlag()));
    orderLock.setLockMsg(orderInfoDO.getLockMsg());
    orderLock.setLockerId(orderInfoDO.getLockerId());
    return orderLock;
  }

  private static AccountInfo toAccountInfo(OrderInfoDO orderInfoDO) {
    AccountInfo accountInfo = new AccountInfo();
    accountInfo.setOrderErpStatus(OrderErpStatusEnum.getOrderStatus(orderInfoDO.getErpState()));
    accountInfo.setErpSaleNo(orderInfoDO.getErpSaleNo());
    accountInfo.setBillTime(orderInfoDO.getBillTime());
    accountInfo.setBillOperator(orderInfoDO.getBillOperator());

    return accountInfo;
  }

  private static OrderOrganization toOrderOrganization(OrderInfoDO orderInfoDO) {

    OrderOrganization orderOrganization = new OrderOrganization();
    orderOrganization.setMerCode(orderInfoDO.getMerCode());
    orderOrganization.setThirdPlatformCode(orderInfoDO.getThirdPlatformCode());
    orderOrganization.setOnlineClientCode(orderInfoDO.getClientCode());
    orderOrganization.setOnlineClientConfigId(orderInfoDO.getClientConfId());
    orderOrganization.setDeliverOnlineStoreCode(orderInfoDO.getOnlineStoreCode());
    orderOrganization.setDeliverOnlineStoreName(orderInfoDO.getOnlineStoreName());
    orderOrganization.setDeliverStoreCode(orderInfoDO.getOrganizationCode());
    orderOrganization.setDeliverStoreName(orderInfoDO.getOrganizationName());
    orderOrganization.setSourceOnlineStoreCode(orderInfoDO.getSourceOnlineStoreCode());
    orderOrganization.setSourceOnlineStoreName(orderInfoDO.getSourceOnlineStoreName());
    orderOrganization.setSourceStoreCode(orderInfoDO.getSourceOnlineStoreCode());
    orderOrganization.setSourceStoreName(orderInfoDO.getSourceOnlineStoreName());

    return orderOrganization;
  }


}
