package com.yxt.order.atom.migration;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yxt.lang.util.JsonUtils;
import com.yxt.order.atom.common.LocalConst.DATA_SOURCE;
import com.yxt.order.atom.migration.dao.HanaMigrationErrorDO;
import com.yxt.order.atom.migration.dao.HanaMigrationErrorDOMapper;
import com.yxt.order.atom.migration.message.MigrationEventReHandleMessage;
import com.yxt.order.atom.migration.service.HanaMigrationService;
import com.yxt.order.atom.repair.dto.StartEndId;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR> Runkang (moatkon)
 * @date 2024年06月25日 10:51
 * @email: <EMAIL>
 * @see MigrationHanaErrorDataHandlerConsumer
 */

@Component
@Slf4j
public class MigrationHanaErrorDataHandler {

  @Resource
  private HanaMigrationService hanaMigrationService;

  @Resource
  private RocketMQTemplate template;


  @Value("${mq.topic.producer.migrationHanaDataReHandle}")
  private String migrationHanaDataReHandleProducer;


  public static final String MIGRATION_RE_HANDLE_TAG = "MIGRATION_RE_HANDLE_TAG";

  @Resource
  private HanaMigrationErrorDOMapper hanaMigrationErrorDOMapper;

  @Value("${migrationBatchSize:2000}")
  private Long batchSize;

  @XxlJob("migrationHanaErrorDataHandler")
  @DS(DATA_SOURCE.ORDER_OFFLINE)
  public void execute() {
    try {
      String param = XxlJobHelper.getJobParam();
      if (StringUtils.isEmpty(param)) {
        throw new RuntimeException("请配置参数");
      }
      StartEndId startEndId = JsonUtils.toObject(param, StartEndId.class);
      XxlJobHelper.log("param:{}", JsonUtils.toJson(startEndId));
//    StartEndId startEndId = hanaMigrationErrorDOMapper.selectMaxMinId(); // 采用配置,不查库了
      while (!startEndId.empty() && startEndId.getStartId() <= startEndId.getEndId()) {
        LambdaQueryWrapper<HanaMigrationErrorDO> query = new LambdaQueryWrapper<>();
        query.ge(HanaMigrationErrorDO::getId, startEndId.getStartId());
        query.lt(HanaMigrationErrorDO::getId, startEndId.getStartId() + batchSize);
        List<HanaMigrationErrorDO> hanaMigrationErrorDOList = hanaMigrationErrorDOMapper.selectList(
            query);
        if (CollectionUtils.isEmpty(hanaMigrationErrorDOList)) {
          refreshStartId(startEndId);
          continue;
        }

        for (HanaMigrationErrorDO hanaMigrationErrorDO : hanaMigrationErrorDOList) {
          try {
            // 通用处理逻辑
            commonHandleLogic(hanaMigrationErrorDO);
            // 成功入重试队列(先发在更新状态的,在消费者那里模式是处理失败的数据,处理成功后删除元数据,不成功则恢复了)
            hanaMigrationService.entryRetryQueue(hanaMigrationErrorDO);
          } catch (Exception e) {
            hanaMigrationErrorDO.setErrorMessage(
                "异常消息发送到重试队列时发生异常:" + e.getMessage());
            hanaMigrationService.refreshHanaMigrationErrorDO(hanaMigrationErrorDO);
          }
        }
        // 刷新起始Id
        refreshStartId(startEndId);
      }

      XxlJobHelper.handleSuccess();
    } catch (Exception e) {
      XxlJobHelper.handleFail("任务执行失败:" + e.getMessage());
    }
  }

  private void commonHandleLogic(HanaMigrationErrorDO hanaMigrationErrorDO) {
    // 重处理消息队列,提速
    MigrationEventReHandleMessage reHandleMessage = new MigrationEventReHandleMessage();
    reHandleMessage.setHanaMigrationErrorDO(hanaMigrationErrorDO);

    // 发送消息
    String dataJson = JsonUtils.toJson(reHandleMessage);
    SendResult sendResult = template.syncSend(
        migrationHanaDataReHandleProducer + ":" + MIGRATION_RE_HANDLE_TAG, dataJson, 6000);
    if (!SendStatus.SEND_OK.equals(sendResult.getSendStatus())) {
      throw new RuntimeException(String.format("消息发送失败,%s", dataJson));
    }
  }

  private void refreshStartId(StartEndId startEndId) {
    startEndId.setStartId(startEndId.getStartId() + batchSize);
  }

}
