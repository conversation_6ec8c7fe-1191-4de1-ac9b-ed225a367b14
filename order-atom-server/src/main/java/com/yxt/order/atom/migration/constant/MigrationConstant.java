package com.yxt.order.atom.migration.constant;

import com.google.common.collect.Sets;
import java.util.Set;

/**
 * @author: moatkon
 * @time: 2025/3/17 9:38
 */
public class MigrationConstant {

  public final static String DELETED_MIGRATION_REPEATED = "迁移重复数据,如后续需要可以从归档表查";
  public final static String DELETED_HD_TXSERIAL_REPEATED = "迁移重复数据,如后续需要可以从归档表查;cf:pageId:65480961";
  public final static String DELETED_PLATFORM_ERROR_REPEATED = "迁移重复数据: 平台编码映射错误导致的重复";
  public static final String DELETED_FROM_UNPROCESSABLE = "[FromUnprocessableOrderDO]迁移重复数据,如后续需要可以从归档表查";
  public static final String DELETED_HAIDIAN_ONLINE_ORDER_AS_OFFLINE_ORDER = "海典将线上正单当做线下正单传入";
  public static final String DELETED_HAIDIAN_OFFLINE_REFUND_AS_OFFLINE_REFUND_ORDER = "海典将线上退单当做线下退单传入";


  public static final String RELATION_TYPE_ORDER = "OFFLINE_ORDER";
  public static final String RELATION_TYPE_REFUND_ORDER = "OFFLINE_REFUND_ORDER";

  public static final Set<String> DELETED_SET = Sets.newHashSet(DELETED_MIGRATION_REPEATED,
      DELETED_FROM_UNPROCESSABLE, DELETED_HAIDIAN_ONLINE_ORDER_AS_OFFLINE_ORDER,
      DELETED_HAIDIAN_OFFLINE_REFUND_AS_OFFLINE_REFUND_ORDER,DELETED_HD_TXSERIAL_REPEATED,DELETED_PLATFORM_ERROR_REPEATED);

  public static final String DONT_NEED_OPERATE = "不用操作";


}
