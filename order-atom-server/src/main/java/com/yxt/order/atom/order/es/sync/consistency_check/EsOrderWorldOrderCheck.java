package com.yxt.order.atom.order.es.sync.consistency_check;

import cn.hutool.core.date.DateUtil;
import com.yxt.common.logic.flash.FlashParam;
import com.yxt.order.atom.order.es.doc.EsOrderWorldOrder;
import com.yxt.order.atom.order.es.mapper.EsOrderWorldOrderMapper;
import com.yxt.order.atom.order.es.sync.AbstractOrderConsistencyCheck;
import com.yxt.order.atom.order.es.sync.consistency_check.consistency_check_efficient_count.EsOrderWorldOrderEfficientCount;
import com.yxt.order.atom.order.es.sync.order_world.flash.OrderWorldOrderFlash;
import javax.annotation.Resource;
import org.dromara.easyes.core.conditions.select.LambdaEsQueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class EsOrderWorldOrderCheck extends AbstractOrderConsistencyCheck {

  @Autowired
  private EsOrderWorldOrderEfficientCount esOrderWorldOrderEfficientCount;

  @Resource
  private EsOrderWorldOrderMapper esOrderWorldOrderMapper;

  @Autowired
  private OrderWorldOrderFlash orderWorldOrderFlash;

  @Override
  protected Long dbDscloudOfflineCount() {
    return esOrderWorldOrderEfficientCount.fetchEfficientCount(getStartDate(), getEndDate());
  }

  @Override
  protected Long esCount() {
    LambdaEsQueryWrapper<EsOrderWorldOrder> queryWrapper = new LambdaEsQueryWrapper<>();
    queryWrapper.ge(EsOrderWorldOrder::getSysCreateTime, DateUtil.formatDateTime(getStartDate()));
    queryWrapper.le(EsOrderWorldOrder::getSysCreateTime, DateUtil.formatDateTime(getEndDate()));
    return esOrderWorldOrderMapper.selectCount(queryWrapper);
  }

  @Override
  protected ConsistencyNotify consistencyNotify() {
    return ConsistencyNotify.ORDER_WORLD_ORDER;
  }

  @Override
  protected void compensate() {
    FlashParam flashParam = getFlashParam();
    orderWorldOrderFlash.startFlush(flashParam);
  }

  @Override
  protected Boolean isHandleNonVipData() {
    return Boolean.TRUE;
  }
}
