package com.yxt.order.atom.order.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p>
 * 商家门店下账配置信息表
 * </p>
 *
 * <AUTHOR> 2020-08-03 下账调整
 * @since 2020-08-03
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("store_bill_config")
public class StoreBillConfigDO implements Serializable {

  private static final long serialVersionUID = -5669763934755421015L;

  @TableId(value = "id", type = IdType.AUTO)
  private Long id;

  /**
   * 配置名称
   */
  private String name;

  /**
   * 企业编码
   */
  private String merCode;

  /**
   * 平台编码
   */
  private String platformCode;

  /**
   * 网店编码
   */
  private String clientCode;

  /**
   * 门店编码
   */
  private String storeCode;

  /**
   * 门店名称
   */
  private String storeName;
  /**
   * 配送费, 收取方式: 1-平台收取, 2-商家收取
   */
  private Integer freightFeeFetch;

  /**
   * 配送费, 是否下账: 0-不下账, 1-下账
   */
  private Boolean freightFeeInventory;

  /**
   * 包装费, 收取方式: 1-平台收取, 2-商家收取
   */
  private Integer packageFeeFetch;

  /**
   * 包装费, 是否下账: 0-不下账, 1-下账
   */
  private Boolean packageFeeInventory;

  /**
   * 商家优惠金额, 是否下账: 0-不下账, 1-下账
   */
  private Boolean mcDiscountInventory;

  /**
   * 商家优惠金额, 是否分摊: 0-下账商家优惠金额, 1-分摊至商品明细
   */
  private Boolean mcDiscountShare;

  /**
   * 平台优惠金额, 是否下账: 0-不下账, 1-下账
   */
  private Boolean ptfDiscountInventory;

  /**
   * 平台优惠金额, 是否分摊: 0-下账平台优惠金额, 1-分摊至商品明细
   */
  private Boolean ptfDiscountShare;

  /**
   * 平台收取佣金, 是否下账: 0-不下账, 1-下账
   */
  private Boolean ptfCommissionInventory;

  /**
   * 平台收取佣金, 是否分摊: 0-下账平台佣金, 1-分摊至商品明细
   */
  private Boolean ptfCommissionShare;

  /**
   * 商品明细优惠金额, 是否下账: 0-不下账, 1-下账
   */
  private Boolean mcDtlDiscountInventory;

  /**
   * 商品明细优惠金额, 是否分摊: 0-下账商品明细优惠金额, 1-分摊至商品明细
   */
  private Boolean mcDtlDiscountShare;

  /**
   * 收银员取值: 1-同拣货员, 2-ERP中获取
   */
  private Integer cashierSource;

  /**
   * 创建人
   */
  private String creator;

  /**
   * 创建时间
   */
  private Date createTime;

  /**
   * 末次修改时间
   */
  private Date modifyTime;

  /**
   * 配置类型（1:初始化配置 2:历史配置 3:当前使用配置）
   */
  private Integer confType;

  /**
   * 平台名称
   */
  private String platformName;

  /**
   * 是否开启收银员录入 1：开启 0：关闭）
   */
  private Integer checkerType;

  /**
   * 是否开启班次录入 1：开启 0：关闭
   */
  private Integer frequencyType;

  /**
   * 下账erp设置 1:下账至erp 0:不下账至erp
   */
  private Integer billErpSetting;

  /**
   * 是否按批次下账
   */
  private Integer isBatchNoStock;

  /**
   * 配送费计算综合毛利，0-不计算，1-计算，默认值1-计算
   */
  private Integer freightFeeGrossProfitFlag;

  /**
   * 包装费计算综合毛利，0-不计算，1-计算，默认值1-计算
   */
  private Integer packageFeeGrossProfitFlag;

  /**
   * 商家优惠金额计算综合毛利，0-不计算，1-计算，默认值1-计算
   */
  private Integer mcDiscountGrossProfitFlag;

  /**
   * 平台优惠金额计算综合毛利，0-不计算，1-计算，默认值1-计算
   */
  private Integer ptfDiscountGrossProfitFlag;

  /**
   * 平台佣金计算综合毛利，0-不计算，1-计算，默认值1-计算
   */
  private Integer ptfCommissionGrossProfitFlag;

  /**
   * 商家明细优惠金额计算综合毛利，0-不计算，1-计算，默认值1-计算
   */
  private Integer mcDtlDiscountGrossProfitFlag;

  /**
   * 仅退款商品下账设置：0-商品零售额销售处理；1-商品报损处理
   */
  private Integer refundBusinessFlag;

  /**
   * 订单已下账是否允许仅退款 1：允许 0：不允许，默认1：允许
   */
  private Integer allowOnlyRefund;

  /**
   * 自动下账时机设置：0-配送出库后；1-拣货完成后，默认0-配送出库后
   */
  private Integer autoEnterAccountFlag;

  /**
   * 线上支付下账配置，默认0；0-按下账配置的支付方式下账，1-按顾客实际支付的支付方式下账
   */
  private Integer onlinePayType;
  /**
   * 订单下账收银员设置：1-订单审核操作员，2-面单打印操作员，3-发货单打印操作员，4-订单发货操作员
   */
  private Integer billCashier;

  /**
   * 下账机构，0-下账至仓库绑定的下账门店、1-下账至推广门店，默认值为：0-下账至仓库绑定的下账门店
   */
  private Integer billOrganizationFlag;


  public static StoreBillConfigDO allNotBill() {
    StoreBillConfigDO storeBillConfigDO = new StoreBillConfigDO();
    // 配送费, 是否下账: 0-不下账, 1-下账
    storeBillConfigDO.setFreightFeeInventory(false);
    // 包装费, 是否下账: 0-不下账, 1-下账
    storeBillConfigDO.setPackageFeeInventory(false);
    // 商家优惠金额, 是否下账: 0-不下账, 1-下账
    storeBillConfigDO.setMcDiscountInventory(false);
    // 平台优惠金额, 是否下账: 0-不下账, 1-下账
    storeBillConfigDO.setPtfDiscountInventory(false);
    // 平台收取佣金, 是否下账: 0-不下账, 1-下账
    storeBillConfigDO.setPtfCommissionInventory(false);
    // 商品明细优惠金额, 是否下账: 0-不下账, 1-下账
    storeBillConfigDO.setMcDtlDiscountInventory(false);
    return storeBillConfigDO;
  }
}
