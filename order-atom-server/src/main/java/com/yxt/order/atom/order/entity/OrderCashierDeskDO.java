package com.yxt.order.common.order_world_dto.db;

import java.time.LocalDateTime;
import lombok.Data;

/**
 * 线下单收银员
 */
@Data
public class OrderCashierDesk {

  /**
   * 主键
   */
  private Long id;

  /**
   * 内部订单号,自己生成
   */
  private String orderNo;

  /**
   * pos收银台编码
   */
  private String posCashierDeskNo;

  /**
   * 收银员编码
   */
  private String cashier;

  /**
   * 收银员姓名
   */
  private String cashierName;

  /**
   * 拣货员编码
   */
  private String picker;

  /**
   * 拣货员姓名
   */
  private String pickerName;

  /**
   * 班次
   */
  private String shiftId;

  /**
   * 班次日期
   */
  private LocalDateTime shiftDate;

  /**
   * 创建人
   */
  private String createdBy;

  /**
   * 更新人
   */
  private String updatedBy;

  /**
   * 创建时间
   */
  private LocalDateTime createdTime;

  /**
   * 更新时间
   */
  private LocalDateTime updatedTime;

  /**
   * 数据版本，每次update+1
   */
  private Long version;

  /**
   * 分销员编码
   */
  private String reseller;

  /**
   * 分销员姓名
   */
  private String resellerName;

  /**
   * 平台创建时间
   */
  private LocalDateTime created;

  /**
   * 平台更新时间
   */
  private LocalDateTime updated;

  /**
   * 系统创建时间
   */
  private LocalDateTime sysCreateTime;

  /**
   * 系统更新时间
   */
  private LocalDateTime sysUpdateTime;

}
