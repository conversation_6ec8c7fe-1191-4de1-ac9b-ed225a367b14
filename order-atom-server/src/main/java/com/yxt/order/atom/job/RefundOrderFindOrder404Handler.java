package com.yxt.order.atom.job;

import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.atom.job.abstractsStageOrder.RefundOrderDirectSave2Db;
import com.yxt.order.atom.job.abstractsStageOrder.RefundOrderFoundOrder;
import com.yxt.order.atom.order.mongo.StagingOrder;
import com.yxt.order.atom.order.repository.StageOrderService;
import com.yxt.order.atom.sdk.offline_order.OfflineOrderAtomApi;
import com.yxt.order.atom.sdk.offline_order.req.OfflineOrderExistsReqDto;
import com.yxt.order.common.CommonDateUtils;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * 处理退单找不到正单
 *
 * <AUTHOR> Runkang (moatkon)
 * @date 2024年08月16日 10:27
 * @email: <EMAIL>
 */
@Component
@Slf4j
public class RefundOrderFindOrder404Handler {

  @Resource
  private StageOrderService stageOrderService;
  @Resource
  private OfflineOrderAtomApi offlineOrderAtomApi;

  @Value("${stageOrderHandleHourLimit:3}")
  private Double stageOrderHandleHourLimit;

  @Resource
  private RefundOrderDirectSave2Db refundOrderDirectSave2Db;

  @Resource
  private RefundOrderFoundOrder refundOrderFoundOrder;

  /**
   * 如果是退单找不到正向单，将暂不往下游推，
   * <p>
   * 3小时后，订单落库后，则再自动往下游推。
   * <p>
   * 若中途有找到正向单，则是正向单落库推送后马上进行退单的推送。
   *
   * @return
   * @throws Exception
   */
  @XxlJob("refundOrderFindOrder404Handler")
  public void execute()  {
    XxlJobHelper.log("refundOrderFindOrder404Handler start-----");
    try {
      Long total = stageOrderService.countRefundOrderFind404Order();
      if (Objects.isNull(total) || total == 0) {
        XxlJobHelper.log("refundOrderFindOrder404Handler total:{}", total);
        XxlJobHelper.handleSuccess();
        return ;
      }

      Integer pageSize = 100;
      int totalPages = (int) Math.ceil((double) total / pageSize);
      for (int i = 0; i <= totalPages; i++) {
        List<StagingOrder> stagingRefundOrders = stageOrderService.queryRefundOrderFind404Order(i,
            pageSize);
        if (CollectionUtils.isEmpty(stagingRefundOrders)) {
          break;
        }
        stageRefundOrdersHandle(stagingRefundOrders);
      }


    } catch (Exception e) {
      XxlJobHelper.log(e);
      XxlJobHelper.handleFail("refundOrderFindOrder404Handler 执行失败-----");
      return;
    }

    XxlJobHelper.log("refundOrderFindOrder404Handler end-----");
    XxlJobHelper.handleSuccess();
  }

  private void stageRefundOrdersHandle(List<StagingOrder> stagingRefundOrders) {
    for (StagingOrder stagingRefundOrder : stagingRefundOrders) {
      //   * 若中途有找到正向单，则是正向单落库推送后马上进行退单的推送。
      //   * 3小时后，订单落库后，则再自动往下游推。
      String storeCode = stagingRefundOrder.getStoreCode();
      String thirdPlatformCode = stagingRefundOrder.getThirdPlatformCode();
      String thirdOrderNo = stagingRefundOrder.getThirdOrderNo();
      String defineNo = stagingRefundOrder.getDefineNo(); // 辅助分表单号

      // 如果有值就是退单对应的正单号,需要再次检查有无正单过来
      if (!StringUtils.isEmpty(thirdOrderNo)) {
        OfflineOrderExistsReqDto reqDto = new OfflineOrderExistsReqDto();
        reqDto.setStoreCode(storeCode);
        reqDto.setThirdOrderNo(thirdOrderNo);
        reqDto.setThirdPlatformCode(thirdPlatformCode);
        reqDto.setDefineNo(defineNo);
        ResponseBase<Boolean> res = offlineOrderAtomApi.offlineOrderExists(reqDto);
        if (res.getData()) { // 正单号现在有了,已经落库了,退单可以推了
          refundOrderFoundOrder.handle(stagingRefundOrder);
        } else { // 否则检查退单是否超时
          Date createTime = stagingRefundOrder.getCreateTime();
          if (CommonDateUtils.getHoursDifferenceWithDecimal(createTime)
              >= stageOrderHandleHourLimit) {
            // 直接落库,并删除Mongo消息
            refundOrderDirectSave2Db.handle(stagingRefundOrder);
          }
        }
      }
    }
  }


}
