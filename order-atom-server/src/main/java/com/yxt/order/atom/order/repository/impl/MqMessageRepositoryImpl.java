package com.yxt.order.atom.order.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yxt.order.atom.order.converter.MqMessageConverter;
import com.yxt.order.atom.order.entity.MqMessageDO;
import com.yxt.order.atom.order.mapper.MqMessageMapper;
import com.yxt.order.atom.order.repository.MqMessageRepository;
import com.yxt.order.atom.sdk.mqmessage.req.MqMessageDeleteReqDto;
import com.yxt.order.atom.sdk.mqmessage.req.MqMessageQueryReqDto;
import com.yxt.order.atom.sdk.mqmessage.res.MqMessageResDto;
import com.yxt.order.common.CommonDateUtils;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR> Runkang (moatkon)
 * @date 2024年04月15日 16:38
 * @email: <EMAIL>
 */
@Repository
public class MqMessageRepositoryImpl implements MqMessageRepository {

  @Resource
  private MqMessageMapper mqMessageMapper;


  @Override
  public void save(MqMessageDO mqMessageDO) {
    if (StringUtils.isEmpty(mqMessageDO.getId())) {
      mqMessageMapper.insert(mqMessageDO);
    } else {
      QueryWrapper<MqMessageDO> updateWrapper = new QueryWrapper<>();
      updateWrapper.lambda().eq(MqMessageDO::getMsgId, mqMessageDO.getMsgId());
      mqMessageMapper.update(mqMessageDO, updateWrapper);
    }
  }

  @Override
  public List<MqMessageResDto> list(MqMessageQueryReqDto mqMessageQueryReqDto) {
    LambdaQueryWrapper<MqMessageDO> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(!StringUtils.isEmpty(mqMessageQueryReqDto.getMsgId()), MqMessageDO::getMsgId,
        mqMessageQueryReqDto.getMsgId());
    queryWrapper.eq(!StringUtils.isEmpty(mqMessageQueryReqDto.getMqMsgStatus()),
        MqMessageDO::getMqMsgStatus, mqMessageQueryReqDto.getMqMsgStatus());
    queryWrapper.in(!CollectionUtils.isEmpty(mqMessageQueryReqDto.getMqMsgTypes()),
        MqMessageDO::getMsgType, mqMessageQueryReqDto.getMqMsgTypes());
    queryWrapper.orderByAsc(MqMessageDO::getCreatedTime);
    queryWrapper.last(Objects.nonNull(mqMessageQueryReqDto.getLimit()),
        " limit " + mqMessageQueryReqDto.getLimit());
    List<MqMessageDO> mqMessageDOS = mqMessageMapper.selectList(queryWrapper);
    return MqMessageConverter.INSTANCE.toDo(mqMessageDOS);
  }

  @Override
  public Boolean delete(MqMessageDeleteReqDto deleteReqDto) {
    boolean result = Boolean.FALSE;
    LambdaQueryWrapper<MqMessageDO> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(!StringUtils.isEmpty(deleteReqDto.getMqMsgStatus()),
        MqMessageDO::getMqMsgStatus, deleteReqDto.getMqMsgStatus());
    queryWrapper.lt(Objects.nonNull(deleteReqDto.getDays()), MqMessageDO::getCreatedTime,
        CommonDateUtils.addDay(new Date(), deleteReqDto.getDays()));
    queryWrapper.last(" limit 100 ");
    for (; ; ) {
      int delete = mqMessageMapper.delete(queryWrapper);
      if (delete <= 0) {
        break;
      }
      result = Boolean.TRUE;
    }

    return result;
  }

}
