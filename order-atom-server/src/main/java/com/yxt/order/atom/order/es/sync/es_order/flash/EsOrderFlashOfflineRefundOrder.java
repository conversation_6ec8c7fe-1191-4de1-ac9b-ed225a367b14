package com.yxt.order.atom.order.es.sync.es_order.flash;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yxt.order.atom.common.LocalConst.DATA_SOURCE;
import com.yxt.order.atom.order.entity.OfflineRefundOrderDO;
import com.yxt.order.atom.order.es.sync.AbstractFlash;
import com.yxt.order.atom.order.es.sync.DoToCanalDtoWrapper;
import com.yxt.order.atom.order.es.sync.FlashQueryWrapper;
import com.yxt.order.atom.order.es.sync.SupportChronicDisease;
import com.yxt.order.atom.order.es.sync.data.CanalOfflineRefundOrder;
import com.yxt.order.atom.order.es.sync.data.CanalOfflineRefundOrder.OfflineRefundOrder;
import com.yxt.order.atom.order.es.sync.es_order.handler.OfflineRefundOrderCanalHandler;
import com.yxt.order.atom.order.mapper.OfflineRefundOrderMapper;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * @author: moatkon
 * @time: 2024/12/20 17:31
 */
@Component
@DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
public class EsOrderFlashOfflineRefundOrder extends
    AbstractFlash<OfflineRefundOrderDO, OfflineRefundOrder, SupportChronicDisease> {

  @Resource
  private OfflineRefundOrderCanalHandler offlineRefundOrderCanalHandler;


  @Resource
  private OfflineRefundOrderMapper offlineRefundOrderMapper;

  @Override
  protected Long queryCursorStartId() {
    return offlineRefundOrderMapper.selectMinId(getFlashParam());
  }

  @Override
  protected Long queryCursorEndId() {
    return offlineRefundOrderMapper.selectMaxId(getFlashParam());
  }

  @Override
  protected List<OfflineRefundOrderDO> getSourceList() {
    LambdaQueryWrapper<OfflineRefundOrderDO> query = FlashQueryWrapper.offlineRefundOrderFlashQuery(getFlashParam(),defaultLimit());
    return offlineRefundOrderMapper.selectList(query);
  }

  @Override
  protected List<OfflineRefundOrder> assembleTargetData(
      List<OfflineRefundOrderDO> refundOrderDOList) {
    return refundOrderDOList.stream().map(DoToCanalDtoWrapper::getOfflineRefundOrder).collect(Collectors.toList());
  }


  @Override
  protected void flash(List<OfflineRefundOrder> offlineRefundOrders) {
    CanalOfflineRefundOrder canalOfflineRefundOrder = new CanalOfflineRefundOrder();
    canalOfflineRefundOrder.setData(offlineRefundOrders);
    offlineRefundOrderCanalHandler.manualFlash(canalOfflineRefundOrder);
  }

  @Override
  protected Boolean isSharding() {
    return Boolean.TRUE;
  }
}
