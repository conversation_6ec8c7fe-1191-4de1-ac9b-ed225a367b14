package com.yxt.order.atom.order.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.Objects;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * <p>
 * 渠道数据
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ToString
@TableName("account_check_pull_job")
public class AccountCheckPullJobDO implements Serializable {

    private static final long serialVersionUID=1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 商户编码
     */
    private String merCode;

    /**
     * 平台编码
     */
    private String platformCode;

    /**
     * 门店编码
     */
    private String clientCode;

    /**
     * 拉取开始时间
     */
    private LocalDateTime pullStartTime;

    /**
     * 拉取结束时间
     */
    private LocalDateTime pullEndTime;

    /**
     * 上下文ID
     */
    private String contextId;

    /**
     * 执行状态 1 待执行 2 执行中 3 执行成功 4 执行失败
     */
    @TableField(value = "`status`")
    private Integer status;

    /**
     * 备注信息
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 末次修改时间
     */
    private Date modifyTime;

}
