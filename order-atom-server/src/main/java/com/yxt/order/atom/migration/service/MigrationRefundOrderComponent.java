package com.yxt.order.atom.migration.service;

import static com.yxt.order.atom.sdk.AtomSdkErrorCode.REFUND_ORDER_NOT_HAVA_NORMAL_ORDER;
import static com.yxt.order.types.offline.enums.AfterSaleTypeEnum.AFTER_SALE_AMOUNT_GOODS;

import com.yxt.lang.exception.YxtBizException;
import com.yxt.order.atom.migration.dao.HanaMigrationMapper;
import com.yxt.order.atom.migration.dao.HanaOrderInfo;
import com.yxt.order.atom.migration.dao.HanaOrderItem;
import com.yxt.order.atom.migration.dao.HanaOrderPay;
import com.yxt.order.atom.migration.dao.HanaStore;
import com.yxt.order.atom.migration.dao.HanaUser;
import com.yxt.order.atom.migration.service.dto.GoodsInfo;
import com.yxt.order.atom.order.repository.OfflineOrderRepository;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineRefundOrderCashierDeskDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineRefundOrderDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineRefundOrderDetailDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineRefundOrderOrganizationDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineRefundOrderPayDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineRefundOrderUserDTO;
import com.yxt.order.atom.sdk.offline_order.req.OfflineOrderInfoReqDto;
import com.yxt.order.atom.sdk.offline_order.req.SaveOfflineRefundOrderReqDto;
import com.yxt.order.atom.sdk.offline_order.res.OfflineOrderInfoResDto;
import com.yxt.order.common.exception.DetailNotExistsException;
import com.yxt.order.types.offline.OfflineDetailGiftType;
import com.yxt.order.types.offline.OfflineThirdPlatformCode;
import com.yxt.order.types.offline.OfflineUserId;
import com.yxt.order.types.offline.enums.GiftTypeEnum;
import com.yxt.order.types.offline.enums.RefundStateEnum;
import com.yxt.order.types.offline.enums.RefundTypeEnum;
import com.yxt.order.types.offline.enums.ThirdPlatformCodeEnum;
import com.yxt.order.types.offline.refund.AfterSaleType;
import com.yxt.order.types.offline.refund.OfflineRefundNo;
import com.yxt.order.types.offline.refund.RefundState;
import com.yxt.order.types.offline.refund.RefundType;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR> Runkang (moatkon)
 * @date 2024年06月12日 9:44
 * @email: <EMAIL>
 */
@Component
@Slf4j
public class MigrationRefundOrderComponent {

  @Resource
  private MigrationPlatformCodeHandler migrationPlatformCodeHandler;

  @Resource
  private OfflineOrderRepository offlineOrderRepository;

  @Resource
  private HanaMigrationMapper hanaMigrationMapper;

  @Resource
  private MigrationCommonComponent migrationCommonComponent;

  public SaveOfflineRefundOrderReqDto buildRefundOrder(
      HanaOrderInfo hanaOrderInfo, String schema) {
    // 获取用户
    HanaUser hanaUser = migrationCommonComponent.buildHanaUserInfo(hanaOrderInfo.getClientCode());
    String userId = Strings.EMPTY;
    if (Objects.nonNull(hanaUser) && Objects.nonNull(hanaUser.getMemberUserId())) {
      userId = String.valueOf(hanaUser.getMemberUserId());
    }

    // 获取退款明细
    List<HanaOrderItem> hanaOrderItems = migrationCommonComponent.getHanaOrderItems(
        hanaOrderInfo, schema);
    if (CollectionUtils.isEmpty(hanaOrderItems)) {
      throw new DetailNotExistsException(
          String.format("退单明细迁移库中不存在,店铺编码:%s, 三方退单号: %s", hanaOrderInfo.getStoreCode(),
              hanaOrderInfo.getThirdRefundNo()));
    }

    SaveOfflineRefundOrderReqDto dto = new SaveOfflineRefundOrderReqDto();
    dto.setOfflineRefundOrderDTO(buildOrderRefund(hanaOrderInfo, hanaOrderItems, schema, userId));

    // 这里入参传入的是dto,注意顺序
    dto.setOfflineRefundOrderDetailDTOList(buildOrderRefundDetail(dto, hanaOrderItems));
    dto.setOfflineRefundOrderPayDTOList(buildOrderRefundPay(dto, schema,hanaOrderInfo));
    if (Objects.nonNull(hanaUser)) {
      dto.setOfflineRefundOrderUserDTO(buildRefundUserDto(dto, hanaUser, userId));
    }

    dto.setOfflineRefundOrderOrganizationDTO(buildRefundOrderOrganization(dto));
    dto.setOfflineRefundOrderCashierDeskDTO(buildRefundOrderCashierDesk(dto, hanaOrderInfo));

    // 退款信息,金额、数量等字段取绝对值
    dto.absAmount();
    return dto;
  }

  private OfflineRefundOrderCashierDeskDTO buildRefundOrderCashierDesk(
      SaveOfflineRefundOrderReqDto dto, HanaOrderInfo hanaOrderInfo) {
    OfflineRefundOrderDTO offlineRefundOrderDTO = dto.getOfflineRefundOrderDTO();

    OfflineRefundOrderCashierDeskDTO orderCashierDeskDTO = new OfflineRefundOrderCashierDeskDTO();
    orderCashierDeskDTO.setRefundNo(offlineRefundOrderDTO.getRefundNo());
    orderCashierDeskDTO.setPosCashierDeskNo(hanaOrderInfo.getPosCashierDeskNo());
    orderCashierDeskDTO.setCashier(hanaOrderInfo.getCashier());
    String cashierName = migrationCommonComponent.getHrmResourceName(hanaOrderInfo.getCashier());
    orderCashierDeskDTO.setCashierName(cashierName);
    orderCashierDeskDTO.setPicker(hanaOrderInfo.getPicker());
    String pickerName = migrationCommonComponent.getHrmResourceName(hanaOrderInfo.getPicker());
    orderCashierDeskDTO.setPickerName(pickerName);
    orderCashierDeskDTO.setShiftId(null);
    orderCashierDeskDTO.setShiftDate(null);
    orderCashierDeskDTO.setCreatedBy("");
    orderCashierDeskDTO.setUpdatedBy("");
    orderCashierDeskDTO.setCreatedTime(new Date());
    orderCashierDeskDTO.setUpdatedTime(new Date());
    orderCashierDeskDTO.setVersion(1L);
    return orderCashierDeskDTO;
  }

  private OfflineRefundOrderOrganizationDTO buildRefundOrderOrganization(
      SaveOfflineRefundOrderReqDto dto) {
    OfflineRefundOrderDTO refundOrderDTO = dto.getOfflineRefundOrderDTO();

    String storeCode = refundOrderDTO.getStoreCode();
    HanaStore hanaStore = migrationCommonComponent.queryStoreInfo(storeCode);
    OfflineRefundOrderOrganizationDTO offlineOrderOrganizationDTO = new OfflineRefundOrderOrganizationDTO();
    offlineOrderOrganizationDTO.setRefundNo(refundOrderDTO.getRefundNo());
    offlineOrderOrganizationDTO.setStoreCode(storeCode);
    offlineOrderOrganizationDTO.setStoreName(hanaStore.getStoreName());
    offlineOrderOrganizationDTO.setCompanyCode(hanaStore.getCompanyCode());
    offlineOrderOrganizationDTO.setCompanyName(hanaStore.getCompanyName());
    offlineOrderOrganizationDTO.setStoreDirectJoinType(hanaStore.getStoreDirectJoinType());
    offlineOrderOrganizationDTO.setCreatedBy("");
    offlineOrderOrganizationDTO.setUpdatedBy("");
    offlineOrderOrganizationDTO.setCreatedTime(new Date());
    offlineOrderOrganizationDTO.setUpdatedTime(new Date());
    offlineOrderOrganizationDTO.setVersion(1L);
    return offlineOrderOrganizationDTO;
  }

  private OfflineRefundOrderUserDTO buildRefundUserDto(SaveOfflineRefundOrderReqDto reqDto,
      HanaUser hanaUser, String userId) {
    OfflineRefundOrderDTO offlineOrderDTO = reqDto.getOfflineRefundOrderDTO();

    OfflineRefundOrderUserDTO offlineRefundOrderUserDTO = new OfflineRefundOrderUserDTO();
    offlineRefundOrderUserDTO.setRefundNo(offlineOrderDTO.getRefundNo());
    offlineRefundOrderUserDTO.setUserId(userId);
    offlineRefundOrderUserDTO.setUserName(hanaUser.getUserName());
    offlineRefundOrderUserDTO.setUserCardNo(hanaUser.getUserCardNo());
    offlineRefundOrderUserDTO.setUserMobile(hanaUser.getUserMobile());
    offlineRefundOrderUserDTO.setCreatedBy("");
    offlineRefundOrderUserDTO.setUpdatedBy("");
    offlineRefundOrderUserDTO.setCreatedTime(new Date());
    offlineRefundOrderUserDTO.setUpdatedTime(new Date());
    offlineRefundOrderUserDTO.setVersion(1L);
    return offlineRefundOrderUserDTO;
  }


  private List<OfflineRefundOrderPayDTO> buildOrderRefundPay(SaveOfflineRefundOrderReqDto dto,
      String schema,HanaOrderInfo hanaOrderInfo) {
    OfflineRefundOrderDTO offlineRefundOrderDTO = dto.getOfflineRefundOrderDTO();
    List<HanaOrderPay> hanaOrderPayList = migrationCommonComponent.getHanaOrderPays(
        offlineRefundOrderDTO.getThirdRefundNo(),
        offlineRefundOrderDTO.getStoreCode(), schema,hanaOrderInfo.orderIdList());
    String refundNo = offlineRefundOrderDTO.getRefundNo();

    return hanaOrderPayList.stream().map(hanaOrderPay -> {

      OfflineRefundOrderPayDTO payDTO = new OfflineRefundOrderPayDTO();
      payDTO.setRefundNo(refundNo);
      payDTO.setRefundPayType(hanaOrderPay.getPayType());
      payDTO.setRefundPayName(hanaOrderPay.getPayName());
      payDTO.setRefundPayAmount(hanaOrderPay.getPayAmount());
      payDTO.setCreatedBy("");
      payDTO.setUpdatedBy("");
      payDTO.setCreatedTime(new Date());
      payDTO.setUpdatedTime(new Date());
      payDTO.setVersion(1L);
      return payDTO;
    }).collect(Collectors.toList());
  }

  private List<OfflineRefundOrderDetailDTO> buildOrderRefundDetail(
      SaveOfflineRefundOrderReqDto dto, List<HanaOrderItem> hanaOrderItems) {

    OfflineRefundOrderDTO offlineRefundOrderDTO = dto.getOfflineRefundOrderDTO();
    String orderNo = offlineRefundOrderDTO.getOrderNo();
    String refundNo = offlineRefundOrderDTO.getRefundNo();

    Set<String> erpCodeSet = hanaOrderItems.stream()
        .map(HanaOrderItem::getErpCode)
        .collect(Collectors.toSet());
    Map<String, GoodsInfo> goodsInfoMap = migrationCommonComponent.getGoodsInfo(erpCodeSet);

    return hanaOrderItems.stream().map(hanaOrderItem -> {
      String refundDetailNo = migrationCommonComponent.generateId();

      OfflineRefundOrderDetailDTO detailDTO = new OfflineRefundOrderDetailDTO();
      detailDTO.setOrderNo(orderNo);
      detailDTO.setRefundNo(refundNo);
      detailDTO.setRefundDetailNo(refundDetailNo);
      detailDTO.setRowNo(hanaOrderItem.getRowNo());
      detailDTO.setPlatformSkuId(null);
      detailDTO.setErpCode(hanaOrderItem.getErpCode());
      String erpName = migrationCommonComponent.getErpNameByErpCode(hanaOrderItem.getErpCode());
      detailDTO.setErpName(erpName);
      detailDTO.setRefundCount(hanaOrderItem.getCommodityCount());
      detailDTO.setRefundStatus(RefundState.refundState(RefundStateEnum.REFUNDED).toString());
      detailDTO.setGiftType(OfflineDetailGiftType.giftType(GiftTypeEnum.NOT_GIFT).toString());
      detailDTO.setOriginalPrice(hanaOrderItem.getOriginalPrice());
      BigDecimal price = migrationCommonComponent.calcPrice(hanaOrderItem);
      detailDTO.setPrice(price);
      detailDTO.setCommodityCostPrice(BigDecimal.ZERO);
      detailDTO.setTotalAmount(hanaOrderItem.getTotalAmount());
      // 总分摊
      BigDecimal totalDiscountShare = migrationCommonComponent.calcTotalDiscount(hanaOrderItem);
      detailDTO.setDiscountShare(totalDiscountShare);
      detailDTO.setDiscountAmount(totalDiscountShare);// 公式和总分摊一致
      detailDTO.setBillPrice(price);// 公式和商品售价（实际）一致
      detailDTO.setBillAmount(hanaOrderItem.getTotalAmount());
      detailDTO.setCreatedBy("");
      detailDTO.setUpdatedBy("");
      detailDTO.setCreatedTime(new Date());
      detailDTO.setUpdatedTime(new Date());
      detailDTO.setVersion(1L);

      GoodsInfo goodsInfo = goodsInfoMap.get(detailDTO.getErpCode());
      if (Objects.nonNull(goodsInfo)) {
        detailDTO.setFiveClass(goodsInfo.getFiveClass());
        detailDTO.setFiveClassName(goodsInfo.getFiveClassName());
        detailDTO.setManufacture(goodsInfo.getManufacture());
        detailDTO.setCommoditySpec(goodsInfo.getCommoditySpecValue());
        detailDTO.setMainPic(goodsInfo.getMainPic());
      }
      detailDTO.setSalerId(hanaOrderItem.getSalerId());
      detailDTO.setSalerName(migrationCommonComponent.getHrmResourceName(hanaOrderItem.getSalerId()));
      return detailDTO;
    }).collect(Collectors.toList());
  }


  private OfflineRefundOrderDTO buildOrderRefund(HanaOrderInfo hanaOrderInfo,
      List<HanaOrderItem> hanaOrderItems, String schema, String userId) {

//    ThirdPlatformCodeEnum platformCode = migrationPlatformCodeHandler.getPlatformCode(schema,
//        hanaOrderInfo.getCreateTime(), hanaOrderInfo.getStoreCode());
    ThirdPlatformCodeEnum platformCode = migrationPlatformCodeHandler.getPlatformCodeOtherOrderId(hanaOrderInfo);

    OfflineThirdPlatformCode thirdPlatformCode = OfflineThirdPlatformCode.thirdPlatformCode(
        platformCode);

    // 生成单号使用
    OfflineUserId offlineUserId = StringUtils.isEmpty(userId) ? null : OfflineUserId.userId(userId);

    Date txDateAndTime = migrationCommonComponent.getTxDateAndTime(hanaOrderInfo);

    String refundNo = migrationCommonComponent.generateId();
    OfflineRefundNo offlineRefundNo = OfflineRefundNo.create(refundNo, offlineUserId, txDateAndTime);

    String shardingNo = offlineRefundNo.toString();
    String orderNo = queryOrderNo(hanaOrderInfo, thirdPlatformCode, shardingNo);

    BigDecimal amount = hanaOrderItems.stream().map(HanaOrderItem::getTotalAmount)
        .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

    OfflineRefundOrderDTO offlineRefundOrderDTO = new OfflineRefundOrderDTO();
    offlineRefundOrderDTO.setOrderNo(orderNo);
    offlineRefundOrderDTO.setStoreCode(hanaOrderInfo.getStoreCode());
    offlineRefundOrderDTO.setUserId(userId);
    offlineRefundOrderDTO.setRefundNo(offlineRefundNo.toString());
    offlineRefundOrderDTO.setThirdPlatformCode(thirdPlatformCode.toString());
    offlineRefundOrderDTO.setThirdRefundNo(hanaOrderInfo.getThirdOrderNo()); //  hanaOrderInfo.getThirdOrderNo() == XF_DOCNO
    offlineRefundOrderDTO.setThirdOrderNo(getThirdOrderNo(hanaOrderInfo));// ok

    //
    if (!StringUtils.isEmpty(hanaOrderInfo.getThirdRefundNo()) && hanaOrderInfo.getThirdRefundNo()
        .startsWith("V")) {
      offlineRefundOrderDTO.setRefundType(RefundType.refundType(RefundTypeEnum.ALL).toString());
    } else {
      if (!StringUtils.isEmpty(hanaOrderInfo.getThirdRefundNo())) { // 不为空,则是正单号
        String thirdOrderNo = hanaOrderInfo.getThirdRefundNo();
        List<HanaOrderInfo> queriedOrderInfoList = hanaMigrationMapper.getOrderInfoForRefund(thirdOrderNo,
            hanaOrderInfo.getStoreCode(), schema);
        HanaOrderInfo orderInfo = null;
        if(!CollectionUtils.isEmpty(queriedOrderInfoList)){
          for (HanaOrderInfo queriedOrder : queriedOrderInfoList) {
            if(queriedOrder.getActualPayAmount().compareTo(hanaOrderInfo.getActualPayAmount().abs()) == 0){
              orderInfo = queriedOrder;
            }
          }
          if(Objects.isNull(orderInfo)){
            throw new RuntimeException("通过退单查询正单,再根据金额辅助判断,也为获取到有效正单");
          }
        }

        if (Objects.nonNull(orderInfo)) {
          // actualPayAmount actualCollectAmount 取的都是同一个字段
          BigDecimal actualCollectAmount = orderInfo.getActualCollectAmount();
          if (actualCollectAmount.compareTo(amount.abs()) >= 0) { // 和绝对值比
            offlineRefundOrderDTO.setRefundType(
                RefundType.refundType(RefundTypeEnum.ALL).toString());
          } else {
            offlineRefundOrderDTO.setRefundType(
                RefundType.refundType(RefundTypeEnum.PART).toString());
          }
        }
      } else {
        offlineRefundOrderDTO.setRefundType(RefundTypeEnum.UNKOWN.toString()); // 其他的,暂时判断不出来
      }
    }
    offlineRefundOrderDTO.setAfterSaleType(
        AfterSaleType.afterSaleType(AFTER_SALE_AMOUNT_GOODS).toString());
    offlineRefundOrderDTO.setRefundState(
        RefundState.refundState(RefundStateEnum.REFUNDED).toString());
    offlineRefundOrderDTO.setReason("");
    offlineRefundOrderDTO.setCreated(txDateAndTime);
    offlineRefundOrderDTO.setApplyTime(txDateAndTime);
    offlineRefundOrderDTO.setCompleteTime(txDateAndTime);
    offlineRefundOrderDTO.setBillTime(txDateAndTime);
    offlineRefundOrderDTO.setTotalAmount(amount);
    offlineRefundOrderDTO.setShopRefund(amount);
    offlineRefundOrderDTO.setConsumerRefund(amount);
    offlineRefundOrderDTO.setCreatedBy("");
    offlineRefundOrderDTO.setUpdatedBy("");
    offlineRefundOrderDTO.setCreatedTime(txDateAndTime);// 改为交易时间
    offlineRefundOrderDTO.setUpdatedTime(new Date());
    offlineRefundOrderDTO.setVersion(1L);
    offlineRefundOrderDTO.setSerialNo(null);
    offlineRefundOrderDTO.setMigration(String.valueOf(Boolean.TRUE));
    return offlineRefundOrderDTO;
  }

  public static String getThirdOrderNo(HanaOrderInfo hanaOrderInfo) {
    // XF_VOIDDOCNO 如果有值，对应的就是正单的XF_DOCNO
    String thirdRefundNo = hanaOrderInfo.getThirdRefundNo();
    if (!StringUtils.isEmpty(thirdRefundNo)) {
      return thirdRefundNo;
    }
    return Strings.EMPTY;
  }

  private String queryOrderNo(HanaOrderInfo hanaOrderInfo,
      OfflineThirdPlatformCode thirdPlatformCode, String shardingNo) {
    String storeCode = hanaOrderInfo.getStoreCode();
    // XF_VOIDDOCNO 如果有值，对应的就是正单的XF_DOCNO
    String thirdRefundNo = hanaOrderInfo.getThirdRefundNo();
    if (StringUtils.isEmpty(thirdRefundNo)) {
      return Strings.EMPTY;
    }
    String docNo = thirdRefundNo; // 有值就是正单的docNo

    OfflineOrderInfoReqDto req = new OfflineOrderInfoReqDto();
    req.setThirdOrderNo(docNo);
    req.setStoreCode(storeCode);
    req.setThirdPlatformCode(thirdPlatformCode.toString());
    req.setNumberHelper(shardingNo); //

    String orderNo = Strings.EMPTY;
    try {
      OfflineOrderInfoResDto offlineOrderInfoResDto = offlineOrderRepository.get(req);
      orderNo = offlineOrderInfoResDto.getOrderNo();
    } catch (Exception ignore) {
      if (ignore instanceof YxtBizException && ((YxtBizException) ignore).getCode()
          .equals(REFUND_ORDER_NOT_HAVA_NORMAL_ORDER.getSdkErrorCode().toString())) {
        log.info("退单号 {},找不到正单号", thirdRefundNo);
      }
      return orderNo;
    }

    return orderNo;
  }
}
