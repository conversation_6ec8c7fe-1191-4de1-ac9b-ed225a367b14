package com.yxt.order.atom.order.repository.abstracts.refund.insert;

import cn.hutool.core.collection.CollUtil;
import com.yxt.order.atom.order.entity.RefundOrderDO;
import com.yxt.order.atom.order.repository.abstracts.refund.AbstractRefundInsert;
import com.yxt.order.atom.order.repository.batch.RefundBatchRepository;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class RefundInfoInsert extends AbstractRefundInsert<List<RefundOrderDO>> {

  @Autowired
  private RefundBatchRepository refundBatchRepository;

  @Override
  protected Boolean canInsert() {
    return CollUtil.isNotEmpty(data());
  }

  @Override
  protected Integer insert(List<RefundOrderDO> refundOrderList) {
    return refundBatchRepository.saveBatch(refundOrderList) ? refundOrderList.size() : 0;
  }

  /**
   * 转换成目标对象T
   *
   * @return
   */
  @Override
  protected List<RefundOrderDO> data() {
    return this.saveData.getRefundOrderList();
  }
}
