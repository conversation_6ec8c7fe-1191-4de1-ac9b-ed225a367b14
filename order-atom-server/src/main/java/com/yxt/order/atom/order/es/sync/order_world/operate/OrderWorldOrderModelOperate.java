package com.yxt.order.atom.order.es.sync.order_world.operate;

import com.yxt.common.logic.es.AbstractEsOperate;
import com.yxt.order.atom.order.es.doc.EsOrderWorldOrder;
import com.yxt.order.atom.order.es.mapper.EsOrderWorldOrderMapper;
import com.yxt.order.atom.order.es.sync.order_world.model.EsOrderWorldOrderModel;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.dromara.easyes.core.conditions.select.LambdaEsQueryWrapper;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class OrderWorldOrderModelOperate extends AbstractEsOperate<EsOrderWorldOrderModel> {

  @Resource
  private EsOrderWorldOrderMapper esOrderWorldOrderMapper;

  public OrderWorldOrderModelOperate() {
    super(EsOrderWorldOrderModel.class);
  }

  @Override
  protected Boolean exec() {
    if (Type.DELETE.equals(getType())) {
      return delete(getT());
    } else if (Type.SAVE.equals(getType())) {
      return save(getT());
    }

    return false;
  }

  private Boolean save(EsOrderWorldOrderModel model) {

    EsOrderWorldOrder esOrder = model.create();

    LambdaEsQueryWrapper<EsOrderWorldOrder> wrapper = new LambdaEsQueryWrapper<>();
    wrapper.eq(EsOrderWorldOrder::getId, model.defineId());
    //先添加路由查找,如果能查找到，直接更新
    wrapper.routing(model.routeKey());
    Long count = esOrderWorldOrderMapper.selectCount(wrapper);
    if (count > 0) {
      esOrderWorldOrderMapper.updateById(model.routeKey(), esOrder);
      return true;
    }
    //重新插入
    esOrderWorldOrderMapper.insert(model.routeKey(), esOrder);
    return true;
  }

  private Boolean delete(EsOrderWorldOrderModel model) {
    return esOrderWorldOrderMapper.deleteById(model.routeKey(), model.defineId()) > 0;
  }
}
