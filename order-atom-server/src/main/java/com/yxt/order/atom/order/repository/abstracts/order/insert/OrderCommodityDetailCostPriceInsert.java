package com.yxt.order.atom.order.repository.abstracts.order.insert;

import com.yxt.order.atom.order.entity.OrderCommodityDetailCostPriceDO;
import com.yxt.order.atom.order.repository.abstracts.order.AbstractInsert;
import com.yxt.order.atom.order.repository.batch.OrderCommodityDetailCostPriceBatchRepository;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * @author: moatkon
 * @time: 2024/10/11 13:47
 */
@Component
public class OrderCommodityDetailCostPriceInsert extends AbstractInsert<List<OrderCommodityDetailCostPriceDO>> {

  @Resource
  private OrderCommodityDetailCostPriceBatchRepository orderCommodityDetailCostPriceBatchRepository;

  @Override
  protected Boolean canInsert() {
    return !CollectionUtils.isEmpty(saveDataOptional.getOrderCommodityDetailCostPriceList());
  }

  @Override
  protected Integer insert(List<OrderCommodityDetailCostPriceDO> list) {
    return orderCommodityDetailCostPriceBatchRepository.saveBatch(list) ? list.size() : 0;
  }

  @Override
  protected List<OrderCommodityDetailCostPriceDO> data() {
    return saveDataOptional.getOrderCommodityDetailCostPriceList();
  }
}
