package com.yxt.order.atom.order.repository.abstracts.order.update;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yxt.order.atom.order.entity.ErpBillInfoDO;
import com.yxt.order.atom.order.entity.OrderInfoDO;
import com.yxt.order.atom.order.mapper.ErpBillInfoMapper;
import com.yxt.order.atom.order.repository.abstracts.order.AbstractUpdate;
import javax.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class ErpBillInfoUpdate extends AbstractUpdate<ErpBillInfoDO> {

  @Resource
  private ErpBillInfoMapper erpBillInfoMapper;

  @Override
  protected Boolean canUpdate() {
    return ObjectUtil.isNotNull(req.getBillInfoDTO()) && (ObjectUtil.isNotNull(req.getBillInfoDTO().getId()) || ObjectUtil.isNotNull(req.getBillInfoDTO().getOrderNo()));
  }

  @Override
  protected Integer update(ErpBillInfoDO erpBillInfoDO) {

    if(ObjectUtil.isNotNull(erpBillInfoDO.getId())){
      return erpBillInfoMapper.updateById(erpBillInfoDO);
    }
    LambdaQueryWrapper<ErpBillInfoDO> wrapper = Wrappers.<ErpBillInfoDO>lambdaQuery()
        .eq(ErpBillInfoDO::getOrderNo, erpBillInfoDO.getOrderNo());
    return erpBillInfoMapper.update(erpBillInfoDO, wrapper);
  }

  /**
   * 转换成目标对象T
   *
   * @return
   */
  @Override
  protected ErpBillInfoDO convert() {
    return BeanUtil.copyProperties(req.getBillInfoDTO(), ErpBillInfoDO.class);
  }
}
