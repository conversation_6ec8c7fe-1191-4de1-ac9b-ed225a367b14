package com.yxt.order.atom.order_world.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Data;

/**
 * 订单信息
 */
@Data
@TableName("offline_order")
public class OrderInfoDO {

  @TableId(value = "id", type = IdType.AUTO)
  private Long id;

  /**
   * 内部订单号,自己生成
   */
  private String orderNo;

  /**
   * 内部父单号
   */
  private String parentOrderNo;

  /**
   * 会员id
   */
  private String userId;

  /**
   * 平台编码,HAIDIAN,内部定义
   */
  private String thirdPlatformCode;

  /**
   * 第三方平台订单号
   */
  private String thirdOrderNo;

  /**
   * 第三方平台订单号(主)
   */
  private String parentThirdOrderNo;

  /**
   * 每日号
   */
  private String dayNum;


  /**
   * 创单时间
   */
  private Date created;

  /**
   * 支付时间
   */
  private Date payTime;

  /**
   * 完成时间
   */
  private Date completeTime;

  /**
   * 创建人
   */
  private String createdBy;

  /**
   * 更新人
   */
  private String updatedBy;

  /**
   * 创建时间
   */
  private Date createdTime;

  /**
   * 更新时间
   */
  private Date updatedTime;

  /**
   * 数据版本，每次update+1
   */
  private Long version;

  /**
   * 交易场景 online:代表线上交易 ,offline:代表线下交易
   */
  private String transactionChannel;

  /**
   * 业务类型 O2O、B2C、B2B
   */
  private String businessType;

  /**
   * 发起方所属机构编码,仅B2B场景有值
   */
  private String launchOrganizationCode;

  /**
   * 发起方所属机构名称,仅B2B场景有值
   */
  private String launchOrganizationName;

  /**
   * 发起人id，目前仅B2B有值
   */
  private String launchUserId;

  /**
   * 分公司编码
   */
  private String companyCode;

  /**
   * 分公司名称
   */
  private String companyName;

  /**
   * 所属机构编码
   */
  private String organizationCode;

  /**
   * 所属机构名称
   */
  private String organizationName;

  /**
   * 全局拦截锁,0正常  ,非0都需要拦截
   */
  private Integer lockForWorld;

  /**
   * 订单状态 创单：CREATED; 待接单：WAIT; 已接单: Accepted; 待二审审核: WAIT_VERIFY ; 待拣货: WAIT_PICK ;   已拣货:  Picked;  待发货: WAIT_DELIVERY ;  部分发货:? PART_DELIVERY ;       已发货： ALL_DELIVERY;     运输中: SHIPPED ; 待自提： WAIT_SELF_PICK;  已签收 Received; 完成：DONE ; 已取消：Canceled;
   */
  private String orderMainStatus;

  /**
   * 订单支付状态 未支付: UNPAY ; 部分支付: PART_PAY; 已支付: PAID ; 支付失败: PAY_FAIL
   */
  private String paymentStatus;

  /**
   * 订单退款进度 UN_REFUND-未退款 PART-部分退款 ALL-全部
   */
  private String orderRefundProgress;

  /**
   * 异常类型 payment_failure（支付失败）,erp_info_err（商品信息错误）,out_of_stock（缺货）,delivery_anomal (配送异常),system_integration_err(系统集成异常),order_data_err(订单数据异常)
   */
  private String abnormalType;

  /**
   * 订单类型
   */
  private String orderType;

  /**
   * 订单标记段,每3位代表一个含义详细见字典
   */
  private String orderTagSegment;

  /**
   * 来源业务线 如POS,ASSIST
   */
  private String sourceBizCode;

  /**
   * 来源场景
   */
  private String sourceScene;

  /**
   * 来源渠道,待产品定义
   */
  private String sourceChannel;

  /**
   * 来源端 如 PC,APP,POS
   */
  private String sourceDevice;

  /**
   * 商户编码
   */
  private String merCode;

  /**
   * 线上店铺编码
   */
  private String onlineStoreCode;

  /**
   * 线上店铺名称
   */
  private String onlineStoreName;

  /**
   * 处方单二审核状态  无需审核:NONE ; 待二审审核: WAIT_VERIFY ; 审核通过: REVIEW_PASS ;审核驳回: REVIEW_REJECT
   */
  private String prescriptionAuditStatus;

  /**
   * 门店直营加盟类型 DIRECT_SALES-直营 JOIN - 加盟
   */
  private String storeDirectJoinType;

  /**
   * 预约单标记 TRUE、FALSE
   */
  private String bookingFlag;

  /**
   * 仅预约单有值,预约送达开始时间
   */
  private Date bookingTimeStart;

  /**
   * 仅预约单有值,预约送达结束时间
   */
  private Date bookingTimeEnd;

  /**
   * 异常类型映射报错信息
   */
  private String abnormalMessage;

  /**
   * 买家留言
   */
  private String buyerMessage;

  /**
   * 卖家备注
   */
  private String sellerRemark;

  /**
   * 是否起效 1-起效 -1-未起效
   */
  private Long isValid;

  /**
   * 平台创建日
   */
  private String createdDay;

  /**
   * 平台更新时间
   */
  private Date updated;

  /**
   * 系统创建时间
   */
  private Date sysCreateTime;

  /**
   * 系统更新时间
   */
  private Date sysUpdateTime;

}