<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.order.atom.migration.dao.HanaMigrationMapper">


    <select id="queryUnMigrationMaxMinId" resultType="com.yxt.order.atom.repair.dto.StartEndId">
      select
      IFNULL(min(id),0) as startId,IFNULL(max(id),0) as endId
      from
      XF_TRANSSALESTOTAL_${dto.schema}
      where 1=1
      <include refid="queryOrderInfoCondition" />
    </select>

  <select id="queryUnMigrationOrder" resultType="com.yxt.order.atom.migration.dao.HanaOrderInfo">
    select
    <include refid="orderInfoConditionColumn" />
    from
    XF_TRANSSALESTOTAL_${dto.schema}
    where id <![CDATA[ >= ]]> #{startId} and id <![CDATA[ < ]]> #{endId}
    and migration = 0
    <if test="dto.migrateSort == 'ORDER'">
      <include refid="hanaOrder" />
    </if>
    <if test="dto.migrateSort == 'REFUND'">
      <include refid="hanaRefundOrder" />
    </if>
  </select>


  <sql id="hanaOrder">
    and XF_NETAMOUNT <![CDATA[ >= ]]> 0
  </sql>

  <sql id="hanaRefundOrder">
    and XF_NETAMOUNT <![CDATA[ < ]]> 0
  </sql>


  <select id="queryNeedFixedHanaOrder" resultType="com.yxt.order.atom.migration.dao.HanaOrderInfo">
    select
    <include refid="orderInfoConditionColumn" />
    from
    XF_TRANSSALESTOTAL_${schema}
    where id <![CDATA[ >= ]]> #{startId} and id <![CDATA[ <= ]]> #{endId}
    and migration in
    <foreach collection="migrationList" item="item" open="(" separator="," close=")">
      #{item,jdbcType=INTEGER}
    </foreach>
<!--    <if test="migrateSort == 'ORDER'">-->
<!--      <include refid="hanaOrder" />-->
<!--    </if>-->
<!--    <if test="migrateSort == 'REFUND'">-->
<!--      <include refid="hanaRefundOrder" />-->
<!--    </if>-->
  </select>


  <!--正单条件是>=0,同步程序逻辑修改-->
  <select id="getOrderInfoForRefund" resultType="com.yxt.order.atom.migration.dao.HanaOrderInfo">
      select
        <include refid="orderInfoConditionColumn" />
      from
        XF_TRANSSALESTOTAL_${schema}
      where
    XF_DOCNO = #{thirdOrderNo}
      and
    XF_STORECODE = #{storeCode}
    <include refid="hanaOrder" />
  </select>

  <sql id="orderInfoConditionColumn">
        id as id,
        XF_DOCNO as thirdOrderNo,
        XF_TXBATCH as orderId,
        XF_TXSERIAL as otherOrderId,
        XF_VOIDDOCNO as thirdRefundNo,
        XF_CREATETIME as createTime,
        XF_TXDATE as txDate,
        XF_TXTIME as txTime,
        XF_TILLID as posCashierDeskNo,
        XF_CASHIER as cashier,
        XF_SALESMAN as picker,
        XF_NETAMOUNT as actualPayAmount,
        XF_NETAMOUNT as actualCollectAmount,
        XF_SELLINGAMOUNT as sellingAmount,
        XF_STORECODE as storeCode,
        XF_CLIENTCODE as clientCode,
        migration as migration,
        extend_json as extendJson
  </sql>



  <sql id="queryOrderInfoCondition">
    <if test="dto.startTime != null and dto.startTime != ''">
      and XF_CREATETIME <![CDATA[ >= ]]> #{dto.startTime}
    </if>
    <if test="dto.endTime != null and dto.endTime != ''">
      and XF_CREATETIME <![CDATA[ < ]]> #{dto.endTime}
    </if>
    and migration = 0
    <if test="dto.migrateSort == 'ORDER'">
      <include refid="hanaOrder" />
    </if>
    <if test="dto.migrateSort == 'REFUND'">
      <include refid="hanaRefundOrder" />
    </if>
  </sql>

    <select id="queryOrganizationInfo" resultType="com.yxt.order.atom.migration.dao.HanaStore">
      select
        PLANT as storeCode,
        PLANT_TEXT as storeName,
        COMP_CODE as companyCode,
        ZC_GSJC as companyName,
        CASE
          WHEN _BIC_ZC_INPRO in ('ZOJM','Z003') THEN 'JOIN'
          WHEN _BIC_ZC_INPRO = 'Z001' THEN 'DIRECT_SALES'
          ELSE 'UNKNOW'
        END AS storeDirectJoinType
      from
        CV_STOREINFOR
      where
      PLANT = #{dto.storeCode}
    </select>


  <select id="queryPayInfo" resultType="com.yxt.order.atom.migration.dao.HanaOrderPay">
    select
    p.XF_TENDERCODE as payType,
    pt.DESCRIPTION as payName,
    p.XF_BASEAMOUNT as payAmount
    from
    XF_TRANSSALESTENDER_${dto.schema} p
    left join tendtyt pt on p.XF_TENDERCODE = pt.TENDERTYPECODE and pt.SPRAS = '1'
    where
    p.XF_DOCNO = #{dto.thirdOrderNo}
    and
    p.XF_STORECODE = #{dto.storeCode}
  </select>


  <select id="queryHdPayInfo" resultType="com.yxt.order.atom.migration.dao.HanaOrderPay">
    select
      tender_code as payType,
      tender_sec as payName,
      pay_amount as payAmount
    from hd_pay_info where order_id = #{orderId} and store_code = #{storeCode}
  </select>


  <select id="queryOrderItem" resultType="com.yxt.order.atom.migration.dao.HanaOrderItem">
    select
    XF_TXSERIAL as rowNo,
    XF_SALESMAN as salerId,
    XF_PLU as erpCode,
    XF_QTYSOLD as commodityCount,
    XF_ORGUPRICE as originalPrice,
    XF_AMTSOLD as totalAmount,
    XF_MARKDOWNAMT as markdownAmt,
    XF_DISCOUNTAMT as discountAmt,
    XF_PROMOTIONAMT as promotionAmt,
    XF_ITEMLOTNUM as makeNo,
    XF_PROMID1 as promotionNo1,
    XF_PROMID2 as promotionNo2,
    XF_PROMID3 as promotionNo3,
    XF_PROMID4 as promotionNo4,
    XF_PROMID5 as promotionNo5,
    XF_PROMAMT1 as promotionAmount1,
    XF_PROMAMT2 as promotionAmount2,
    XF_PROMAMT3 as promotionAmount3,
    XF_PROMAMT4 as promotionAmount4,
    XF_PROMAMT5 as promotionAmount5
    from
    XF_TRANSSALESITEM_${dto.schema}
    where
    XF_STORECODE = #{dto.storeCode}
    and XF_DOCNO = #{dto.thirdOrderNo}
    and XF_TXDATE = #{dto.txDate}
    and XF_TILLID = #{dto.posCashierDeskNo}
  </select>

  <select id="queryErpName" resultType="com.yxt.order.atom.migration.dao.HanaGoods">
    select
      MAKTX as erpName
    from
      MAKT
    WHERE
     MATNR = #{dto.erpCode}
  </select>

  <select id="queryByLoginId" resultType="com.yxt.order.atom.migration.dao.HanaHrmResource">
    select
      LASTNAME as name
    from
      hrmresource
    WHERE
    LOGINID = #{dto.loginId}
  </select>

  <!--一个主单只对应一个赠品单或者返利单,limit 1是处理脏数据的-->
  <select id="queryParentOrderNoFromDbmCoupon" resultType="java.lang.String">
    select
    XF_USEDDOCNO
    from
    xf_dbm_coupon_${schema}
    where XF_DOCNO = #{childThirdOrderNo} and XF_STORECODE = #{storeCode} limit 1
  </select>

  <select id="queryParentOrderNoFromXfZtGiftgivingMemoMap" resultType="java.lang.String">
    select
    XF_DOCNO
    from
    XF_ZT_GIFTGIVING_MEMOMAP_${schema}
    where XF_GEDOCNO = #{childThirdOrderNo} and XF_STORECODE = #{storeCode} limit 1
  </select>



  <update id="migrateResult">
    update XF_TRANSSALESTOTAL_${schema} set migration = #{migration},extend_json = #{extendJson} where id = #{id} and migration = 0
  </update>

    <select id="queryMigrationOrderById"
    resultType="com.yxt.order.atom.migration.dao.HanaOrderInfo">
      select
      <include refid="orderInfoConditionColumn" />
      from
      XF_TRANSSALESTOTAL_${schema}
      where id = #{id}
  </select>

</mapper>
