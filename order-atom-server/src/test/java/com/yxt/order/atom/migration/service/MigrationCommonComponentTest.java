package com.yxt.order.atom.migration.service;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.yxt.order.atom.migration.dao.HanaOrderItem;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import junit.framework.TestCase;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

@Slf4j
public class MigrationCommonComponentTest extends TestCase {

  @Test
  public void testGetUserId() {
    Cache<String, String> cache = CacheBuilder.newBuilder()
        .initialCapacity(1)
        .maximumSize(2)
        .build();

    cache.put("1", "1-v");
    cache.put("2", "1-v");
    cache.put("3", "3-v");

    String ifPresent = cache.getIfPresent("1");
    String ifPresent2 = cache.getIfPresent("2");
    String ifPresent3 = cache.getIfPresent("3");
    System.out.println();
  }


  @Test
  public void testGetTxDateAndTime() throws ParseException {

//    String date = "2023-2-11 0:00:00.0";
//    String date = "2023-12-01 0:00:00.0";
//    String date = "2023-12-11 0:00:00.0";
//    String date = "2023-2-7 0:00:00.0";
    String date = "2023-10-11 0:00:00.0";
    String time = "205433";
    //yyyy-MM-dd'T'HH:mm:ss

    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-M-d H:m:s.S");
    // 解析时间字符串
    LocalDateTime dateTime = LocalDateTime.parse(date, formatter);
    int year = dateTime.getYear();
    int month = dateTime.getMonthValue();
    int day = dateTime.getDayOfMonth();
    log.info("{} {} {}", year, month, day);

    String hour = time.substring(0, 2);
    String minute = time.substring(2, 4);
    String second = time.substring(4, 6);
    log.info("{} {} {}", hour, minute, second);

    System.out.println();

    // yyyy-MM-dd'T'HH:mm:ss.SSS'Z'
    SimpleDateFormat df1 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    String dateStr = String.format("%s-%s-%s %s:%s:%s", year, month, day, hour, minute, second);
    Date parse = df1.parse(dateStr);
    System.out.println(parse);
  }

  @Test
  public void testCalcPrice() {
    HanaOrderItem hanaOrderItem = new HanaOrderItem();
    hanaOrderItem.setTotalAmount(BigDecimal.valueOf(13));
    hanaOrderItem.setCommodityCount(BigDecimal.valueOf(7L));
    System.out.println(hanaOrderItem.getTotalAmount()
        .divide(hanaOrderItem.getCommodityCount(), 6, RoundingMode.DOWN));
    // 1.8571428571
    String plainString = hanaOrderItem.getTotalAmount()
        .divide(hanaOrderItem.getCommodityCount(), 10, RoundingMode.HALF_UP)
        .toPlainString();
    System.out.println(plainString);
    // 设置保留小数点后6位，并使用舍入
    // 1.857142
    System.out.println(new BigDecimal("1.8571428571").setScale(6, RoundingMode.DOWN));
    System.out.println(new BigDecimal("1.8571429571").setScale(6, RoundingMode.DOWN));
    System.out.println(new BigDecimal("1.8571425571").setScale(6, RoundingMode.DOWN));
    System.out.println(new BigDecimal("1.8571423571").setScale(6, RoundingMode.DOWN));
    System.out.println(new BigDecimal("1.8571420571").setScale(6, RoundingMode.DOWN));
    System.out.println(new BigDecimal("-1.8571428571").setScale(6, RoundingMode.DOWN));
    System.out.println(new BigDecimal("-1.8571429571").setScale(6, RoundingMode.DOWN));
    System.out.println(new BigDecimal("-1.8571425571").setScale(6, RoundingMode.DOWN));
    System.out.println(new BigDecimal("-1.8571423571").setScale(6, RoundingMode.DOWN));
    System.out.println(new BigDecimal("-1.8571420571").setScale(6, RoundingMode.DOWN));
  }

  @Test
  public void testCalcPrice2() {
    HanaOrderItem hanaOrderItem = new HanaOrderItem();
    hanaOrderItem.setTotalAmount(BigDecimal.valueOf(13));
    hanaOrderItem.setCommodityCount(BigDecimal.valueOf(0.02));
    System.out.println();
  }


}